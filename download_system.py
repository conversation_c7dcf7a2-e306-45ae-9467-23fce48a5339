import hashlib
import secrets
import time
import os
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class SecureDownloadSystem:
    def __init__(self, bot, db):
        self.bot = bot
        self.db = db
        
        # إعدادات الأمان
        self.MAX_DAILY_DOWNLOADS = 3
        self.MAX_MONTHLY_DOWNLOADS = 50
        self.LINK_EXPIRY_MINUTES = 30
        self.DEVICE_LOCK_ENABLED = True
        
        # إنشاء الجداول إذا لم تكن موجودة
        self.db.create_download_tables()
    
    def generate_device_fingerprint(self, user_agent, ip_address, user_id):
        """إنشاء بصمة فريدة للجهاز"""
        try:
            # دمج معلومات الجهاز لإنشاء بصمة فريدة
            fingerprint_data = f"{user_agent}:{ip_address}:{user_id}:{int(time.time() // 86400)}"
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]
            return fingerprint
        except Exception as e:
            logger.error(f"Failed to generate device fingerprint: {e}")
            return None
    
    def create_secure_download_link(self, user_id, file_id, device_fingerprint, ip_address, user_agent):
        """إنشاء رابط تحميل آمن ومؤقت"""
        try:
            # التحقق من صحة الاشتراك
            if not self._is_user_subscribed(user_id):
                return {
                    'success': False,
                    'message': '❌ يجب أن تكون مشترك للوصول للتحميلات'
                }
            
            # التحقق من حدود التحميل
            limits_check = self._check_download_limits(user_id, device_fingerprint)
            if not limits_check['allowed']:
                return {
                    'success': False,
                    'message': limits_check['message']
                }
            
            # إنشاء رمز تحميل فريد
            download_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(minutes=self.LINK_EXPIRY_MINUTES)
            
            # حفظ رابط التحميل في قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO download_links 
                    (user_id, file_id, download_token, device_fingerprint, ip_address, user_agent, expires_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (user_id, file_id, download_token, device_fingerprint, ip_address, user_agent, expires_at))
            
            # تسجيل النشاط
            self.db.log_activity(user_id, "download_link_created", f"Created download link for file {file_id}")
            
            return {
                'success': True,
                'download_token': download_token,
                'expires_at': expires_at,
                'expires_in_minutes': self.LINK_EXPIRY_MINUTES
            }
            
        except Exception as e:
            logger.error(f"Failed to create secure download link: {e}")
            return {
                'success': False,
                'message': '❌ حدث خطأ في إنشاء رابط التحميل'
            }
    
    def validate_download_request(self, download_token, device_fingerprint, ip_address, user_agent):
        """التحقق من صحة طلب التحميل"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT dl.*, df.file_name, df.file_path, df.file_size
                    FROM download_links dl
                    JOIN download_files df ON dl.file_id = df.id
                    WHERE dl.download_token = %s
                    AND dl.expires_at > NOW()
                    AND dl.is_used = FALSE
                    AND df.is_active = TRUE
                """, (download_token,))
                
                link_data = cursor.fetchone()
                
                if not link_data:
                    return {
                        'valid': False,
                        'message': '❌ رابط التحميل غير صحيح أو منتهي الصلاحية'
                    }
                
                # التحقق من بصمة الجهاز
                if self.DEVICE_LOCK_ENABLED and link_data['device_fingerprint'] != device_fingerprint:
                    # تسجيل محاولة وصول مشبوهة
                    self.db.log_activity(
                        link_data['user_id'], 
                        "suspicious_download_attempt", 
                        f"Device fingerprint mismatch for token {download_token}"
                    )
                    return {
                        'valid': False,
                        'message': '🚫 هذا الرابط مخصص لجهاز آخر'
                    }
                
                # التحقق من أن المستخدم ما زال مشترك
                if not self._is_user_subscribed(link_data['user_id']):
                    return {
                        'valid': False,
                        'message': '❌ انتهت صلاحية اشتراكك'
                    }
                
                return {
                    'valid': True,
                    'link_data': link_data
                }
                
        except Exception as e:
            logger.error(f"Failed to validate download request: {e}")
            return {
                'valid': False,
                'message': '❌ حدث خطأ في التحقق من الرابط'
            }
    
    def mark_download_used(self, download_token, user_id, bytes_downloaded=0, download_time=0, status='completed'):
        """تسجيل استخدام رابط التحميل"""
        try:
            with self.db.get_db_cursor() as cursor:
                # تحديث حالة الرابط
                cursor.execute(f"""
                    UPDATE download_links 
                    SET is_used = TRUE, used_at = NOW()
                    WHERE download_token = %s
                """, (download_token,))
                
                # إضافة سجل في تاريخ التحميلات
                cursor.execute(f"""
                    INSERT INTO download_history 
                    (user_id, file_id, download_token, download_status, bytes_downloaded, download_time, completed_at)
                    SELECT user_id, file_id, download_token, %s, %s, %s, NOW()
                    FROM download_links 
                    WHERE download_token = %s
                """, (status, bytes_downloaded, download_time, download_token))
                
                # تحديث حدود التحميل
                self._update_download_limits(user_id)
                
            logger.info(f"Download marked as used: {download_token}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to mark download as used: {e}")
            return False
    
    def _is_user_subscribed(self, user_id):
        """التحقق من صحة اشتراك المستخدم"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT is_active, expire_time 
                    FROM {self.db.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))
                
                subscriber = cursor.fetchone()
                
                if not subscriber:
                    return False
                
                if not subscriber['is_active']:
                    return False
                
                if subscriber['expire_time'] <= int(datetime.now().timestamp()):
                    return False
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to check subscription status: {e}")
            return False
    
    def _check_download_limits(self, user_id, device_fingerprint):
        """التحقق من حدود التحميل"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM download_limits 
                    WHERE user_id = %s
                """, (user_id,))
                
                limits = cursor.fetchone()
                today = datetime.now().date()
                
                if not limits:
                    # إنشاء سجل جديد للمستخدم
                    cursor.execute(f"""
                        INSERT INTO download_limits 
                        (user_id, device_fingerprint, last_reset_date)
                        VALUES (%s, %s, %s)
                    """, (user_id, device_fingerprint, today))
                    return {'allowed': True}
                
                # التحقق من قفل الجهاز
                if limits['is_device_locked'] and limits['device_fingerprint'] != device_fingerprint:
                    return {
                        'allowed': False,
                        'message': '🔒 حسابك مقفل على جهاز آخر. تواصل مع الدعم للمساعدة.'
                    }
                
                # إعادة تعيين العدادات إذا لزم الأمر
                if limits['last_download_date'] != today:
                    cursor.execute(f"""
                        UPDATE download_limits 
                        SET daily_downloads = 0, last_reset_date = %s
                        WHERE user_id = %s
                    """, (today, user_id))
                    limits['daily_downloads'] = 0
                
                # التحقق من الحدود اليومية
                if limits['daily_downloads'] >= self.MAX_DAILY_DOWNLOADS:
                    return {
                        'allowed': False,
                        'message': f'⏰ وصلت للحد الأقصى اليومي ({self.MAX_DAILY_DOWNLOADS} تحميلات). حاول غداً.'
                    }
                
                # التحقق من الحدود الشهرية
                if limits['monthly_downloads'] >= self.MAX_MONTHLY_DOWNLOADS:
                    return {
                        'allowed': False,
                        'message': f'📊 وصلت للحد الأقصى الشهري ({self.MAX_MONTHLY_DOWNLOADS} تحميل).'
                    }
                
                return {'allowed': True}
                
        except Exception as e:
            logger.error(f"Failed to check download limits: {e}")
            return {
                'allowed': False,
                'message': '❌ حدث خطأ في التحقق من حدود التحميل'
            }
    
    def _update_download_limits(self, user_id):
        """تحديث عدادات التحميل"""
        try:
            today = datetime.now().date()
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE download_limits 
                    SET daily_downloads = daily_downloads + 1,
                        monthly_downloads = monthly_downloads + 1,
                        total_downloads = total_downloads + 1,
                        last_download_date = %s
                    WHERE user_id = %s
                """, (today, user_id))
                
        except Exception as e:
            logger.error(f"Failed to update download limits: {e}")
