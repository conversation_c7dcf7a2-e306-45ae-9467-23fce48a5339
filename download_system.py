import hashlib
import secrets
import time
import os
from datetime import datetime, timedelta
import logging
from telebot import types

logger = logging.getLogger(__name__)

class SecureDownloadSystem:
    def __init__(self, bot, db):
        self.bot = bot
        self.db = db

        # إعدادات الأمان
        self.LINK_EXPIRY_MINUTES = 15  # مدة صلاحية الرابط
        self.DEVICE_LOCK_ENABLED = True  # تفعيل قفل الجهاز

        # إنشاء الجداول إذا لم تكن موجودة
        self.db.create_download_tables()

    def generate_device_fingerprint(self, user_agent, ip_address, user_id):
        """إنشاء بصمة فريدة للجهاز"""
        try:
            # دمج معلومات الجهاز لإنشاء بصمة فريدة
            fingerprint_data = f"{user_agent}:{ip_address}:{user_id}:{int(time.time() // 86400)}"
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]
            return fingerprint
        except Exception as e:
            logger.error(f"Failed to generate device fingerprint: {e}")
            return None

    def create_secure_download_link(self, user_id, file_id, device_fingerprint, ip_address, user_agent):
        """إنشاء رابط تحميل آمن ومؤقت"""
        try:
            # التحقق من صحة الاشتراك
            if not self._is_user_subscribed(user_id):
                return {
                    'success': False,
                    'message': '❌ يجب أن تكون مشترك للوصول للتحميلات'
                }

            # التحقق من حدود التحميل
            limits_check = self._check_download_limits(user_id, device_fingerprint)
            if not limits_check['allowed']:
                return {
                    'success': False,
                    'message': limits_check['message']
                }

            # إنشاء رمز تحميل فريد
            download_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(minutes=self.LINK_EXPIRY_MINUTES)

            # حفظ رابط التحميل في قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO download_links
                    (user_id, file_id, download_token, device_fingerprint, ip_address, user_agent, expires_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (user_id, file_id, download_token, device_fingerprint, ip_address, user_agent, expires_at))

            # تسجيل النشاط
            self.db.log_activity(user_id, "download_link_created", f"Created download link for file {file_id}")

            return {
                'success': True,
                'download_token': download_token,
                'expires_at': expires_at,
                'expires_in_minutes': self.LINK_EXPIRY_MINUTES
            }

        except Exception as e:
            logger.error(f"Failed to create secure download link: {e}")
            return {
                'success': False,
                'message': '❌ حدث خطأ في إنشاء رابط التحميل'
            }

    def validate_download_request(self, download_token, device_fingerprint, ip_address, user_agent):
        """التحقق من صحة طلب التحميل"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT dl.*, df.file_name, df.file_path, df.file_size
                    FROM download_links dl
                    JOIN download_files df ON dl.file_id = df.id
                    WHERE dl.download_token = %s
                    AND dl.expires_at > NOW()
                    AND dl.is_used = FALSE
                    AND df.is_active = TRUE
                """, (download_token,))

                link_data = cursor.fetchone()

                if not link_data:
                    return {
                        'valid': False,
                        'message': '❌ رابط التحميل غير صحيح أو منتهي الصلاحية'
                    }

                # التحقق من بصمة الجهاز
                if self.DEVICE_LOCK_ENABLED and link_data['device_fingerprint'] != device_fingerprint:
                    # تسجيل محاولة وصول مشبوهة
                    self.db.log_activity(
                        link_data['user_id'],
                        "suspicious_download_attempt",
                        f"Device fingerprint mismatch for token {download_token}"
                    )
                    return {
                        'valid': False,
                        'message': '🚫 هذا الرابط مخصص لجهاز آخر'
                    }

                # التحقق من أن المستخدم ما زال مشترك
                if not self._is_user_subscribed(link_data['user_id']):
                    return {
                        'valid': False,
                        'message': '❌ انتهت صلاحية اشتراكك'
                    }

                return {
                    'valid': True,
                    'link_data': link_data
                }

        except Exception as e:
            logger.error(f"Failed to validate download request: {e}")
            return {
                'valid': False,
                'message': '❌ حدث خطأ في التحقق من الرابط'
            }

    def mark_download_used(self, download_token, user_id, bytes_downloaded=0, download_time=0, status='completed'):
        """تسجيل استخدام رابط التحميل"""
        try:
            with self.db.get_db_cursor() as cursor:
                # تحديث حالة الرابط
                cursor.execute(f"""
                    UPDATE download_links
                    SET is_used = TRUE, used_at = NOW()
                    WHERE download_token = %s
                """, (download_token,))

                # إضافة سجل في تاريخ التحميلات
                cursor.execute(f"""
                    INSERT INTO download_history
                    (user_id, file_id, download_token, download_status, bytes_downloaded, download_time, completed_at)
                    SELECT user_id, file_id, download_token, %s, %s, %s, NOW()
                    FROM download_links
                    WHERE download_token = %s
                """, (status, bytes_downloaded, download_time, download_token))

                # تحديث حدود التحميل
                self._update_download_limits(user_id)

            logger.info(f"Download marked as used: {download_token}")
            return True

        except Exception as e:
            logger.error(f"Failed to mark download as used: {e}")
            return False

    def _is_user_subscribed(self, user_id):
        """التحقق من صحة اشتراك المستخدم"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT is_active, expire_time
                    FROM {self.db.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    return False

                if not subscriber['is_active']:
                    return False

                if subscriber['expire_time'] <= int(datetime.now().timestamp()):
                    return False

                return True

        except Exception as e:
            logger.error(f"Failed to check subscription status: {e}")
            return False

    def _check_download_limits(self, user_id, device_fingerprint):
        """التحقق من حدود التحميل"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM download_limits
                    WHERE user_id = %s
                """, (user_id,))

                limits = cursor.fetchone()
                today = datetime.now().date()

                if not limits:
                    # إنشاء سجل جديد للمستخدم
                    cursor.execute(f"""
                        INSERT INTO download_limits
                        (user_id, device_fingerprint, last_reset_date)
                        VALUES (%s, %s, %s)
                    """, (user_id, device_fingerprint, today))
                    return {'allowed': True}

                # التحقق من قفل الجهاز
                if limits['is_device_locked'] and limits['device_fingerprint'] != device_fingerprint:
                    return {
                        'allowed': False,
                        'message': '🔒 حسابك مقفل على جهاز آخر. تواصل مع الدعم للمساعدة.'
                    }

                # إعادة تعيين العدادات إذا لزم الأمر
                if limits['last_download_date'] != today:
                    cursor.execute(f"""
                        UPDATE download_limits
                        SET daily_downloads = 0, last_reset_date = %s
                        WHERE user_id = %s
                    """, (today, user_id))
                    limits['daily_downloads'] = 0

                # التحقق من الحدود اليومية
                if limits['daily_downloads'] >= self.MAX_DAILY_DOWNLOADS:
                    return {
                        'allowed': False,
                        'message': f'⏰ وصلت للحد الأقصى اليومي ({self.MAX_DAILY_DOWNLOADS} تحميلات). حاول غداً.'
                    }

                # التحقق من الحدود الشهرية
                if limits['monthly_downloads'] >= self.MAX_MONTHLY_DOWNLOADS:
                    return {
                        'allowed': False,
                        'message': f'📊 وصلت للحد الأقصى الشهري ({self.MAX_MONTHLY_DOWNLOADS} تحميل).'
                    }

                return {'allowed': True}

        except Exception as e:
            logger.error(f"Failed to check download limits: {e}")
            return {
                'allowed': False,
                'message': '❌ حدث خطأ في التحقق من حدود التحميل'
            }

    def _update_download_limits(self, user_id):
        """تحديث عدادات التحميل"""
        try:
            today = datetime.now().date()
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE download_limits
                    SET daily_downloads = daily_downloads + 1,
                        monthly_downloads = monthly_downloads + 1,
                        total_downloads = total_downloads + 1,
                        last_download_date = %s
                    WHERE user_id = %s
                """, (today, user_id))

        except Exception as e:
            logger.error(f"Failed to update download limits: {e}")

    def show_download_menu(self, user_id):
        """عرض قائمة التحميلات للمشترك"""
        try:
            # التحقق من صحة الاشتراك
            if not self._is_user_subscribed(user_id):
                from telebot import types
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                    types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
                )

                self.bot.send_message(
                    user_id,
                    "🚫 **يجب أن تكون مشترك للوصول للتحميلات**\n\n"
                    "💎 اشترك الآن للحصول على:\n"
                    "• ملفات PUBG Mobile Portable\n"
                    "• أدوات وإضافات حصرية\n"
                    "• تحديثات مستمرة\n"
                    "• دعم فني متميز",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return

            # الحصول على الملفات المتاحة
            available_files = self._get_available_files()
            user_downloads = self._get_user_download_history(user_id)

            message = "🎮 **ملفات PUBG Mobile Portable**\n\n"
            message += "📁 **الملفات المتاحة:**\n\n"

            from telebot import types
            markup = types.InlineKeyboardMarkup(row_width=1)

            for file_info in available_files:
                file_id = file_info['id']
                file_name = file_info['file_name']
                file_size = self._format_file_size(file_info['file_size'])

                # التحقق من حالة التحميل
                is_downloaded = any(d['file_id'] == file_id and d['download_status'] == 'completed'
                                  for d in user_downloads)

                if is_downloaded:
                    status = "✅ تم التحميل"
                    button_text = f"✅ {file_name} ({file_size})"
                    callback_data = f"file_downloaded_{file_id}"
                else:
                    status = "📥 متاح للتحميل"
                    button_text = f"📥 {file_name} ({file_size})"
                    callback_data = f"download_file_{file_id}"

                message += f"📄 **{file_name}**\n"
                message += f"📊 الحجم: {file_size}\n"
                message += f"🔄 الحالة: {status}\n"
                if file_info.get('description'):
                    message += f"📝 الوصف: {file_info['description']}\n"
                message += "\n"

                markup.add(types.InlineKeyboardButton(button_text, callback_data=callback_data))

            # إضافة معلومات الأمان
            message += "🛡️ **معلومات الأمان:**\n"
            message += "• كل ملف يمكن تحميله مرة واحدة فقط\n"
            message += f"• روابط التحميل مؤقتة ({self.LINK_EXPIRY_MINUTES} دقيقة)\n"
            message += "• مرتبطة بجهازك فقط\n"
            message += "• محمية من المشاركة\n\n"

            # إحصائيات المستخدم
            completed_downloads = len([d for d in user_downloads if d['download_status'] == 'completed'])
            total_files = len(available_files)

            message += f"📊 **إحصائياتك:**\n"
            message += f"✅ تم تحميل: {completed_downloads}/{total_files} ملف\n"
            message += f"📁 متبقي: {total_files - completed_downloads} ملف"

            markup.add(
                types.InlineKeyboardButton("📊 سجل التحميلات", callback_data="download_history"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            self.bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(user_id, "download_menu_viewed")

        except Exception as e:
            logger.error(f"Failed to show download menu: {e}")
            self.bot.send_message(user_id, "❌ حدث خطأ في عرض قائمة التحميلات")

    def handle_file_download_request(self, user_id, file_id):
        """معالجة طلب تحميل ملف"""
        try:
            # التحقق من صحة الاشتراك
            if not self._is_user_subscribed(user_id):
                self.bot.send_message(user_id, "❌ انتهت صلاحية اشتراكك")
                return

            # التحقق من أن الملف لم يتم تحميله من قبل
            if self._is_file_already_downloaded(user_id, file_id):
                from telebot import types
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 قائمة التحميلات", callback_data="download_pubg"))

                self.bot.send_message(
                    user_id,
                    "⚠️ **تم تحميل هذا الملف من قبل**\n\n"
                    "🔒 لأسباب أمنية، كل ملف يمكن تحميله مرة واحدة فقط\n"
                    "💡 إذا كنت تحتاج لإعادة التحميل، تواصل مع الدعم",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return

            # الحصول على معلومات الملف
            file_info = self._get_file_info(file_id)
            if not file_info:
                self.bot.send_message(user_id, "❌ الملف غير موجود")
                return

            # إنشاء رابط تحميل آمن
            download_result = self._create_secure_download_link(user_id, file_id)

            if download_result['success']:
                file_size = self._format_file_size(file_info['file_size'])

                message = f"🔐 **رابط التحميل الآمن**\n\n"
                message += f"📄 **الملف:** {file_info['file_name']}\n"
                message += f"📊 **الحجم:** {file_size}\n"
                message += f"⏰ **صالح لمدة:** {self.LINK_EXPIRY_MINUTES} دقيقة\n"
                message += f"🔒 **مرتبط بجهازك فقط**\n\n"
                message += f"⚠️ **تحذير مهم:**\n"
                message += f"• هذا الرابط يعمل مرة واحدة فقط\n"
                message += f"• لا تشارك الرابط مع أي شخص\n"
                message += f"• ابدأ التحميل فوراً\n"
                message += f"• الرابط سينتهي خلال {self.LINK_EXPIRY_MINUTES} دقيقة"

                from telebot import types
                markup = types.InlineKeyboardMarkup()
                # رابط التحميل المباشر (تجريبي - يحتاج خادم ويب حقيقي)
                download_url = f"https://example.com/download/{download_result['download_token']}"
                markup.add(types.InlineKeyboardButton("📥 تحميل الملف", url=download_url))
                markup.add(types.InlineKeyboardButton("🔙 قائمة التحميلات", callback_data="download_pubg"))

                self.bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

                # تسجيل النشاط
                self.db.log_activity(user_id, "download_link_created", f"File: {file_info['file_name']}")

            else:
                self.bot.send_message(user_id, f"❌ {download_result['message']}")

        except Exception as e:
            logger.error(f"Failed to handle download request: {e}")
            self.bot.send_message(user_id, "❌ حدث خطأ في إنشاء رابط التحميل")

    def show_download_history(self, user_id):
        """عرض سجل التحميلات للمستخدم"""
        try:
            downloads = self._get_user_download_history(user_id)

            if not downloads:
                from telebot import types
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 قائمة التحميلات", callback_data="download_pubg"))

                self.bot.send_message(
                    user_id,
                    "📊 **سجل التحميلات**\n\n"
                    "📁 لم تقم بأي تحميلات بعد\n"
                    "💡 ابدأ بتحميل ملفات PUBG Mobile الآن!",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return

            message = "📊 **سجل التحميلات**\n\n"

            completed = [d for d in downloads if d['download_status'] == 'completed']
            failed = [d for d in downloads if d['download_status'] == 'failed']

            message += f"✅ **التحميلات المكتملة:** {len(completed)}\n"
            message += f"❌ **التحميلات الفاشلة:** {len(failed)}\n\n"

            # عرض آخر 10 تحميلات
            recent_downloads = sorted(downloads, key=lambda x: x['created_at'], reverse=True)[:10]

            message += "📋 **آخر التحميلات:**\n\n"

            for download in recent_downloads:
                file_name = download.get('file_name', 'ملف غير معروف')
                status_icon = "✅" if download['download_status'] == 'completed' else "❌"
                download_date = download['created_at'].strftime('%Y-%m-%d %H:%M')

                message += f"{status_icon} **{file_name}**\n"
                message += f"📅 {download_date}\n"

                if download['download_status'] == 'completed' and download.get('download_time'):
                    message += f"⏱️ وقت التحميل: {download['download_time']:.1f} ثانية\n"

                message += "\n"

            from telebot import types
            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 قائمة التحميلات", callback_data="download_pubg"))

            self.bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show download history: {e}")
            self.bot.send_message(user_id, "❌ حدث خطأ في عرض سجل التحميلات")

    def _is_user_subscribed(self, user_id):
        """التحقق من صحة اشتراك المستخدم"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT is_active, expire_time
                    FROM {self.db.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber or not subscriber['is_active']:
                    return False

                if subscriber['expire_time'] <= int(datetime.now().timestamp()):
                    return False

                return True

        except Exception as e:
            logger.error(f"Failed to check subscription status: {e}")
            return False

    def _get_available_files(self):
        """الحصول على قائمة الملفات المتاحة"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM download_files
                    WHERE is_active = TRUE
                    ORDER BY created_at DESC
                """)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get available files: {e}")
            return []

    def _get_user_download_history(self, user_id):
        """الحصول على سجل تحميلات المستخدم"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT dh.*, df.file_name
                    FROM download_history dh
                    LEFT JOIN download_files df ON dh.file_id = df.id
                    WHERE dh.user_id = %s
                    ORDER BY dh.created_at DESC
                """, (user_id,))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get user download history: {e}")
            return []

    def _is_file_already_downloaded(self, user_id, file_id):
        """التحقق من أن الملف تم تحميله من قبل"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) as count FROM download_history
                    WHERE user_id = %s AND file_id = %s AND download_status = 'completed'
                """, (user_id, file_id))

                result = cursor.fetchone()
                return result['count'] > 0
        except Exception as e:
            logger.error(f"Failed to check if file already downloaded: {e}")
            return False

    def _get_file_info(self, file_id):
        """الحصول على معلومات الملف"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM download_files
                    WHERE id = %s AND is_active = TRUE
                """, (file_id,))
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get file info: {e}")
            return None

    def _create_secure_download_link(self, user_id, file_id):
        """إنشاء رابط تحميل آمن"""
        try:
            # إنشاء رمز تحميل فريد
            download_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(minutes=self.LINK_EXPIRY_MINUTES)

            # بصمة الجهاز (مبسطة للمثال)
            device_fingerprint = hashlib.sha256(f"{user_id}:{int(time.time() // 86400)}".encode()).hexdigest()[:32]

            # حفظ رابط التحميل
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO download_links
                    (user_id, file_id, download_token, device_fingerprint, expires_at)
                    VALUES (%s, %s, %s, %s, %s)
                """, (user_id, file_id, download_token, device_fingerprint, expires_at))

            return {
                'success': True,
                'download_token': download_token,
                'expires_at': expires_at
            }

        except Exception as e:
            logger.error(f"Failed to create secure download link: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في إنشاء رابط التحميل'
            }

    def _format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"
