"""
وظائف مساعدة للنظام
System Utility Functions
"""
import logging
import os
import json
from datetime import datetime, timedelta
from functools import wraps
import time
import hashlib
import secrets
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class RateLimiter:
    """نظام تحديد معدل الطلبات لمنع الإساءة"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = {}
    
    def is_allowed(self, user_id: int) -> bool:
        """التحقق من السماح للمستخدم بإرسال طلب"""
        now = time.time()
        
        # تنظيف الطلبات القديمة
        if user_id in self.requests:
            self.requests[user_id] = [
                req_time for req_time in self.requests[user_id]
                if now - req_time < self.time_window
            ]
        else:
            self.requests[user_id] = []
        
        # التحقق من عدد الطلبات
        if len(self.requests[user_id]) >= self.max_requests:
            return False
        
        # إضافة الطلب الحالي
        self.requests[user_id].append(now)
        return True

class SecurityManager:
    """مدير الأمان للنظام"""
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """إنشاء رمز آمن عشوائي"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_data(data: str) -> str:
        """تشفير البيانات باستخدام SHA-256"""
        return hashlib.sha256(data.encode()).hexdigest()
    
    @staticmethod
    def validate_user_input(text: str, max_length: int = 1000) -> bool:
        """التحقق من صحة مدخلات المستخدم"""
        if not text or len(text) > max_length:
            return False
        
        # منع الأكواد الضارة
        dangerous_patterns = ['<script', 'javascript:', 'data:', 'vbscript:']
        text_lower = text.lower()
        
        for pattern in dangerous_patterns:
            if pattern in text_lower:
                return False
        
        return True

class DateTimeHelper:
    """مساعد التاريخ والوقت"""
    
    @staticmethod
    def timestamp_to_datetime(timestamp: int) -> datetime:
        """تحويل timestamp إلى datetime"""
        return datetime.fromtimestamp(timestamp)
    
    @staticmethod
    def datetime_to_timestamp(dt: datetime) -> int:
        """تحويل datetime إلى timestamp"""
        return int(dt.timestamp())
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M") -> str:
        """تنسيق التاريخ والوقت"""
        return dt.strftime(format_str)
    
    @staticmethod
    def get_days_difference(timestamp1: int, timestamp2: int) -> int:
        """حساب الفرق بالأيام بين تاريخين"""
        dt1 = datetime.fromtimestamp(timestamp1)
        dt2 = datetime.fromtimestamp(timestamp2)
        return abs((dt2 - dt1).days)
    
    @staticmethod
    def add_days_to_timestamp(timestamp: int, days: int) -> int:
        """إضافة أيام إلى timestamp"""
        dt = datetime.fromtimestamp(timestamp)
        new_dt = dt + timedelta(days=days)
        return int(new_dt.timestamp())

class MessageFormatter:
    """منسق الرسائل"""
    
    @staticmethod
    def format_currency(amount: float, currency: str = "جنيه") -> str:
        """تنسيق المبلغ المالي"""
        return f"{amount:.2f} {currency}"
    
    @staticmethod
    def format_user_info(user_data: Dict) -> str:
        """تنسيق معلومات المستخدم"""
        username = user_data.get('username', 'غير محدد')
        first_name = user_data.get('first_name', 'غير محدد')
        last_name = user_data.get('last_name', '')
        
        full_name = f"{first_name} {last_name}".strip()
        
        return f"👤 {full_name}\n📝 @{username}\n🆔 {user_data.get('user_id', 'غير محدد')}"
    
    @staticmethod
    def format_subscription_status(subscriber: Dict) -> str:
        """تنسيق حالة الاشتراك"""
        if not subscriber:
            return "❌ غير مشترك"
        
        now = datetime.now().timestamp()
        expire_time = subscriber['expire_time']
        
        if expire_time > now:
            days_left = int((expire_time - now) / 86400)
            return f"✅ نشط - متبقي {days_left} {'يوم' if days_left == 1 else 'أيام'}"
        else:
            return "❌ منتهي الصلاحية"
    
    @staticmethod
    def format_payment_status(status: str) -> str:
        """تنسيق حالة الدفع"""
        status_map = {
            'pending': '⏳ قيد المراجعة',
            'approved': '✅ مقبولة',
            'rejected': '❌ مرفوضة'
        }
        return status_map.get(status, '❓ غير معروف')

class FileManager:
    """مدير الملفات"""
    
    @staticmethod
    def ensure_directory_exists(directory: str) -> bool:
        """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {directory}: {e}")
            return False
    
    @staticmethod
    def save_json_file(data: Dict, filepath: str) -> bool:
        """حفظ البيانات في ملف JSON"""
        try:
            directory = os.path.dirname(filepath)
            if directory:
                FileManager.ensure_directory_exists(directory)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to save JSON file {filepath}: {e}")
            return False
    
    @staticmethod
    def load_json_file(filepath: str) -> Optional[Dict]:
        """تحميل البيانات من ملف JSON"""
        try:
            if not os.path.exists(filepath):
                return None
            
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load JSON file {filepath}: {e}")
            return None

class LogManager:
    """مدير السجلات"""
    
    @staticmethod
    def setup_logging(log_level: str = "INFO", log_file: str = "bot.log") -> None:
        """إعداد نظام السجلات"""
        # إنشاء مجلد السجلات
        log_dir = "logs"
        FileManager.ensure_directory_exists(log_dir)
        
        log_filepath = os.path.join(log_dir, log_file)
        
        # إعداد التنسيق
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # إعداد معالج الملف
        file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
        file_handler.setFormatter(formatter)
        
        # إعداد معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # إعداد المسجل الرئيسي
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        logger.info("Logging system initialized")

def rate_limit(max_requests: int = 10, time_window: int = 60):
    """ديكوريتر لتحديد معدل الطلبات"""
    limiter = RateLimiter(max_requests, time_window)
    
    def decorator(func):
        @wraps(func)
        def wrapper(message, *args, **kwargs):
            user_id = message.from_user.id if hasattr(message, 'from_user') else message.chat.id
            
            if not limiter.is_allowed(user_id):
                # إرسال رسالة تحذير
                try:
                    bot = kwargs.get('bot') or args[0] if args else None
                    if bot and hasattr(bot, 'send_message'):
                        bot.send_message(
                            user_id,
                            "⚠️ تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً."
                        )
                except:
                    pass
                return
            
            return func(message, *args, **kwargs)
        return wrapper
    return decorator

def admin_required(func):
    """ديكوريتر للتحقق من صلاحيات الإدارة"""
    @wraps(func)
    def wrapper(message, *args, **kwargs):
        from config import Config
        config = Config()
        
        user_id = message.from_user.id if hasattr(message, 'from_user') else message.chat.id
        
        if user_id != config.ADMIN_ID:
            try:
                bot = kwargs.get('bot') or args[0] if args else None
                if bot and hasattr(bot, 'send_message'):
                    bot.send_message(user_id, "❌ ليس لديك صلاحية للوصول لهذه الوظيفة")
            except:
                pass
            return
        
        return func(message, *args, **kwargs)
    return wrapper

def log_user_activity(action: str):
    """ديكوريتر لتسجيل نشاط المستخدم"""
    def decorator(func):
        @wraps(func)
        def wrapper(message, *args, **kwargs):
            try:
                from database import DatabaseManager
                db = DatabaseManager()
                
                user_id = message.from_user.id if hasattr(message, 'from_user') else message.chat.id
                username = getattr(message.from_user, 'username', None) if hasattr(message, 'from_user') else None
                
                details = f"Action: {action}, Username: {username}"
                db.log_activity(user_id, action, details)
                
            except Exception as e:
                logger.error(f"Failed to log user activity: {e}")
            
            return func(message, *args, **kwargs)
        return wrapper
    return decorator

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    @staticmethod
    def create_database_backup() -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            from database import DatabaseManager
            from config import Config
            
            db = DatabaseManager()
            config = Config()
            
            # إنشاء مجلد النسخ الاحتياطية
            backup_dir = "backups"
            FileManager.ensure_directory_exists(backup_dir)
            
            # اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"backup_{timestamp}.sql")
            
            # تنفيذ النسخ الاحتياطي
            import subprocess
            
            cmd = [
                "pg_dump",
                f"--host={config.DB_HOST}",
                f"--port={config.DB_PORT}",
                f"--username={config.DB_USER}",
                f"--dbname={config.DB_NAME}",
                f"--file={backup_file}",
                "--verbose"
            ]
            
            env = os.environ.copy()
            env['PGPASSWORD'] = config.DB_PASS
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Database backup created successfully: {backup_file}")
                return True
            else:
                logger.error(f"Database backup failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to create database backup: {e}")
            return False

class HealthChecker:
    """فاحص صحة النظام"""
    
    @staticmethod
    def check_database_connection() -> bool:
        """فحص اتصال قاعدة البيانات"""
        try:
            from database import DatabaseManager
            db = DatabaseManager()
            
            with db.get_db_cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
                
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False
    
    @staticmethod
    def check_bot_connection(bot) -> bool:
        """فحص اتصال البوت"""
        try:
            bot.get_me()
            return True
        except Exception as e:
            logger.error(f"Bot connection check failed: {e}")
            return False
    
    @staticmethod
    def get_system_status() -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        return {
            'timestamp': datetime.now().isoformat(),
            'database_connected': HealthChecker.check_database_connection(),
            'uptime': time.time() - getattr(HealthChecker, '_start_time', time.time())
        }

# تسجيل وقت بدء النظام
HealthChecker._start_time = time.time()
