"""
لوحة تحكم الإدارة المتقدمة
Advanced Admin Control Panel
"""
import logging
from datetime import datetime, timedelta
import json
import csv
import io
import telebot  # type: ignore
from telebot import types  # type: ignore
from database import DatabaseManager
from config import Config

logger = logging.getLogger(__name__)

class AdminPanel:
    def __init__(self, bot: telebot.TeleBot):
        self.bot = bot
        self.db = DatabaseManager()
        self.config = Config()

        # أسماء الجداول مع البادئة
        self.subscribers_table = self.db.subscribers_table
        self.payments_table = self.db.payments_table

    def is_admin(self, user_id):
        """التحقق من صلاحيات الإدارة"""
        return user_id == self.config.ADMIN_ID

    def show_admin_menu(self, chat_id):
        """عرض القائمة الرئيسية للإدارة"""
        if not self.is_admin(chat_id):
            return False

        markup = types.InlineKeyboardMarkup(row_width=2)

        # الصف الأول - الإحصائيات والتقارير
        markup.add(
            types.InlineKeyboardButton("📊 الإحصائيات", callback_data="admin_stats"),
            types.InlineKeyboardButton("📈 التقارير", callback_data="admin_reports_menu")
        )

        # الصف الثاني - إدارة المشتركين
        markup.add(
            types.InlineKeyboardButton("👥 المشتركين", callback_data="admin_subscribers"),
            types.InlineKeyboardButton("💰 المدفوعات", callback_data="admin_payments")
        )

        # الصف الثالث - الإعدادات والأدوات
        markup.add(
            types.InlineKeyboardButton("⚙️ الإعدادات", callback_data="admin_settings"),
            types.InlineKeyboardButton("🔧 الأدوات", callback_data="admin_tools")
        )

        # الصف الرابع - الأمان وقائمة الحظر
        markup.add(
            types.InlineKeyboardButton("🛡️ الأمان", callback_data="admin_security"),
            types.InlineKeyboardButton("🚫 قائمة الحظر", callback_data="admin_blacklist")
        )

        # الصف الخامس - نظام الدعم
        markup.add(
            types.InlineKeyboardButton("🎧 نظام الدعم", callback_data="admin_support"),
            types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue")
        )

        # الصف السادس - إدارة الملفات والتحميلات
        markup.add(
            types.InlineKeyboardButton("📁 إدارة الملفات", callback_data="admin_files"),
            types.InlineKeyboardButton("📥 إحصائيات التحميل", callback_data="admin_download_stats")
        )

        # الصف السابع - الرسائل الجماعية والتحديث
        markup.add(
            types.InlineKeyboardButton("📢 رسالة جماعية", callback_data="admin_broadcast"),
            types.InlineKeyboardButton("🔄 تحديث", callback_data="admin_refresh")
        )

        message = "🎛️ لوحة تحكم الإدارة\n\n" \
                 "اختر العملية التي تريد تنفيذها:"

        try:
            self.bot.send_message(chat_id, message, reply_markup=markup)
            return True
        except Exception as e:
            logger.error(f"Failed to show admin menu: {e}")
            return False

    def show_statistics(self, chat_id):
        """عرض الإحصائيات التفصيلية"""
        try:
            stats = self.db.get_statistics()
            if not stats:
                self.bot.send_message(chat_id, "❌ فشل في جلب الإحصائيات")
                return

            # حساب إحصائيات إضافية
            with self.db.get_db_cursor() as cursor:
                # المشتركين المنتهية صلاحيتهم قريباً
                cursor.execute(f"""
                    SELECT COUNT(*) as expiring_soon
                    FROM {self.subscribers_table}
                    WHERE is_active = TRUE AND expire_time <= %s AND expire_time > %s
                """, (
                    int((datetime.now() + timedelta(days=3)).timestamp()),
                    int(datetime.now().timestamp())
                ))
                expiring_soon = cursor.fetchone()['expiring_soon']

                # إيرادات هذا الشهر
                cursor.execute(f"""
                    SELECT COALESCE(SUM(amount), 0) as monthly_revenue
                    FROM {self.payments_table}
                    WHERE status = 'approved'
                    AND DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)
                """)
                monthly_revenue = cursor.fetchone()['monthly_revenue']

                # متوسط الاشتراكات اليومية (آخر 7 أيام)
                cursor.execute(f"""
                    SELECT COUNT(*) / 7.0 as daily_avg
                    FROM {self.subscribers_table}
                    WHERE created_at >= NOW() - INTERVAL '7 days'
                """)
                daily_avg = cursor.fetchone()['daily_avg']

            message = f"📊 إحصائيات النظام\n\n" \
                     f"👥 المشتركين:\n" \
                     f"• الإجمالي: {stats['total_subscribers']}\n" \
                     f"• النشطين: {stats['active_subscribers']}\n" \
                     f"• جدد اليوم: {stats['today_new_subscribers']}\n" \
                     f"• منتهية قريباً: {expiring_soon}\n\n" \
                     f"💰 الإيرادات:\n" \
                     f"• الإجمالي: {stats['total_revenue']:.2f} جنيه\n" \
                     f"• هذا الشهر: {float(monthly_revenue):.2f} جنيه\n" \
                     f"• مدفوعات اليوم: {stats['today_payments']}\n\n" \
                     f"📈 الأداء:\n" \
                     f"• متوسط الاشتراكات اليومية: {float(daily_avg):.1f}\n" \
                     f"• معدل النشاط: {(stats['active_subscribers']/max(stats['total_subscribers'],1)*100):.1f}%"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show statistics: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب الإحصائيات")

    def show_reports_menu(self, chat_id):
        """عرض قائمة التقارير"""
        markup = types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            types.InlineKeyboardButton("💰 تقرير المدفوعات", callback_data="admin_payments_report"),
            types.InlineKeyboardButton("👥 تقرير المشتركين", callback_data="admin_subscribers_report")
        )

        markup.add(
            types.InlineKeyboardButton("📊 تقرير الإيرادات", callback_data="admin_revenue_report"),
            types.InlineKeyboardButton("📈 تقرير النمو", callback_data="admin_growth_report")
        )

        markup.add(
            types.InlineKeyboardButton("📅 تقرير يومي", callback_data="admin_daily_report"),
            types.InlineKeyboardButton("📆 تقرير شهري", callback_data="admin_monthly_report")
        )

        markup.add(
            types.InlineKeyboardButton("⚠️ تقرير المشاكل", callback_data="admin_issues_report"),
            types.InlineKeyboardButton("🔄 تقرير النشاط", callback_data="admin_activity_report")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

        message = "📈 التقارير والإحصائيات\n\n" \
                 "اختر نوع التقرير الذي تريد عرضه:"

        self.bot.send_message(chat_id, message, reply_markup=markup)

    def show_payments_report(self, chat_id):
        """عرض تقرير المدفوعات المفصل"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات المدفوعات
                cursor.execute(f"""
                    SELECT
                        status,
                        COUNT(*) as count,
                        COALESCE(SUM(amount), 0) as total_amount
                    FROM {self.payments_table}
                    GROUP BY status
                """)
                payment_stats = cursor.fetchall()

                # المدفوعات اليوم
                cursor.execute(f"""
                    SELECT COUNT(*) as today_count, COALESCE(SUM(amount), 0) as today_amount
                    FROM {self.payments_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)
                today_stats = cursor.fetchone()

                # المدفوعات هذا الشهر
                cursor.execute(f"""
                    SELECT COUNT(*) as month_count, COALESCE(SUM(amount), 0) as month_amount
                    FROM {self.payments_table}
                    WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)
                """)
                month_stats = cursor.fetchone()

                # أعلى 5 مدفوعات
                cursor.execute(f"""
                    SELECT p.amount, s.first_name, s.username, p.created_at
                    FROM {self.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    WHERE p.status = 'approved'
                    ORDER BY p.amount DESC
                    LIMIT 5
                """)
                top_payments = cursor.fetchall()

            message = "💰 تقرير المدفوعات المفصل\n\n"

            # إحصائيات حسب الحالة
            message += "📊 إحصائيات حسب الحالة:\n"
            for stat in payment_stats:
                status_emoji = {"pending": "⏳", "approved": "✅", "rejected": "❌"}.get(stat['status'], "❓")
                message += f"{status_emoji} {stat['status']}: {stat['count']} دفعة ({float(stat['total_amount']):.2f} جنيه)\n"

            message += f"\n📅 إحصائيات اليوم:\n"
            message += f"• عدد المدفوعات: {today_stats['today_count']}\n"
            message += f"• إجمالي المبلغ: {float(today_stats['today_amount']):.2f} جنيه\n"

            message += f"\n📆 إحصائيات الشهر:\n"
            message += f"• عدد المدفوعات: {month_stats['month_count']}\n"
            message += f"• إجمالي المبلغ: {float(month_stats['month_amount']):.2f} جنيه\n"

            if top_payments:
                message += f"\n🏆 أعلى 5 مدفوعات:\n"
                for i, payment in enumerate(top_payments, 1):
                    name = payment['first_name'] or 'غير محدد'
                    username = payment['username'] or 'غير محدد'
                    date = payment['created_at'].strftime('%Y-%m-%d')
                    message += f"{i}. {float(payment['amount']):.2f} جنيه - {name} (@{username}) - {date}\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📊 تفاصيل أكثر", callback_data="admin_detailed_payments"),
                types.InlineKeyboardButton("🔙 التقارير", callback_data="admin_reports_menu")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show payments report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب تقرير المدفوعات")

    def show_subscribers_report(self, chat_id):
        """عرض تقرير المشتركين المفصل"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات المشتركين
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active,
                        COUNT(CASE WHEN is_active = FALSE THEN 1 END) as inactive
                    FROM {self.subscribers_table}
                """)
                basic_stats = cursor.fetchone()

                # المشتركين الجدد (آخر 30 يوم)
                cursor.execute(f"""
                    SELECT COUNT(*) as new_subscribers
                    FROM {self.subscribers_table}
                    WHERE created_at >= NOW() - INTERVAL '30 days'
                """)
                new_subs = cursor.fetchone()['new_subscribers']

                # المشتركين حسب الشهر (آخر 6 شهور)
                cursor.execute(f"""
                    SELECT
                        DATE_TRUNC('month', created_at) as month,
                        COUNT(*) as count
                    FROM {self.subscribers_table}
                    WHERE created_at >= NOW() - INTERVAL '6 months'
                    GROUP BY DATE_TRUNC('month', created_at)
                    ORDER BY month DESC
                """)
                monthly_stats = cursor.fetchall()

                # أكثر المشتركين نشاطاً
                cursor.execute(f"""
                    SELECT first_name, username, subscription_count, total_paid
                    FROM {self.subscribers_table}
                    WHERE subscription_count > 0
                    ORDER BY subscription_count DESC, total_paid DESC
                    LIMIT 5
                """)
                top_subscribers = cursor.fetchall()

            message = "👥 تقرير المشتركين المفصل\n\n"

            message += f"📊 الإحصائيات العامة:\n"
            message += f"• إجمالي المشتركين: {basic_stats['total']}\n"
            message += f"• المشتركين النشطين: {basic_stats['active']}\n"
            message += f"• المشتركين غير النشطين: {basic_stats['inactive']}\n"
            message += f"• مشتركين جدد (30 يوم): {new_subs}\n"

            if monthly_stats:
                message += f"\n📈 النمو الشهري (آخر 6 شهور):\n"
                for stat in monthly_stats:
                    month_name = stat['month'].strftime('%Y-%m')
                    message += f"• {month_name}: {stat['count']} مشترك جديد\n"

            if top_subscribers:
                message += f"\n🏆 أكثر المشتركين نشاطاً:\n"
                for i, sub in enumerate(top_subscribers, 1):
                    name = sub['first_name'] or 'غير محدد'
                    username = sub['username'] or 'غير محدد'
                    message += f"{i}. {name} (@{username}) - {sub['subscription_count']} اشتراك - {float(sub['total_paid']):.2f} جنيه\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📊 تفاصيل أكثر", callback_data="admin_detailed_subscribers"),
                types.InlineKeyboardButton("🔙 التقارير", callback_data="admin_reports_menu")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show subscribers report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب تقرير المشتركين")

    def show_subscribers_management(self, chat_id):
        """عرض إدارة المشتركين"""
        try:
            logger.info(f"Showing subscribers management for admin {chat_id}")

            markup = types.InlineKeyboardMarkup(row_width=2)

            markup.add(
                types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers"),
                types.InlineKeyboardButton("🔍 البحث عن مشترك", callback_data="admin_search_subscriber")
            )

            markup.add(
                types.InlineKeyboardButton("⚠️ منتهية قريباً", callback_data="admin_expiring_soon"),
                types.InlineKeyboardButton("❌ منتهية الصلاحية", callback_data="admin_expired")
            )

            markup.add(
                types.InlineKeyboardButton("➕ إضافة مشترك", callback_data="admin_add_subscriber"),
                types.InlineKeyboardButton("🗑️ حذف مشترك", callback_data="admin_remove_subscriber")
            )

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

            message = "👥 إدارة المشتركين\n\n" \
                     "اختر العملية التي تريد تنفيذها:"

            self.bot.send_message(chat_id, message, reply_markup=markup)
            logger.info(f"Successfully sent subscribers management menu to admin {chat_id}")

        except Exception as e:
            logger.error(f"Failed to show subscribers management for admin {chat_id}: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إدارة المشتركين")

    def show_payments_management(self, chat_id):
        """عرض إدارة المدفوعات"""
        markup = types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            types.InlineKeyboardButton("⏳ مدفوعات معلقة", callback_data="admin_pending_payments"),
            types.InlineKeyboardButton("✅ مدفوعات مقبولة", callback_data="admin_approved_payments")
        )

        markup.add(
            types.InlineKeyboardButton("❌ مدفوعات مرفوضة", callback_data="admin_rejected_payments"),
            types.InlineKeyboardButton("📊 تقرير المدفوعات", callback_data="admin_payments_report")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

        message = "💰 إدارة المدفوعات\n\n" \
                 "اختر العملية التي تريد تنفيذها:"

        self.bot.send_message(chat_id, message, reply_markup=markup)

    def show_pending_payments(self, chat_id, page=1, limit=5):
        """عرض المدفوعات المعلقة"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                cursor.execute(f"""
                    SELECT p.*, s.username, s.first_name
                    FROM {self.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    WHERE p.status = 'pending'
                    ORDER BY p.created_at DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                payments = cursor.fetchall()

                # عدد المدفوعات الإجمالي
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.payments_table} WHERE status = 'pending'")
                total = cursor.fetchone()['total']

            if not payments:
                message = "✅ لا توجد مدفوعات معلقة حالياً"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"⏳ المدفوعات المعلقة (صفحة {page})\n\n"

            markup = types.InlineKeyboardMarkup(row_width=2)

            for payment in payments:
                username = payment['username'] or 'غير محدد'
                first_name = payment['first_name'] or 'غير محدد'
                created_at = payment['created_at'].strftime('%Y-%m-%d %H:%M')

                message += f"💳 دفعة #{payment['id']}\n" \
                          f"👤 {first_name} (@{username})\n" \
                          f"💰 {payment['amount']} جنيه\n" \
                          f"📅 {created_at}\n" \
                          f"💳 {payment['payment_method'] or 'غير محدد'}\n\n"

                # أزرار الموافقة والرفض
                markup.add(
                    types.InlineKeyboardButton(f"✅ موافقة #{payment['id']}",
                                             callback_data=f"approve_payment_{payment['id']}"),
                    types.InlineKeyboardButton(f"❌ رفض #{payment['id']}",
                                             callback_data=f"reject_payment_{payment['id']}")
                )

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_pending_payments_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_pending_payments_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show pending payments: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب المدفوعات المعلقة")

    def approve_payment(self, chat_id, payment_id):
        """الموافقة على دفعة"""
        try:
            user_id = self.db.approve_payment(payment_id, self.config.ADMIN_ID, "Approved by admin")

            if user_id:
                # الحصول على بيانات الدفعة لمعرفة بيانات المستخدم
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"""
                        SELECT p.*, s.username, s.first_name
                        FROM {self.db.payments_table} p
                        LEFT JOIN {self.db.subscribers_table} s ON p.user_id = s.user_id
                        WHERE p.id = %s
                    """, (payment_id,))
                    payment_data = cursor.fetchone()

                if payment_data:
                    # إضافة/تجديد الاشتراك مع البيانات الصحيحة
                    self.db.add_subscriber(
                        user_id,
                        payment_data.get('username'),
                        payment_data.get('first_name'),
                        None  # last_name
                    )

                # إرسال رسالة للمشترك
                try:
                    self.bot.send_message(user_id,
                        "✅ تم قبول دفعتك!\n\n"
                        "🎉 تم تفعيل اشتراكك بنجاح\n"
                        "🔗 يمكنك الآن الدخول للجروب\n\n"
                        "شكراً لك على الثقة! 💙")

                    # محاولة إنشاء رابط دعوة للجروب
                    try:
                        invite_link = self.bot.create_chat_invite_link(
                            self.config.GROUP_ID,
                            member_limit=1,
                            expire_date=int((datetime.now() + timedelta(hours=24)).timestamp())
                        )

                        self.bot.send_message(user_id,
                            f"🔗 رابط الدخول للجروب:\n{invite_link.invite_link}\n\n"
                            "⚠️ هذا الرابط صالح لمدة 24 ساعة فقط")

                    except Exception as invite_error:
                        invite_error_msg = str(invite_error)
                        if "chat not found" in invite_error_msg.lower():
                            logger.error(f"Failed to create invite link for user {user_id}: Group not found or bot not admin")
                            # إرسال رسالة بديلة للمستخدم
                            self.bot.send_message(user_id,
                                "⚠️ لم نتمكن من إنشاء رابط دعوة تلقائي\n\n"
                                "📞 يرجى التواصل مع الإدارة للحصول على رابط الجروب\n"
                                "✅ اشتراكك مُفعل ومؤكد")

                            # إشعار للإدارة
                            self.bot.send_message(chat_id,
                                f"⚠️ تم قبول الدفعة #{payment_id} بنجاح\n"
                                f"👤 المستخدم: {user_id}\n"
                                f"❌ فشل إنشاء رابط الدعوة - تحقق من إعدادات الجروب\n"
                                f"🔧 تأكد من أن البوت مشرف في الجروب وأن GROUP_ID صحيح")
                        else:
                            logger.error(f"Failed to create invite link for user {user_id}: {invite_error}")
                            # رسالة عامة للمستخدم
                            self.bot.send_message(user_id,
                                "⚠️ حدث خطأ في إنشاء رابط الدعوة\n\n"
                                "📞 يرجى التواصل مع الإدارة للحصول على رابط الجروب\n"
                                "✅ اشتراكك مُفعل ومؤكد")

                except Exception as e:
                    error_msg = str(e)
                    if "chat not found" in error_msg.lower() or "bot was blocked" in error_msg.lower():
                        logger.warning(f"User {user_id} has blocked the bot or deleted chat")
                        # إرسال إشعار للإدارة
                        self.bot.send_message(chat_id,
                            f"⚠️ تم قبول الدفعة #{payment_id} بنجاح\n"
                            f"👤 المستخدم: {user_id}\n"
                            f"❌ المستخدم أوقف البوت أو حذف المحادثة\n"
                            f"✅ الاشتراك مُفعل ويمكنه العودة للبوت في أي وقت")
                    else:
                        logger.error(f"Failed to send approval message to user {user_id}: {e}")
                        # إشعار للإدارة بالخطأ
                        self.bot.send_message(chat_id,
                            f"⚠️ تم قبول الدفعة #{payment_id} بنجاح\n"
                            f"👤 المستخدم: {user_id}\n"
                            f"❌ فشل إرسال رسالة التأكيد: {str(e)}\n"
                            f"✅ الاشتراك مُفعل في النظام")

                self.bot.send_message(chat_id, f"✅ تم قبول الدفعة #{payment_id} بنجاح")

                # تسجيل النشاط
                self.db.log_activity(user_id, "payment_approved", f"Payment #{payment_id} approved by admin")

            else:
                self.bot.send_message(chat_id, f"❌ فشل في قبول الدفعة #{payment_id}")

        except Exception as e:
            logger.error(f"Failed to approve payment {payment_id}: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في قبول الدفعة #{payment_id}")

    def reject_payment(self, chat_id, payment_id, reason=None):
        """رفض دفعة"""
        try:
            user_id = self.db.reject_payment(payment_id, self.config.ADMIN_ID, reason)

            if user_id:
                # إرسال رسالة للمشترك
                try:
                    reject_message = "❌ تم رفض دفعتك\n\n" \
                                   "🔍 يرجى مراجعة إثبات الدفع والمحاولة مرة أخرى\n" \
                                   "📞 للاستفسار: راسل الإدارة مباشرة"

                    if reason:
                        reject_message += f"\n\n📝 السبب: {reason}"

                    self.bot.send_message(user_id, reject_message)

                except Exception as e:
                    logger.error(f"Failed to send rejection message to user {user_id}: {e}")

                self.bot.send_message(chat_id, f"❌ تم رفض الدفعة #{payment_id}")

                # تسجيل النشاط
                self.db.log_activity(user_id, "payment_rejected", f"Payment #{payment_id} rejected by admin: {reason}")

            else:
                self.bot.send_message(chat_id, f"❌ فشل في رفض الدفعة #{payment_id}")

        except Exception as e:
            logger.error(f"Failed to reject payment {payment_id}: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في رفض الدفعة #{payment_id}")

    def show_subscribers_list(self, chat_id, page=1, limit=10):
        """عرض قائمة المشتركين"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                cursor.execute(f"""
                    SELECT user_id, username, first_name, join_time, expire_time, is_active, subscription_count
                    FROM {self.subscribers_table}
                    ORDER BY join_time DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                subscribers = cursor.fetchall()

                # عدد المشتركين الإجمالي
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.subscribers_table}")
                total = cursor.fetchone()['total']

            if not subscribers:
                message = "👥 لا يوجد مشتركين حالياً"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"👥 قائمة المشتركين (صفحة {page})\n\n"

            for subscriber in subscribers:
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                # حساب الأيام المتبقية
                if subscriber['is_active'] and subscriber['expire_time']:
                    days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                    status = f"✅ نشط ({days_left} يوم متبقي)" if days_left > 0 else "❌ منتهي"
                else:
                    status = "❌ غير نشط"

                join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d') if subscriber['join_time'] else 'غير محدد'

                message += f"👤 {first_name} (@{username})\n" \
                          f"🆔 {subscriber['user_id']}\n" \
                          f"📅 انضم: {join_date}\n" \
                          f"📊 الحالة: {status}\n" \
                          f"🔄 عدد الاشتراكات: {subscriber['subscription_count']}\n\n"

            # أزرار التنقل
            markup = types.InlineKeyboardMarkup(row_width=2)

            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_list_subscribers_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_list_subscribers_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show subscribers list: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب قائمة المشتركين")

    def show_expiring_soon(self, chat_id):
        """عرض المشتركين منتهية الصلاحية قريباً"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, username, first_name, expire_time
                    FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    AND expire_time <= %s
                    AND expire_time > %s
                    ORDER BY expire_time ASC
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp())
                ))

                subscribers = cursor.fetchall()

            if not subscribers:
                message = "✅ لا يوجد مشتركين ستنتهي صلاحيتهم قريباً"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"⚠️ مشتركين ستنتهي صلاحيتهم قريباً ({len(subscribers)})\n\n"

            for subscriber in subscribers:
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d')

                message += f"👤 {first_name} (@{username})\n" \
                          f"🆔 {subscriber['user_id']}\n" \
                          f"⏰ ينتهي في: {days_left} يوم ({expire_date})\n\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📢 إرسال تذكير جماعي", callback_data="admin_send_reminder_all"),
                types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show expiring soon: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب المشتركين منتهية الصلاحية")

    def show_expired_subscribers(self, chat_id):
        """عرض المشتركين منتهية الصلاحية"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, username, first_name, expire_time
                    FROM {self.subscribers_table}
                    WHERE is_active = FALSE
                    OR expire_time <= %s
                    ORDER BY expire_time DESC
                    LIMIT 20
                """, (int(datetime.now().timestamp()),))

                subscribers = cursor.fetchall()

            if not subscribers:
                message = "✅ لا يوجد مشتركين منتهية الصلاحية"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"❌ مشتركين منتهية الصلاحية ({len(subscribers)})\n\n"

            for subscriber in subscribers:
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                if subscriber['expire_time']:
                    expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d')
                    days_ago = int((datetime.now().timestamp() - subscriber['expire_time']) / 86400)
                    time_info = f"انتهى منذ {days_ago} يوم ({expire_date})"
                else:
                    time_info = "غير محدد"

                message += f"👤 {first_name} (@{username})\n" \
                          f"🆔 {subscriber['user_id']}\n" \
                          f"⏰ {time_info}\n\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🗑️ تنظيف المنتهية", callback_data="admin_cleanup_expired"),
                types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show expired subscribers: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب المشتركين منتهية الصلاحية")

    def show_approved_payments(self, chat_id, page=1, limit=10):
        """عرض المدفوعات المقبولة"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                cursor.execute(f"""
                    SELECT p.*, s.username, s.first_name
                    FROM {self.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    WHERE p.status = 'approved'
                    ORDER BY p.approved_at DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                payments = cursor.fetchall()

                # عدد المدفوعات الإجمالي
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.payments_table} WHERE status = 'approved'")
                total = cursor.fetchone()['total']

            if not payments:
                message = "✅ لا توجد مدفوعات مقبولة"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"✅ المدفوعات المقبولة (صفحة {page})\n\n"

            for payment in payments:
                username = payment['username'] or 'غير محدد'
                first_name = payment['first_name'] or 'غير محدد'
                approved_at = payment['approved_at'].strftime('%Y-%m-%d %H:%M') if payment['approved_at'] else 'غير محدد'

                message += f"💳 دفعة #{payment['id']}\n" \
                          f"👤 {first_name} (@{username})\n" \
                          f"💰 {payment['amount']} جنيه\n" \
                          f"✅ تمت الموافقة: {approved_at}\n" \
                          f"💳 {payment['payment_method'] or 'غير محدد'}\n\n"

            # أزرار التنقل
            markup = types.InlineKeyboardMarkup(row_width=2)

            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_approved_payments_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_approved_payments_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show approved payments: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب المدفوعات المقبولة")

    def show_rejected_payments(self, chat_id, page=1, limit=10):
        """عرض المدفوعات المرفوضة"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                cursor.execute(f"""
                    SELECT p.*, s.username, s.first_name
                    FROM {self.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    WHERE p.status = 'rejected'
                    ORDER BY p.rejected_at DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                payments = cursor.fetchall()

                # عدد المدفوعات الإجمالي
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.payments_table} WHERE status = 'rejected'")
                total = cursor.fetchone()['total']

            if not payments:
                message = "✅ لا توجد مدفوعات مرفوضة"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"❌ المدفوعات المرفوضة (صفحة {page})\n\n"

            for payment in payments:
                username = payment['username'] or 'غير محدد'
                first_name = payment['first_name'] or 'غير محدد'
                created_at = payment['created_at'].strftime('%Y-%m-%d %H:%M')
                rejected_at = payment['rejected_at'].strftime('%Y-%m-%d %H:%M') if payment.get('rejected_at') else 'غير محدد'
                rejection_reason = payment.get('rejection_reason') or 'غير محدد'

                message += f"💳 دفعة #{payment['id']}\n" \
                          f"👤 {first_name} (@{username})\n" \
                          f"💰 {payment['amount']} جنيه\n" \
                          f"📅 تاريخ الإرسال: {created_at}\n" \
                          f"❌ تاريخ الرفض: {rejected_at}\n" \
                          f"📝 سبب الرفض: {rejection_reason}\n" \
                          f"💳 طريقة الدفع: {payment['payment_method'] or 'غير محدد'}\n\n"

            # أزرار التنقل
            markup = types.InlineKeyboardMarkup(row_width=2)

            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_rejected_payments_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_rejected_payments_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show rejected payments: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب المدفوعات المرفوضة")



    def show_payments_report(self, chat_id):
        """عرض تقرير المدفوعات"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات عامة
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as total_revenue
                    FROM {self.payments_table}
                """)

                stats = cursor.fetchone()

                # إحصائيات هذا الشهر
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as monthly_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as monthly_revenue
                    FROM {self.payments_table}
                    WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)
                """)

                monthly_stats = cursor.fetchone()

                # إحصائيات اليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as daily_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as daily_revenue
                    FROM {self.payments_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                daily_stats = cursor.fetchone()

                # أفضل طرق الدفع
                cursor.execute(f"""
                    SELECT
                        payment_method,
                        COUNT(*) as count,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as revenue
                    FROM {self.payments_table}
                    WHERE payment_method IS NOT NULL
                    GROUP BY payment_method
                    ORDER BY count DESC
                    LIMIT 5
                """)

                payment_methods = cursor.fetchall()

            # تكوين الرسالة
            approval_rate = (stats['approved_count'] / max(stats['total_payments'], 1)) * 100
            rejection_rate = (stats['rejected_count'] / max(stats['total_payments'], 1)) * 100

            message = f"📊 تقرير المدفوعات الشامل\n\n" \
                     f"📈 الإحصائيات العامة:\n" \
                     f"• إجمالي المدفوعات: {stats['total_payments']}\n" \
                     f"• معلقة: {stats['pending_count']}\n" \
                     f"• مقبولة: {stats['approved_count']} ({approval_rate:.1f}%)\n" \
                     f"• مرفوضة: {stats['rejected_count']} ({rejection_rate:.1f}%)\n" \
                     f"• إجمالي الإيرادات: {float(stats['total_revenue']):.2f} جنيه\n\n" \
                     f"📅 هذا الشهر:\n" \
                     f"• المدفوعات: {monthly_stats['monthly_payments']}\n" \
                     f"• الإيرادات: {float(monthly_stats['monthly_revenue']):.2f} جنيه\n\n" \
                     f"📅 اليوم:\n" \
                     f"• المدفوعات: {daily_stats['daily_payments']}\n" \
                     f"• الإيرادات: {float(daily_stats['daily_revenue']):.2f} جنيه\n\n"

            if payment_methods:
                message += f"💳 طرق الدفع الأكثر استخداماً:\n"
                for method in payment_methods:
                    method_name = method['payment_method'] or 'غير محدد'
                    message += f"• {method_name}: {method['count']} مدفوعة ({float(method['revenue']):.2f} جنيه)\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📊 تصدير التقرير", callback_data="admin_export_report"),
                types.InlineKeyboardButton("🔙 العودة", callback_data="admin_payments")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show payments report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إنشاء تقرير المدفوعات")

    def show_tools_menu(self, chat_id):
        """عرض قائمة الأدوات"""
        markup = types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            types.InlineKeyboardButton("🗑️ تنظيف قاعدة البيانات", callback_data="admin_cleanup_db"),
            types.InlineKeyboardButton("💾 نسخة احتياطية", callback_data="admin_backup_db")
        )

        markup.add(
            types.InlineKeyboardButton("📊 إعادة حساب الإحصائيات", callback_data="admin_recalc_stats"),
            types.InlineKeyboardButton("🔄 إعادة تشغيل النظام", callback_data="admin_restart_system")
        )

        markup.add(
            types.InlineKeyboardButton("📧 اختبار التنبيهات", callback_data="admin_test_notifications"),
            types.InlineKeyboardButton("🔍 فحص النظام", callback_data="admin_system_check")
        )

        markup.add(
            types.InlineKeyboardButton("👥 فحص إعدادات الجروب", callback_data="admin_check_group")
        )

        markup.add(
            types.InlineKeyboardButton("🔧 إصلاح الجروب", callback_data="admin_fix_group"),
            types.InlineKeyboardButton("📋 معلومات الجروب", callback_data="admin_group_info")
        )

        markup.add(
            types.InlineKeyboardButton("🆔 الحصول على GROUP_ID", callback_data="admin_get_group_id"),
            types.InlineKeyboardButton("🔗 تحديث رابط الجروب", callback_data="admin_update_group_link")
        )

        markup.add(
            types.InlineKeyboardButton("📅 إصلاح تواريخ الانضمام", callback_data="admin_fix_join_dates"),
            types.InlineKeyboardButton("🔄 تحديث البيانات", callback_data="admin_update_data")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

        message = "🔧 أدوات الإدارة\n\n" \
                 "اختر الأداة التي تريد استخدامها:"

        self.bot.send_message(chat_id, message, reply_markup=markup)

    def show_settings_menu(self, chat_id):
        """عرض قائمة الإعدادات"""
        markup = types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            types.InlineKeyboardButton("💰 إعدادات الاشتراك", callback_data="admin_subscription_settings"),
            types.InlineKeyboardButton("💳 إعدادات الدفع", callback_data="admin_payment_settings")
        )

        markup.add(
            types.InlineKeyboardButton("🔔 إعدادات التنبيهات", callback_data="admin_notification_settings"),
            types.InlineKeyboardButton("👥 إعدادات الجروب", callback_data="admin_group_settings")
        )

        markup.add(
            types.InlineKeyboardButton("📊 إعدادات التقارير", callback_data="admin_report_settings"),
            types.InlineKeyboardButton("🛡️ إعدادات الأمان", callback_data="admin_security_settings")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

        message = "⚙️ إعدادات النظام\n\n" \
                 "اختر الإعداد الذي تريد تعديله:"

        self.bot.send_message(chat_id, message, reply_markup=markup)

    def show_broadcast_menu(self, chat_id):
        """عرض قائمة الرسائل الجماعية"""
        markup = types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            types.InlineKeyboardButton("📢 رسالة لجميع المشتركين", callback_data="admin_broadcast_all"),
            types.InlineKeyboardButton("✅ رسالة للمشتركين النشطين", callback_data="admin_broadcast_active")
        )

        markup.add(
            types.InlineKeyboardButton("⚠️ رسالة للمنتهية قريباً", callback_data="admin_broadcast_expiring"),
            types.InlineKeyboardButton("❌ رسالة للمنتهية الصلاحية", callback_data="admin_broadcast_expired")
        )

        markup.add(
            types.InlineKeyboardButton("📊 إحصائيات الرسائل", callback_data="admin_broadcast_stats"),
            types.InlineKeyboardButton("🧪 اختبار الرسائل", callback_data="admin_test_broadcast")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

        message = "📢 الرسائل الجماعية\n\n" \
                 "اختر نوع الرسالة التي تريد إرسالها:"

        self.bot.send_message(chat_id, message, reply_markup=markup)

    def cleanup_expired_subscribers(self, chat_id):
        """تنظيف المشتركين منتهية الصلاحية"""
        try:
            with self.db.get_db_cursor() as cursor:
                # حذف المشتركين منتهية الصلاحية منذ أكثر من 30 يوم
                cursor.execute(f"""
                    DELETE FROM {self.subscribers_table}
                    WHERE (is_active = FALSE OR expire_time <= %s)
                    AND expire_time < %s
                """, (
                    int(datetime.now().timestamp()),
                    int((datetime.now() - timedelta(days=30)).timestamp())
                ))

                deleted_count = cursor.rowcount

            if deleted_count > 0:
                self.bot.send_message(chat_id, f"✅ تم حذف {deleted_count} مشترك منتهي الصلاحية")

                # تسجيل النشاط
                self.db.log_activity(chat_id, "cleanup_expired", f"Deleted {deleted_count} expired subscribers")
            else:
                self.bot.send_message(chat_id, "✅ لا يوجد مشتركين منتهية الصلاحية للحذف")

        except Exception as e:
            logger.error(f"Failed to cleanup expired subscribers: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تنظيف المشتركين منتهية الصلاحية")

    def send_reminder_to_all_expiring(self, chat_id):
        """إرسال تذكير جماعي للمشتركين منتهية الصلاحية قريباً"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, first_name, expire_time
                    FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    AND expire_time <= %s
                    AND expire_time > %s
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp())
                ))

                subscribers = cursor.fetchall()

            if not subscribers:
                self.bot.send_message(chat_id, "✅ لا يوجد مشتركين يحتاجون تذكير")
                return

            sent_count = 0
            failed_count = 0

            for subscriber in subscribers:
                try:
                    days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))

                    message = f"⚠️ تذكير: سينتهي اشتراكك خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}\n\n" \
                             f"💡 جدد اشتراكك الآن لتجنب انقطاع الخدمة"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(types.InlineKeyboardButton("💳 تجديد الاشتراك", callback_data="payment_methods"))

                    self.bot.send_message(subscriber['user_id'], message, reply_markup=markup)
                    sent_count += 1

                except Exception as e:
                    logger.error(f"Failed to send reminder to user {subscriber['user_id']}: {e}")
                    failed_count += 1

            result_message = f"📢 تم إرسال التذكير:\n" \
                           f"✅ نجح: {sent_count}\n" \
                           f"❌ فشل: {failed_count}\n" \
                           f"📊 الإجمالي: {len(subscribers)}"

            self.bot.send_message(chat_id, result_message)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "mass_reminder", f"Sent reminders to {sent_count} users")

        except Exception as e:
            logger.error(f"Failed to send mass reminder: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إرسال التذكير الجماعي")

    def search_subscriber(self, chat_id, search_query):
        """البحث عن مشترك"""
        try:
            with self.db.get_db_cursor() as cursor:
                # البحث بالمعرف أو اسم المستخدم أو الاسم الأول
                cursor.execute(f"""
                    SELECT user_id, username, first_name, join_time, expire_time, is_active, subscription_count, total_paid
                    FROM {self.subscribers_table}
                    WHERE CAST(user_id AS TEXT) LIKE %s
                    OR LOWER(username) LIKE LOWER(%s)
                    OR LOWER(first_name) LIKE LOWER(%s)
                    ORDER BY join_time DESC
                    LIMIT 10
                """, (f"%{search_query}%", f"%{search_query}%", f"%{search_query}%"))

                subscribers = cursor.fetchall()

            if not subscribers:
                message = f"🔍 لم يتم العثور على مشتركين بالبحث: '{search_query}'"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"🔍 نتائج البحث عن: '{search_query}'\n\n"

            for subscriber in subscribers:
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                # حساب الأيام المتبقية
                if subscriber['is_active'] and subscriber['expire_time']:
                    days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                    status = f"✅ نشط ({days_left} يوم متبقي)" if days_left > 0 else "❌ منتهي"
                else:
                    status = "❌ غير نشط"

                join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d') if subscriber['join_time'] else 'غير محدد'

                message += f"👤 {first_name} (@{username})\n" \
                          f"🆔 {subscriber['user_id']}\n" \
                          f"📅 انضم: {join_date}\n" \
                          f"📊 الحالة: {status}\n" \
                          f"🔄 عدد الاشتراكات: {subscriber['subscription_count']}\n" \
                          f"💰 إجمالي المدفوعات: {float(subscriber['total_paid']):.2f} جنيه\n\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔍 بحث جديد", callback_data="admin_search_subscriber"),
                types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to search subscriber: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في البحث عن المشترك")

    def add_subscriber_manually(self, chat_id, user_id, duration_days=30):
        """إضافة مشترك يدوياً"""
        try:
            # التحقق من وجود المستخدم
            try:
                user_info = self.bot.get_chat(user_id)
                username = user_info.username
                first_name = user_info.first_name
            except:
                # إذا لم نتمكن من الحصول على معلومات المستخدم
                username = None
                first_name = "مستخدم يدوي"

            # إضافة الاشتراك
            success = self.db.add_subscriber(user_id, username, first_name, None)

            if success:
                # إنشاء رابط دعوة للجروب
                try:
                    invite_link = self.bot.create_chat_invite_link(
                        self.config.GROUP_ID,
                        member_limit=1,
                        expire_date=int((datetime.now() + timedelta(hours=24)).timestamp())
                    )

                    # إرسال رسالة للمشترك الجديد
                    try:
                        self.bot.send_message(user_id,
                            f"🎉 تم تفعيل اشتراكك يدوياً من قبل الإدارة!\n\n"
                            f"⏰ مدة الاشتراك: {duration_days} يوم\n"
                            f"🔗 رابط الدخول للجروب:\n{invite_link.invite_link}\n\n"
                            f"⚠️ هذا الرابط صالح لمدة 24 ساعة فقط")
                    except:
                        pass  # المستخدم قد يكون أوقف البوت

                    self.bot.send_message(chat_id,
                        f"✅ تم إضافة المشترك بنجاح!\n\n"
                        f"🆔 معرف المستخدم: {user_id}\n"
                        f"👤 الاسم: {first_name or 'غير محدد'}\n"
                        f"⏰ مدة الاشتراك: {duration_days} يوم\n"
                        f"🔗 تم إرسال رابط الجروب")

                except Exception as e:
                    logger.error(f"Failed to create invite link: {e}")
                    self.bot.send_message(chat_id,
                        f"✅ تم إضافة المشترك بنجاح!\n"
                        f"❌ لكن فشل في إنشاء رابط الجروب")

                # تسجيل النشاط
                self.db.log_activity(chat_id, "manual_add_subscriber", f"Added user {user_id} manually")

            else:
                self.bot.send_message(chat_id, "❌ فشل في إضافة المشترك")

        except Exception as e:
            logger.error(f"Failed to add subscriber manually: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إضافة المشترك")

    def remove_subscriber_manually(self, chat_id, user_id):
        """حذف مشترك يدوياً مع إجراءات أمنية شاملة"""
        try:
            logger.info(f"Admin {chat_id} attempting to remove subscriber {user_id}")

            # الحصول على معلومات المشترك قبل الحذف
            subscriber = self.db.get_subscriber(user_id)

            if not subscriber:
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("🔍 البحث عن مشترك", callback_data="admin_search_subscriber"),
                    types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers")
                )
                markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

                self.bot.send_message(
                    chat_id,
                    f"❌ **المستخدم غير موجود**\n\n"
                    f"🔍 لم يتم العثور على مشترك بالمعرف: `{user_id}`\n\n"
                    f"💡 تأكد من صحة المعرف أو ابحث عن المشترك أولاً",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return False

            # استخدام مدير روابط الدعوة لإزالة المستخدم بأمان
            try:
                from invite_manager import InviteManager
                invite_manager = InviteManager(self.bot, self.db, self.config)
                removal_result = invite_manager.handle_user_removal(user_id, "manual_admin_removal")
                links_revoked = removal_result.get('links_revoked', 0)
                group_removed = removal_result.get('group_removed', False)
            except Exception as e:
                logger.error(f"Failed to use invite manager for removal: {e}")
                links_revoked = 0
                group_removed = False

                # محاولة إزالة تقليدية من الجروب
                try:
                    self.bot.ban_chat_member(self.config.GROUP_ID, user_id)
                    self.bot.unban_chat_member(self.config.GROUP_ID, user_id)
                    group_removed = True
                except Exception as group_error:
                    logger.error(f"Failed to remove user from group: {group_error}")

            # حذف المشترك من قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    DELETE FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                deleted_count = cursor.rowcount

            if deleted_count == 0:
                self.bot.send_message(chat_id, "❌ فشل في حذف المشترك من قاعدة البيانات")
                return False

            # إرسال رسالة للمستخدم المحذوف
            try:
                self.bot.send_message(user_id,
                    "❌ **تم إنهاء اشتراكك**\n\n"
                    "🔴 تم إنهاء اشتراكك بواسطة الإدارة\n"
                    "📞 للاستفسار: راسل الإدارة مباشرة\n\n"
                    "💡 يمكنك الاشتراك مرة أخرى في أي وقت")
            except Exception as msg_error:
                logger.info(f"Could not send message to removed user {user_id}: {msg_error}")

            # إعداد رسالة النجاح
            username = subscriber.get('username', 'غير محدد')
            first_name = subscriber.get('first_name', 'غير محدد')

            success_message = f"✅ **تم حذف المشترك بنجاح!**\n\n"
            success_message += f"👤 **المشترك المحذوف:**\n"
            success_message += f"📝 الاسم: {first_name}\n"
            success_message += f"📱 اسم المستخدم: @{username}\n"
            success_message += f"🆔 المعرف: `{user_id}`\n"
            success_message += f"💰 إجمالي مدفوعاته: {float(subscriber.get('total_paid', 0)):.2f} جنيه\n\n"

            success_message += f"🔒 **الإجراءات الأمنية المتخذة:**\n"
            success_message += f"• {'✅' if group_removed else '❌'} إزالة من الجروب\n"
            success_message += f"• 🔗 إلغاء {links_revoked} رابط دعوة\n"
            success_message += f"• 🗑️ حذف جميع البيانات من النظام\n"
            success_message += f"• 📝 تسجيل الإجراء في سجل النشاط"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers"),
                types.InlineKeyboardButton("🔍 البحث عن مشترك", callback_data="admin_search_subscriber")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(chat_id, success_message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(
                chat_id,
                "manual_remove_subscriber",
                f"Removed user {user_id} ({first_name}), revoked {links_revoked} links"
            )

            logger.info(f"Successfully removed subscriber {user_id} by admin {chat_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to remove subscriber manually: {e}")

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(
                chat_id,
                f"❌ **حدث خطأ في حذف المشترك**\n\n"
                f"🔧 تفاصيل الخطأ: {str(e)}\n\n"
                f"💡 حاول مرة أخرى أو راجع سجل الأخطاء",
                reply_markup=markup,
                parse_mode='Markdown'
            )
            return False

    def cleanup_database(self, chat_id):
        """تنظيف قاعدة البيانات"""
        try:
            with self.db.get_db_cursor() as cursor:
                # حذف التنبيهات القديمة (أكثر من 30 يوم)
                cursor.execute(f"""
                    DELETE FROM {self.db.notifications_table}
                    WHERE sent_at < NOW() - INTERVAL '30 days'
                """)
                notifications_deleted = cursor.rowcount

                # حذف سجلات النشاط القديمة (أكثر من 90 يوم)
                cursor.execute(f"""
                    DELETE FROM {self.db.activity_logs_table}
                    WHERE created_at < NOW() - INTERVAL '90 days'
                """)
                logs_deleted = cursor.rowcount

                # حذف المدفوعات المرفوضة القديمة (أكثر من 60 يوم)
                cursor.execute(f"""
                    DELETE FROM {self.db.payments_table}
                    WHERE status = 'rejected' AND created_at < NOW() - INTERVAL '60 days'
                """)
                payments_deleted = cursor.rowcount

            message = f"🗑️ تم تنظيف قاعدة البيانات بنجاح!\n\n" \
                     f"📧 التنبيهات المحذوفة: {notifications_deleted}\n" \
                     f"📝 سجلات النشاط المحذوفة: {logs_deleted}\n" \
                     f"💳 المدفوعات المرفوضة المحذوفة: {payments_deleted}\n\n" \
                     f"✅ تم توفير مساحة في قاعدة البيانات"

            self.bot.send_message(chat_id, message)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "database_cleanup",
                               f"Deleted {notifications_deleted} notifications, {logs_deleted} logs, {payments_deleted} payments")

        except Exception as e:
            logger.error(f"Failed to cleanup database: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تنظيف قاعدة البيانات")

    def recalculate_statistics(self, chat_id):
        """إعادة حساب الإحصائيات"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إعادة حساب إجمالي المدفوعات لكل مشترك
                cursor.execute(f"""
                    UPDATE {self.subscribers_table}
                    SET total_paid = (
                        SELECT COALESCE(SUM(amount), 0)
                        FROM {self.db.payments_table}
                        WHERE {self.db.payments_table}.user_id = {self.subscribers_table}.user_id
                        AND status = 'approved'
                    )
                """)
                updated_subscribers = cursor.rowcount

                # إعادة حساب عدد الاشتراكات لكل مشترك
                cursor.execute(f"""
                    UPDATE {self.subscribers_table}
                    SET subscription_count = (
                        SELECT COUNT(*)
                        FROM {self.db.payments_table}
                        WHERE {self.db.payments_table}.user_id = {self.subscribers_table}.user_id
                        AND status = 'approved'
                    )
                """)

                # تحديث الإحصائيات في جدول الإحصائيات
                cursor.execute(f"""
                    INSERT INTO {self.db.statistics_table}
                    (date, total_subscribers, active_subscribers, total_revenue, daily_revenue)
                    VALUES (CURRENT_DATE,
                        (SELECT COUNT(*) FROM {self.subscribers_table}),
                        (SELECT COUNT(*) FROM {self.subscribers_table} WHERE is_active = TRUE),
                        (SELECT COALESCE(SUM(amount), 0) FROM {self.db.payments_table} WHERE status = 'approved'),
                        (SELECT COALESCE(SUM(amount), 0) FROM {self.db.payments_table}
                         WHERE status = 'approved' AND DATE(created_at) = CURRENT_DATE)
                    )
                    ON CONFLICT (date) DO UPDATE SET
                        total_subscribers = EXCLUDED.total_subscribers,
                        active_subscribers = EXCLUDED.active_subscribers,
                        total_revenue = EXCLUDED.total_revenue,
                        daily_revenue = EXCLUDED.daily_revenue
                """)

            message = f"📊 تم إعادة حساب الإحصائيات بنجاح!\n\n" \
                     f"👥 المشتركين المحدثين: {updated_subscribers}\n" \
                     f"📈 تم تحديث جميع الإحصائيات\n" \
                     f"💰 تم إعادة حساب المدفوعات\n\n" \
                     f"✅ الإحصائيات محدثة ودقيقة الآن"

            self.bot.send_message(chat_id, message)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "recalculate_stats", f"Updated {updated_subscribers} subscribers")

        except Exception as e:
            logger.error(f"Failed to recalculate statistics: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إعادة حساب الإحصائيات")

    def test_notifications(self, chat_id):
        """اختبار نظام التنبيهات"""
        try:
            # اختبار إرسال تنبيه للإدارة
            test_message = f"🧪 اختبار نظام التنبيهات\n\n" \
                          f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" \
                          f"🤖 البوت: يعمل بشكل طبيعي\n" \
                          f"📡 الاتصال: متصل\n" \
                          f"🔔 التنبيهات: تعمل بنجاح\n\n" \
                          f"✅ جميع الأنظمة تعمل بشكل صحيح!"

            self.bot.send_message(chat_id, test_message)

            # اختبار إرسال تنبيه تجريبي لمشترك واحد (إذا وجد)
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    LIMIT 1
                """)
                test_user = cursor.fetchone()

            if test_user:
                try:
                    self.bot.send_message(test_user['user_id'],
                        "🧪 رسالة اختبار من نظام التنبيهات\n\n"
                        "هذه رسالة تجريبية للتأكد من عمل النظام\n"
                        "يمكنك تجاهل هذه الرسالة")

                    self.bot.send_message(chat_id,
                        f"✅ تم إرسال رسالة اختبار للمستخدم {test_user['user_id']}")
                except:
                    self.bot.send_message(chat_id,
                        "⚠️ فشل في إرسال رسالة اختبار للمستخدم (قد يكون أوقف البوت)")

            # تسجيل النشاط
            self.db.log_activity(chat_id, "test_notifications", "Tested notification system")

        except Exception as e:
            logger.error(f"Failed to test notifications: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في اختبار التنبيهات")

    def system_health_check(self, chat_id):
        """فحص صحة النظام"""
        try:
            health_report = "🔍 تقرير فحص النظام\n\n"

            # فحص قاعدة البيانات
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute("SELECT 1")
                health_report += "✅ قاعدة البيانات: متصلة\n"
            except:
                health_report += "❌ قاعدة البيانات: غير متصلة\n"

            # فحص الجداول
            try:
                with self.db.get_db_cursor() as cursor:
                    tables = [
                        self.subscribers_table,
                        self.db.payments_table,
                        self.db.notifications_table,
                        self.db.activity_logs_table,
                        self.db.statistics_table
                    ]

                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                        count = cursor.fetchone()['count']
                        health_report += f"✅ {table}: {count} سجل\n"

            except Exception as e:
                health_report += f"❌ خطأ في فحص الجداول: {e}\n"

            # فحص البوت
            try:
                me = self.bot.get_me()
                health_report += f"✅ البوت: {me.first_name} (@{me.username})\n"
            except:
                health_report += "❌ البوت: غير متصل\n"

            # فحص الجروب
            try:
                chat_info = self.bot.get_chat(self.config.GROUP_ID)
                member_count = self.bot.get_chat_member_count(self.config.GROUP_ID)
                health_report += f"✅ الجروب: {chat_info.title} ({member_count} عضو)\n"
            except:
                health_report += "❌ الجروب: غير متاح\n"

            # إحصائيات سريعة
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_subscribers
                    FROM {self.subscribers_table}
                """)
                stats = cursor.fetchone()

                cursor.execute(f"""
                    SELECT COUNT(*) as pending_payments
                    FROM {self.db.payments_table}
                    WHERE status = 'pending'
                """)
                pending = cursor.fetchone()

            health_report += f"\n📊 إحصائيات سريعة:\n" \
                           f"👥 إجمالي المشتركين: {stats['total_subscribers']}\n" \
                           f"✅ المشتركين النشطين: {stats['active_subscribers']}\n" \
                           f"⏳ المدفوعات المعلقة: {pending['pending_payments']}\n"

            health_report += f"\n⏰ وقت الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            self.bot.send_message(chat_id, health_report)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "system_health_check", "Performed system health check")

        except Exception as e:
            logger.error(f"Failed to perform health check: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في فحص النظام")

    def broadcast_to_all_subscribers(self, chat_id, message_text):
        """إرسال رسالة جماعية لجميع المشتركين"""
        try:
            # إرسال رسالة بدء العملية
            progress_msg = self.bot.send_message(chat_id, "📢 جاري إرسال الرسالة الجماعية...\n⏳ يرجى الانتظار")

            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, first_name, username FROM {self.subscribers_table}
                    ORDER BY join_time DESC
                """)
                subscribers = cursor.fetchall()

            if not subscribers:
                self.bot.edit_message_text(
                    "❌ لا يوجد مشتركين لإرسال الرسالة إليهم",
                    chat_id,
                    progress_msg.message_id
                )
                return

            sent_count = 0
            failed_count = 0
            failed_users = []

            # إضافة تأخير بين الرسائل لتجنب حدود التليجرام
            import time

            for i, subscriber in enumerate(subscribers):
                try:
                    # تحديث رسالة التقدم كل 10 مستخدمين
                    if i % 10 == 0:
                        progress_text = f"📢 جاري الإرسال...\n\n" \
                                      f"✅ تم إرسال: {sent_count}\n" \
                                      f"❌ فشل: {failed_count}\n" \
                                      f"📊 التقدم: {i}/{len(subscribers)}"
                        try:
                            self.bot.edit_message_text(progress_text, chat_id, progress_msg.message_id)
                        except:
                            pass

                    # إرسال الرسالة
                    self.bot.send_message(subscriber['user_id'], message_text)
                    sent_count += 1

                    # تأخير قصير لتجنب حدود التليجرام (30 رسالة في الثانية)
                    time.sleep(0.05)

                except Exception as e:
                    logger.error(f"Failed to send broadcast to user {subscriber['user_id']}: {e}")
                    failed_count += 1
                    failed_users.append({
                        'user_id': subscriber['user_id'],
                        'name': subscriber.get('first_name', 'غير محدد'),
                        'error': str(e)
                    })

            # رسالة النتيجة النهائية
            result_message = f"📢 **تم إرسال الرسالة الجماعية**\n\n" \
                           f"✅ **نجح:** {sent_count}\n" \
                           f"❌ **فشل:** {failed_count}\n" \
                           f"📊 **الإجمالي:** {len(subscribers)}\n" \
                           f"📈 **معدل النجاح:** {(sent_count/len(subscribers)*100):.1f}%\n\n" \
                           f"📝 **الرسالة المرسلة:**\n{message_text[:150]}{'...' if len(message_text) > 150 else ''}"

            # إضافة تفاصيل الأخطاء إذا وجدت
            if failed_users and len(failed_users) <= 5:
                result_message += f"\n\n❌ **المستخدمين الذين فشل الإرسال إليهم:**\n"
                for user in failed_users[:5]:
                    result_message += f"• {user['name']} ({user['user_id']})\n"

            self.bot.edit_message_text(result_message, chat_id, progress_msg.message_id, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(chat_id, "broadcast_all", f"Sent to {sent_count}/{len(subscribers)} users")

        except Exception as e:
            logger.error(f"Failed to broadcast to all subscribers: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في إرسال الرسالة الجماعية:\n{str(e)}")

    def broadcast_to_active_subscribers(self, chat_id, message_text):
        """إرسال رسالة جماعية للمشتركين النشطين فقط"""
        try:
            # إرسال رسالة بدء العملية
            progress_msg = self.bot.send_message(chat_id, "📢 جاري إرسال الرسالة للمشتركين النشطين...\n⏳ يرجى الانتظار")

            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, first_name, username FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    ORDER BY join_time DESC
                """)
                subscribers = cursor.fetchall()

            if not subscribers:
                self.bot.edit_message_text(
                    "❌ لا يوجد مشتركين نشطين لإرسال الرسالة إليهم",
                    chat_id,
                    progress_msg.message_id
                )
                return

            sent_count = 0
            failed_count = 0
            failed_users = []

            # إضافة تأخير بين الرسائل لتجنب حدود التليجرام
            import time

            for i, subscriber in enumerate(subscribers):
                try:
                    # تحديث رسالة التقدم كل 5 مستخدمين
                    if i % 5 == 0:
                        progress_text = f"📢 جاري الإرسال للنشطين...\n\n" \
                                      f"✅ تم إرسال: {sent_count}\n" \
                                      f"❌ فشل: {failed_count}\n" \
                                      f"📊 التقدم: {i}/{len(subscribers)}"
                        try:
                            self.bot.edit_message_text(progress_text, chat_id, progress_msg.message_id)
                        except:
                            pass

                    # إرسال الرسالة
                    self.bot.send_message(subscriber['user_id'], message_text)
                    sent_count += 1

                    # تأخير قصير لتجنب حدود التليجرام
                    time.sleep(0.05)

                except Exception as e:
                    logger.error(f"Failed to send broadcast to user {subscriber['user_id']}: {e}")
                    failed_count += 1
                    failed_users.append({
                        'user_id': subscriber['user_id'],
                        'name': subscriber.get('first_name', 'غير محدد'),
                        'error': str(e)
                    })

            # رسالة النتيجة النهائية
            result_message = f"📢 **تم إرسال الرسالة للمشتركين النشطين**\n\n" \
                           f"✅ **نجح:** {sent_count}\n" \
                           f"❌ **فشل:** {failed_count}\n" \
                           f"📊 **الإجمالي:** {len(subscribers)}\n" \
                           f"📈 **معدل النجاح:** {(sent_count/len(subscribers)*100):.1f}%\n\n" \
                           f"📝 **الرسالة المرسلة:**\n{message_text[:150]}{'...' if len(message_text) > 150 else ''}"

            # إضافة تفاصيل الأخطاء إذا وجدت
            if failed_users and len(failed_users) <= 3:
                result_message += f"\n\n❌ **المستخدمين الذين فشل الإرسال إليهم:**\n"
                for user in failed_users[:3]:
                    result_message += f"• {user['name']} ({user['user_id']})\n"

            self.bot.edit_message_text(result_message, chat_id, progress_msg.message_id, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(chat_id, "broadcast_active", f"Sent to {sent_count}/{len(subscribers)} active users")

        except Exception as e:
            logger.error(f"Failed to broadcast to active subscribers: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في إرسال الرسالة للمشتركين النشطين:\n{str(e)}")

    def broadcast_to_expiring_subscribers(self, chat_id, message_text):
        """إرسال رسالة جماعية للمشتركين منتهية الصلاحية قريباً"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, first_name, expire_time FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    AND expire_time <= %s
                    AND expire_time > %s
                    ORDER BY expire_time ASC
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp())
                ))
                subscribers = cursor.fetchall()

            if not subscribers:
                self.bot.send_message(chat_id, "❌ لا يوجد مشتركين ستنتهي صلاحيتهم قريباً")
                return

            sent_count = 0
            failed_count = 0

            for subscriber in subscribers:
                try:
                    days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                    personalized_message = f"{message_text}\n\n⏰ ينتهي اشتراكك خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}"

                    self.bot.send_message(subscriber['user_id'], personalized_message)
                    sent_count += 1
                except Exception as e:
                    logger.error(f"Failed to send broadcast to user {subscriber['user_id']}: {e}")
                    failed_count += 1

            result_message = f"📢 تم إرسال الرسالة للمنتهية قريباً:\n\n" \
                           f"✅ نجح: {sent_count}\n" \
                           f"❌ فشل: {failed_count}\n" \
                           f"📊 الإجمالي: {len(subscribers)}\n\n" \
                           f"📝 الرسالة المرسلة:\n{message_text[:100]}..."

            self.bot.send_message(chat_id, result_message)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "broadcast_expiring", f"Sent to {sent_count} expiring users")

        except Exception as e:
            logger.error(f"Failed to broadcast to expiring subscribers: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إرسال الرسالة للمنتهية قريباً")

    def get_broadcast_statistics(self, chat_id):
        """عرض إحصائيات الرسائل الجماعية"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات الرسائل الجماعية من سجل النشاط
                cursor.execute(f"""
                    SELECT
                        action,
                        COUNT(*) as count,
                        MAX(created_at) as last_sent
                    FROM {self.db.activity_logs_table}
                    WHERE action IN ('broadcast_all', 'broadcast_active', 'broadcast_expiring', 'mass_reminder')
                    GROUP BY action
                    ORDER BY count DESC
                """)
                broadcast_stats = cursor.fetchall()

                # إحصائيات المشتركين
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_subscribers,
                        COUNT(CASE WHEN is_active = TRUE AND expire_time <= %s AND expire_time > %s THEN 1 END) as expiring_soon
                    FROM {self.subscribers_table}
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp())
                ))
                subscriber_stats = cursor.fetchone()

            message = f"📊 إحصائيات الرسائل الجماعية\n\n"

            # إحصائيات المشتركين
            message += f"👥 المشتركين المستهدفين:\n" \
                      f"• جميع المشتركين: {subscriber_stats['total_subscribers']}\n" \
                      f"• المشتركين النشطين: {subscriber_stats['active_subscribers']}\n" \
                      f"• المنتهية قريباً: {subscriber_stats['expiring_soon']}\n\n"

            # إحصائيات الرسائل المرسلة
            if broadcast_stats:
                message += f"📢 الرسائل المرسلة:\n"
                for stat in broadcast_stats:
                    action_name = {
                        'broadcast_all': 'رسائل لجميع المشتركين',
                        'broadcast_active': 'رسائل للنشطين',
                        'broadcast_expiring': 'رسائل للمنتهية قريباً',
                        'mass_reminder': 'تذكيرات جماعية'
                    }.get(stat['action'], stat['action'])

                    last_sent = stat['last_sent'].strftime('%Y-%m-%d %H:%M') if stat['last_sent'] else 'غير محدد'
                    message += f"• {action_name}: {stat['count']} مرة (آخر إرسال: {last_sent})\n"
            else:
                message += f"📢 لم يتم إرسال رسائل جماعية بعد\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_broadcast"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to get broadcast statistics: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في جلب إحصائيات الرسائل")

    def test_broadcast_system(self, chat_id):
        """اختبار نظام الرسائل الجماعية"""
        try:
            message = "🧪 اختبار نظام الرسائل الجماعية\n\n"

            # فحص قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                # عدد المشتركين الإجمالي
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.subscribers_table}")
                total_subscribers = cursor.fetchone()['total']

                # عدد المشتركين النشطين
                cursor.execute(f"SELECT COUNT(*) as active FROM {self.subscribers_table} WHERE is_active = TRUE")
                active_subscribers = cursor.fetchone()['active']

                # اختبار جلب عينة من المشتركين
                cursor.execute(f"""
                    SELECT user_id, first_name, username FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    LIMIT 3
                """)
                sample_users = cursor.fetchall()

            message += f"📊 **إحصائيات قاعدة البيانات:**\n"
            message += f"• إجمالي المشتركين: {total_subscribers}\n"
            message += f"• المشتركين النشطين: {active_subscribers}\n\n"

            # اختبار الاتصال بالبوت
            try:
                bot_info = self.bot.get_me()
                message += f"🤖 **معلومات البوت:**\n"
                message += f"• الاسم: {bot_info.first_name}\n"
                message += f"• اسم المستخدم: @{bot_info.username}\n"
                message += f"• الحالة: ✅ متصل\n\n"
            except Exception as e:
                message += f"🤖 **معلومات البوت:**\n"
                message += f"• الحالة: ❌ خطأ في الاتصال: {str(e)}\n\n"

            # عرض عينة من المشتركين
            if sample_users:
                message += f"👥 **عينة من المشتركين النشطين:**\n"
                for user in sample_users:
                    message += f"• {user.get('first_name', 'غير محدد')} (@{user.get('username', 'غير محدد')}) - {user['user_id']}\n"
            else:
                message += f"👥 **عينة من المشتركين:**\n• لا يوجد مشتركين نشطين\n"

            message += f"\n🧪 **اختبارات النظام:**\n"

            # اختبار إرسال رسالة للإدارة
            try:
                test_message = f"🧪 رسالة اختبار من نظام الرسائل الجماعية\n⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                self.bot.send_message(chat_id, test_message)
                message += f"• ✅ إرسال رسالة للإدارة: نجح\n"
            except Exception as e:
                message += f"• ❌ إرسال رسالة للإدارة: فشل ({str(e)})\n"

            # اختبار قراءة قاعدة البيانات
            if total_subscribers >= 0:
                message += f"• ✅ قراءة قاعدة البيانات: نجح\n"
            else:
                message += f"• ❌ قراءة قاعدة البيانات: فشل\n"

            message += f"\n💡 **التوصيات:**\n"
            if total_subscribers == 0:
                message += f"• لا يوجد مشتركين لإرسال الرسائل إليهم\n"
                message += f"• أضف مشترك تجريبي للاختبار\n"
            elif active_subscribers == 0:
                message += f"• لا يوجد مشتركين نشطين\n"
                message += f"• تحقق من حالة المشتركين الموجودين\n"
            else:
                message += f"• النظام جاهز لإرسال الرسائل الجماعية\n"
                message += f"• يمكنك إرسال رسالة تجريبية للمشتركين النشطين\n"

            markup = types.InlineKeyboardMarkup()
            if active_subscribers > 0:
                markup.add(
                    types.InlineKeyboardButton("📤 إرسال رسالة تجريبية", callback_data="admin_send_test_message"),
                    types.InlineKeyboardButton("📊 إحصائيات مفصلة", callback_data="admin_broadcast_stats")
                )
            else:
                markup.add(
                    types.InlineKeyboardButton("➕ إضافة مشترك تجريبي", callback_data="admin_add_test_subscriber"),
                    types.InlineKeyboardButton("📊 إحصائيات مفصلة", callback_data="admin_broadcast_stats")
                )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_broadcast"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to test broadcast system: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في اختبار نظام الرسائل الجماعية:\n{str(e)}")

    def send_test_broadcast_message(self, chat_id):
        """إرسال رسالة تجريبية للمشتركين"""
        try:
            test_message = f"🧪 رسالة تجريبية من الإدارة\n\n" \
                          f"📢 هذه رسالة اختبار لنظام الرسائل الجماعية\n" \
                          f"⏰ تم الإرسال في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n" \
                          f"✅ إذا وصلتك هذه الرسالة، فإن النظام يعمل بشكل صحيح\n" \
                          f"📞 للاستفسار: راسل الإدارة"

            # إرسال للمشتركين النشطين فقط
            self.broadcast_to_active_subscribers(chat_id, test_message)

        except Exception as e:
            logger.error(f"Failed to send test broadcast: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في إرسال الرسالة التجريبية:\n{str(e)}")



    def show_subscribers_list(self, chat_id, page=1, limit=10):
        """عرض قائمة المشتركين مع التنقل"""
        try:
            logger.info(f"Admin {chat_id} requesting subscribers list, page {page}")

            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                # عدد المشتركين الإجمالي أولاً
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.subscribers_table}")
                total = cursor.fetchone()['total']

                logger.info(f"Total subscribers in database: {total}")

                if total == 0:
                    # لا يوجد مشتركين - عرض خيار إضافة مشترك تجريبي
                    message = "❌ لا يوجد مشتركين في النظام\n\n" \
                             "💡 يمكنك إضافة مشترك تجريبي للاختبار"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(
                        types.InlineKeyboardButton("➕ إضافة مشترك تجريبي", callback_data="admin_add_test_subscriber"),
                        types.InlineKeyboardButton("➕ إضافة مشترك يدوياً", callback_data="admin_add_subscriber")
                    )
                    markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                    self.bot.send_message(chat_id, message, reply_markup=markup)
                    return

                # جلب المشتركين مع التنقل
                cursor.execute(f"""
                    SELECT user_id, username, first_name, is_active, expire_time, join_time, subscription_count, total_paid
                    FROM {self.subscribers_table}
                    ORDER BY join_time DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                subscribers = cursor.fetchall()
                logger.info(f"Retrieved {len(subscribers)} subscribers for page {page}")

            if not subscribers and total > 0:
                # هناك مشتركين لكن الصفحة فارغة - العودة للصفحة الأولى
                message = f"❌ الصفحة {page} فارغة\n\n" \
                         f"📊 إجمالي المشتركين: {total}\n" \
                         f"📄 عدد الصفحات: {(total + limit - 1) // limit}"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("📄 الصفحة الأولى", callback_data="admin_list_subscribers_1"),
                    types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers")
                )
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"👥 **قائمة المشتركين** (صفحة {page})\n\n"
            message += f"📊 **الإحصائيات:**\n"
            message += f"👥 إجمالي المشتركين: {total}\n"
            message += f"📄 الصفحة: {page}/{(total + limit - 1) // limit}\n"
            message += f"📋 عرض: {len(subscribers)} مشترك\n\n"

            for i, subscriber in enumerate(subscribers, 1):
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                # حساب الأيام المتبقية
                if subscriber['is_active'] and subscriber['expire_time']:
                    days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                    if days_left > 7:
                        status = f"✅ نشط ({days_left} يوم)"
                    elif days_left > 3:
                        status = f"🟡 ينتهي قريباً ({days_left} أيام)"
                    elif days_left > 0:
                        status = f"🔴 ينتهي خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}"
                    else:
                        status = "⚠️ منتهي"
                else:
                    status = "❌ غير نشط"

                join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d') if subscriber['join_time'] else 'غير محدد'

                message += f"{i}. 👤 **{first_name}**\n"
                message += f"   🆔 `{subscriber['user_id']}`\n"
                message += f"   📱 @{username}\n"
                message += f"   📅 انضم: {join_date}\n"
                message += f"   📊 {status}\n"
                message += f"   🔄 اشتراكات: {subscriber['subscription_count']}\n"
                message += f"   💰 مدفوعات: {float(subscriber['total_paid']):.2f} جنيه\n\n"

            # أزرار الإجراءات لكل مشترك
            markup = types.InlineKeyboardMarkup(row_width=2)

            # أزرار حذف وحظر لكل مشترك
            for subscriber in subscribers:
                user_id = subscriber['user_id']
                first_name = subscriber['first_name'] or f"ID:{user_id}"
                if len(first_name) > 12:
                    first_name = first_name[:9] + "..."

                markup.add(
                    types.InlineKeyboardButton(f"👤 {first_name}", callback_data=f"view_subscriber_{user_id}"),
                    types.InlineKeyboardButton(f"🗑️ حذف", callback_data=f"confirm_delete_subscriber_{user_id}")
                )
                markup.add(
                    types.InlineKeyboardButton(f"➕ زيادة أيام", callback_data=f"add_days_subscriber_{user_id}"),
                    types.InlineKeyboardButton(f"➖ تقليل أيام", callback_data=f"reduce_days_subscriber_{user_id}")
                )
                markup.add(
                    types.InlineKeyboardButton(f"🚫 حظر", callback_data=f"ban_subscriber_{user_id}"),
                    types.InlineKeyboardButton(f"⏰ تمديد سريع", callback_data=f"quick_extend_subscriber_{user_id}")
                )

            # أزرار التنقل بين الصفحات
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_list_subscribers_{page-1}"))

            nav_buttons.append(types.InlineKeyboardButton(f"📄 {page}", callback_data="current_page"))

            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_list_subscribers_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            # أزرار إضافية
            markup.add(
                types.InlineKeyboardButton("🔍 بحث", callback_data="admin_search_subscriber"),
                types.InlineKeyboardButton("➕ إضافة مشترك", callback_data="admin_add_subscriber")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show subscribers list: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض قائمة المشتركين")

    def show_expiring_soon(self, chat_id, page=1, limit=10):
        """عرض المشتركين المنتهية صلاحيتهم قريباً"""
        try:
            logger.info(f"Admin {chat_id} requesting expiring soon subscribers, page {page}")
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                # جلب المشتركين المنتهية صلاحيتهم خلال 7 أيام
                cursor.execute(f"""
                    SELECT user_id, username, first_name, expire_time, subscription_count, total_paid
                    FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    AND expire_time <= %s
                    AND expire_time > %s
                    ORDER BY expire_time ASC
                    LIMIT %s OFFSET %s
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp()),
                    limit, offset
                ))

                subscribers = cursor.fetchall()

                # عدد المشتركين الإجمالي
                cursor.execute(f"""
                    SELECT COUNT(*) as total FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    AND expire_time <= %s
                    AND expire_time > %s
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp())
                ))
                total = cursor.fetchone()['total']

            if not subscribers:
                message = "✅ لا يوجد مشتركين ستنتهي صلاحيتهم قريباً"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"⚠️ المشتركين المنتهية قريباً (صفحة {page})\n\n"

            for subscriber in subscribers:
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                # حساب الأيام المتبقية
                days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d %H:%M')

                urgency = "🔴 عاجل" if days_left <= 1 else "🟡 قريباً" if days_left <= 3 else "🟢 عادي"

                message += f"👤 {first_name} (@{username})\n" \
                          f"🆔 {subscriber['user_id']}\n" \
                          f"⏰ ينتهي خلال: {days_left} {'يوم' if days_left == 1 else 'أيام'}\n" \
                          f"📅 تاريخ الانتهاء: {expire_date}\n" \
                          f"🚨 الأولوية: {urgency}\n" \
                          f"🔄 اشتراكات: {subscriber['subscription_count']}\n" \
                          f"💰 مدفوعات: {float(subscriber['total_paid']):.2f} جنيه\n\n"

            # أزرار التنقل والإجراءات
            markup = types.InlineKeyboardMarkup(row_width=2)

            # أزرار الإجراءات
            markup.add(
                types.InlineKeyboardButton("📧 تذكير جماعي", callback_data="admin_send_reminder_all"),
                types.InlineKeyboardButton("📢 رسالة مخصصة", callback_data="admin_broadcast_expiring")
            )

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_expiring_soon_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_expiring_soon_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))

            message += f"📊 إجمالي المنتهية قريباً: {total}"

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show expiring soon subscribers: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض المشتركين المنتهية قريباً")

    def show_expired_subscribers(self, chat_id, page=1, limit=10):
        """عرض المشتركين منتهية الصلاحية"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                # جلب المشتركين منتهية الصلاحية
                cursor.execute(f"""
                    SELECT user_id, username, first_name, expire_time, subscription_count, total_paid, is_active
                    FROM {self.subscribers_table}
                    WHERE (is_active = FALSE OR expire_time <= %s)
                    ORDER BY expire_time DESC
                    LIMIT %s OFFSET %s
                """, (int(datetime.now().timestamp()), limit, offset))

                subscribers = cursor.fetchall()

                # عدد المشتركين الإجمالي
                cursor.execute(f"""
                    SELECT COUNT(*) as total FROM {self.subscribers_table}
                    WHERE (is_active = FALSE OR expire_time <= %s)
                """, (int(datetime.now().timestamp()),))
                total = cursor.fetchone()['total']

            if not subscribers:
                message = "✅ لا يوجد مشتركين منتهية الصلاحية"
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))
                self.bot.send_message(chat_id, message, reply_markup=markup)
                return

            message = f"❌ المشتركين منتهية الصلاحية (صفحة {page})\n\n"

            for subscriber in subscribers:
                username = subscriber['username'] or 'غير محدد'
                first_name = subscriber['first_name'] or 'غير محدد'

                # حساب الأيام منذ الانتهاء
                if subscriber['expire_time']:
                    days_expired = max(0, int((datetime.now().timestamp() - subscriber['expire_time']) / 86400))
                    expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d %H:%M')

                    if days_expired == 0:
                        status = "⏰ انتهى اليوم"
                    elif days_expired == 1:
                        status = "📅 انتهى أمس"
                    else:
                        status = f"📅 انتهى منذ {days_expired} أيام"
                else:
                    expire_date = 'غير محدد'
                    status = "❌ غير نشط"

                # تحديد نوع الانتهاء
                if not subscriber['is_active']:
                    expiry_type = "🔴 تم إيقافه يدوياً"
                else:
                    expiry_type = "⏰ انتهى تلقائياً"

                message += f"👤 {first_name} (@{username})\n" \
                          f"🆔 {subscriber['user_id']}\n" \
                          f"📅 تاريخ الانتهاء: {expire_date}\n" \
                          f"📊 الحالة: {status}\n" \
                          f"🔄 نوع الانتهاء: {expiry_type}\n" \
                          f"💼 اشتراكات سابقة: {subscriber['subscription_count']}\n" \
                          f"💰 إجمالي مدفوعات: {float(subscriber['total_paid']):.2f} جنيه\n\n"

            # أزرار التنقل والإجراءات
            markup = types.InlineKeyboardMarkup(row_width=2)

            # أزرار الإجراءات
            markup.add(
                types.InlineKeyboardButton("🧹 تنظيف القديمة", callback_data="admin_cleanup_expired"),
                types.InlineKeyboardButton("📧 رسالة للمنتهية", callback_data="admin_broadcast_expired")
            )

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق",
                                                            callback_data=f"admin_expired_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي",
                                                            callback_data=f"admin_expired_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_subscribers"))

            message += f"📊 إجمالي منتهية الصلاحية: {total}"

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show expired subscribers: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض المشتركين منتهية الصلاحية")

    def cleanup_expired_subscribers(self, chat_id):
        """تنظيف المشتركين منتهية الصلاحية"""
        try:
            with self.db.get_db_cursor() as cursor:
                # البحث عن المشتركين منتهية الصلاحية منذ أكثر من 30 يوم
                cursor.execute(f"""
                    SELECT user_id, username, first_name, expire_time
                    FROM {self.subscribers_table}
                    WHERE (is_active = FALSE OR expire_time <= %s)
                    AND expire_time <= %s
                """, (
                    int(datetime.now().timestamp()),
                    int((datetime.now() - timedelta(days=30)).timestamp())
                ))

                old_subscribers = cursor.fetchall()

                if not old_subscribers:
                    self.bot.send_message(chat_id, "✅ لا يوجد مشتركين قدامى للتنظيف")
                    return

                # حذف المشتركين القدامى
                cursor.execute(f"""
                    DELETE FROM {self.subscribers_table}
                    WHERE (is_active = FALSE OR expire_time <= %s)
                    AND expire_time <= %s
                """, (
                    int(datetime.now().timestamp()),
                    int((datetime.now() - timedelta(days=30)).timestamp())
                ))

                deleted_count = cursor.rowcount

            # إرسال تقرير التنظيف
            message = f"🧹 تم تنظيف قاعدة البيانات\n\n" \
                     f"🗑️ تم حذف {deleted_count} مشترك قديم\n" \
                     f"📅 المشتركين المحذوفين انتهت صلاحيتهم منذ أكثر من 30 يوم\n\n"

            if old_subscribers:
                message += "📋 المشتركين المحذوفين:\n"
                for i, sub in enumerate(old_subscribers[:10], 1):  # عرض أول 10 فقط
                    expire_date = datetime.fromtimestamp(sub['expire_time']).strftime('%Y-%m-%d') if sub['expire_time'] else 'غير محدد'
                    message += f"{i}. {sub['first_name']} (@{sub['username'] or 'غير محدد'}) - انتهى: {expire_date}\n"

                if len(old_subscribers) > 10:
                    message += f"... و {len(old_subscribers) - 10} آخرين\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "cleanup_expired", f"Cleaned up {deleted_count} expired subscribers")

        except Exception as e:
            logger.error(f"Failed to cleanup expired subscribers: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تنظيف المشتركين منتهية الصلاحية")

    def show_subscriber_details(self, chat_id, user_id):
        """عرض تفاصيل مشترك معين"""
        try:
            # الحصول على معلومات المشترك
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, username, first_name, is_active, expire_time, join_time,
                           subscription_count, total_paid, last_payment_date, payment_method
                    FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على المشترك {user_id}")
                    return

                # الحصول على آخر الأنشطة
                cursor.execute(f"""
                    SELECT action, details, created_at
                    FROM {self.activity_logs_table}
                    WHERE user_id = %s
                    ORDER BY created_at DESC
                    LIMIT 5
                """, (user_id,))

                recent_activities = cursor.fetchall()

            message = f"👤 **تفاصيل المشترك**\n\n"

            # معلومات أساسية
            message += f"📋 **المعلومات الأساسية:**\n"
            message += f"🆔 المعرف: `{subscriber['user_id']}`\n"
            message += f"📝 الاسم: {subscriber['first_name'] or 'غير محدد'}\n"
            message += f"📱 اسم المستخدم: @{subscriber['username'] or 'غير محدد'}\n"

            # حالة الاشتراك
            if subscriber['is_active'] and subscriber['expire_time']:
                days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d %H:%M')

                if days_left > 7:
                    status = f"✅ نشط ({days_left} يوم متبقي)"
                elif days_left > 3:
                    status = f"🟡 ينتهي قريباً ({days_left} أيام)"
                elif days_left > 0:
                    status = f"🔴 ينتهي خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}"
                else:
                    status = "⚠️ منتهي"

                message += f"📊 الحالة: {status}\n"
                message += f"⏰ ينتهي في: {expire_date}\n"
            else:
                message += f"📊 الحالة: ❌ غير نشط\n"

            # معلومات الانضمام والمدفوعات
            join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d %H:%M') if subscriber['join_time'] else 'غير محدد'
            message += f"📅 تاريخ الانضمام: {join_date}\n"
            message += f"🔄 عدد الاشتراكات: {subscriber['subscription_count']}\n"
            message += f"💰 إجمالي المدفوعات: {float(subscriber['total_paid']):.2f} جنيه\n"

            if subscriber['last_payment_date']:
                last_payment = datetime.fromtimestamp(subscriber['last_payment_date']).strftime('%Y-%m-%d %H:%M')
                message += f"💳 آخر دفعة: {last_payment}\n"

            if subscriber['payment_method']:
                message += f"💳 طريقة الدفع: {subscriber['payment_method']}\n"

            # آخر الأنشطة
            if recent_activities:
                message += f"\n📋 **آخر الأنشطة:**\n"
                for activity in recent_activities:
                    activity_date = activity['created_at'].strftime('%m-%d %H:%M')
                    message += f"• {activity['action']}: {activity['details'] or 'لا توجد تفاصيل'}\n"
                    message += f"  ⏰ {activity_date}\n"

            # أزرار الإجراءات
            markup = types.InlineKeyboardMarkup(row_width=2)

            if subscriber['is_active']:
                markup.add(
                    types.InlineKeyboardButton("⏰ تمديد الاشتراك", callback_data=f"extend_subscriber_{user_id}"),
                    types.InlineKeyboardButton("⏸️ إيقاف الاشتراك", callback_data=f"deactivate_subscriber_{user_id}")
                )
            else:
                markup.add(
                    types.InlineKeyboardButton("✅ تفعيل الاشتراك", callback_data=f"activate_subscriber_{user_id}"),
                    types.InlineKeyboardButton("🔄 تجديد الاشتراك", callback_data=f"renew_subscriber_{user_id}")
                )

            markup.add(
                types.InlineKeyboardButton("🗑️ حذف المشترك", callback_data=f"confirm_delete_subscriber_{user_id}"),
                types.InlineKeyboardButton("🚫 حظر المشترك", callback_data=f"ban_subscriber_{user_id}")
            )
            markup.add(
                types.InlineKeyboardButton("💬 راسل المشترك", url=f"tg://user?id={user_id}"),
                types.InlineKeyboardButton("📊 المزيد من التفاصيل", callback_data=f"security_user_details_{user_id}")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة المشتركين", callback_data="admin_list_subscribers"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show subscriber details: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تفاصيل المشترك")

    def confirm_delete_subscriber(self, chat_id, user_id):
        """تأكيد حذف المشترك"""
        try:
            # الحصول على معلومات المشترك
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT first_name, username, total_paid, subscription_count
                    FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على المشترك {user_id}")
                    return

            message = f"⚠️ **تأكيد حذف المشترك**\n\n"
            message += f"👤 **المشترك:**\n"
            message += f"📝 الاسم: {subscriber['first_name'] or 'غير محدد'}\n"
            message += f"📱 اسم المستخدم: @{subscriber['username'] or 'غير محدد'}\n"
            message += f"🆔 المعرف: `{user_id}`\n\n"
            message += f"📊 **البيانات التي ستُحذف:**\n"
            message += f"💰 إجمالي المدفوعات: {float(subscriber['total_paid']):.2f} جنيه\n"
            message += f"🔄 عدد الاشتراكات: {subscriber['subscription_count']}\n"
            message += f"📋 جميع سجلات النشاط\n\n"
            message += f"⚠️ **تحذير:** هذا الإجراء لا يمكن التراجع عنه!\n"
            message += f"❓ هل أنت متأكد من حذف هذا المشترك؟"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("✅ نعم، احذف المشترك", callback_data=f"delete_subscriber_confirmed_{user_id}"),
                types.InlineKeyboardButton("❌ إلغاء", callback_data=f"view_subscriber_{user_id}")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show delete confirmation: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تأكيد الحذف")

    def delete_subscriber(self, chat_id, user_id):
        """حذف المشترك نهائياً"""
        try:
            # الحصول على معلومات المشترك قبل الحذف
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT first_name, username, total_paid
                    FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على المشترك {user_id}")
                    return

                # حذف المشترك من جدول المشتركين
                cursor.execute(f"""
                    DELETE FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                deleted_count = cursor.rowcount

                # حذف سجلات النشاط (اختياري - يمكن الاحتفاظ بها للمراجعة)
                # cursor.execute(f"""
                #     DELETE FROM {self.activity_logs_table}
                #     WHERE user_id = %s
                # """, (user_id,))

            if deleted_count > 0:
                message = f"✅ **تم حذف المشترك بنجاح!**\n\n"
                message += f"👤 **المشترك المحذوف:**\n"
                message += f"📝 الاسم: {subscriber['first_name'] or 'غير محدد'}\n"
                message += f"📱 اسم المستخدم: @{subscriber['username'] or 'غير محدد'}\n"
                message += f"🆔 المعرف: `{user_id}`\n"
                message += f"💰 إجمالي مدفوعاته: {float(subscriber['total_paid']):.2f} جنيه\n\n"
                message += f"📝 تم تسجيل هذا الإجراء في سجل النشاط"

                # تسجيل النشاط
                self.db.log_activity(chat_id, "delete_subscriber", f"Deleted subscriber {user_id} ({subscriber['first_name']})")
            else:
                message = "❌ فشل في حذف المشترك"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers"),
                types.InlineKeyboardButton("📊 إحصائيات", callback_data="admin_subscribers_stats")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to delete subscriber: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في حذف المشترك")

    def extend_subscription(self, admin_id, user_id, days):
        """تمديد اشتراك مشترك"""
        try:
            # الحصول على معلومات المشترك الحالية
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT first_name, username, expire_time, is_active
                    FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    self.bot.send_message(admin_id, f"❌ لم يتم العثور على المشترك {user_id}")
                    return False

                # حساب تاريخ الانتهاء الجديد
                current_expire = subscriber['expire_time'] or int(datetime.now().timestamp())
                if current_expire < int(datetime.now().timestamp()):
                    # إذا كان الاشتراك منتهي، ابدأ من الآن
                    new_expire = int(datetime.now().timestamp()) + (days * 86400)
                else:
                    # إذا كان نشط، أضف الأيام للتاريخ الحالي
                    new_expire = current_expire + (days * 86400)

                # تحديث الاشتراك
                cursor.execute(f"""
                    UPDATE {self.subscribers_table}
                    SET expire_time = %s, is_active = TRUE, updated_at = %s
                    WHERE user_id = %s
                """, (new_expire, datetime.now(), user_id))

                updated_count = cursor.rowcount

            if updated_count > 0:
                new_expire_date = datetime.fromtimestamp(new_expire).strftime('%Y-%m-%d %H:%M')

                message = f"✅ **تم تمديد الاشتراك بنجاح!**\n\n"
                message += f"👤 **المشترك:**\n"
                message += f"📝 الاسم: {subscriber['first_name'] or 'غير محدد'}\n"
                message += f"📱 اسم المستخدم: @{subscriber['username'] or 'غير محدد'}\n"
                message += f"🆔 المعرف: `{user_id}`\n\n"
                message += f"⏰ **التمديد:**\n"
                message += f"📅 تم إضافة: {days} {'يوم' if days == 1 else 'أيام'}\n"
                message += f"📅 ينتهي الآن في: {new_expire_date}\n\n"
                message += f"📝 تم تسجيل هذا الإجراء في سجل النشاط"

                # إشعار المشترك بالتمديد
                try:
                    subscriber_message = f"🎉 **تم تمديد اشتراكك!**\n\n"
                    subscriber_message += f"⏰ تم إضافة {days} {'يوم' if days == 1 else 'أيام'} لاشتراكك\n"
                    subscriber_message += f"📅 اشتراكك ينتهي الآن في: {new_expire_date}\n\n"
                    subscriber_message += f"🎯 استمتع بالمحتوى الحصري!\n"
                    subscriber_message += f"💎 شكراً لك على ثقتك بنا"

                    self.bot.send_message(user_id, subscriber_message, parse_mode='Markdown')
                except Exception as e:
                    logger.warning(f"Failed to notify subscriber {user_id} of extension: {e}")

                # تسجيل النشاط
                self.db.log_activity(admin_id, "extend_subscription", f"Extended subscription for {user_id} by {days} days")

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("👤 عرض المشترك", callback_data=f"view_subscriber_{user_id}"),
                    types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers")
                )
                markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

                self.bot.send_message(admin_id, message, reply_markup=markup, parse_mode='Markdown')
                return True
            else:
                self.bot.send_message(admin_id, "❌ فشل في تمديد الاشتراك")
                return False

        except Exception as e:
            logger.error(f"Failed to extend subscription: {e}")
            self.bot.send_message(admin_id, "❌ حدث خطأ في تمديد الاشتراك")
            return False

    def show_files_management(self, chat_id):
        """عرض قائمة إدارة الملفات"""
        try:
            markup = types.InlineKeyboardMarkup(row_width=2)

            markup.add(
                types.InlineKeyboardButton("📁 قائمة الملفات", callback_data="admin_files_list"),
                types.InlineKeyboardButton("➕ إضافة ملف", callback_data="admin_add_file")
            )

            markup.add(
                types.InlineKeyboardButton("✏️ تعديل ملف", callback_data="admin_edit_file"),
                types.InlineKeyboardButton("🗑️ حذف ملف", callback_data="admin_delete_file")
            )

            markup.add(
                types.InlineKeyboardButton("📊 إحصائيات التحميل", callback_data="admin_download_stats"),
                types.InlineKeyboardButton("📋 سجل التحميلات", callback_data="admin_download_logs")
            )

            markup.add(
                types.InlineKeyboardButton("🔧 إعدادات التحميل", callback_data="admin_download_settings"),
                types.InlineKeyboardButton("🧹 تنظيف الروابط", callback_data="admin_cleanup_links")
            )

            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            message = "📁 **إدارة ملفات التحميل**\n\n"
            message += "🎮 إدارة ملفات PUBG Mobile Portable\n"
            message += "📊 مراقبة التحميلات والإحصائيات\n"
            message += "🔧 تحكم في إعدادات النظام\n\n"
            message += "اختر العملية التي تريد تنفيذها:"

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show files management: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إدارة الملفات")

    def show_files_list(self, chat_id, page=1, limit=5):
        """عرض قائمة الملفات المتاحة"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                # الحصول على الملفات مع إحصائيات التحميل
                cursor.execute("""
                    SELECT df.*,
                           COUNT(dh.id) as download_count,
                           COUNT(DISTINCT dh.user_id) as unique_downloaders
                    FROM download_files df
                    LEFT JOIN download_history dh ON df.id = dh.file_id AND dh.download_status = 'completed'
                    GROUP BY df.id
                    ORDER BY df.created_at DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                files = cursor.fetchall()

                # عدد الملفات الإجمالي
                cursor.execute("SELECT COUNT(*) as total FROM download_files")
                total = cursor.fetchone()['total']

            if not files:
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("➕ إضافة ملف جديد", callback_data="admin_add_file"),
                    types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files")
                )

                self.bot.send_message(
                    chat_id,
                    "📁 **قائمة الملفات فارغة**\n\n"
                    "لا توجد ملفات مضافة حالياً\n"
                    "💡 ابدأ بإضافة ملف جديد",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return

            message = f"📁 **قائمة الملفات** (صفحة {page})\n\n"
            message += f"📊 **الإحصائيات:**\n"
            message += f"📄 إجمالي الملفات: {total}\n"
            message += f"📋 عرض: {len(files)} ملف\n\n"

            markup = types.InlineKeyboardMarkup(row_width=1)

            for i, file_info in enumerate(files, 1):
                file_size = self._format_file_size(file_info['file_size'])
                status_icon = "✅" if file_info['is_active'] else "❌"

                message += f"{i}. {status_icon} **{file_info['file_name']}**\n"
                message += f"   📊 الحجم: {file_size}\n"
                message += f"   📥 تحميلات: {file_info['download_count']} (من {file_info['unique_downloaders']} مستخدم)\n"
                message += f"   📂 الفئة: {file_info['category']}\n"
                message += f"   📅 أُضيف: {file_info['created_at'].strftime('%Y-%m-%d')}\n"

                if file_info['description']:
                    desc = file_info['description'][:50] + "..." if len(file_info['description']) > 50 else file_info['description']
                    message += f"   📝 الوصف: {desc}\n"

                message += "\n"

                # أزرار إدارة الملف
                markup.add(
                    types.InlineKeyboardButton(
                        f"⚙️ إدارة {file_info['file_name'][:20]}{'...' if len(file_info['file_name']) > 20 else ''}",
                        callback_data=f"admin_manage_file_{file_info['id']}"
                    )
                )

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق", callback_data=f"admin_files_list_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي", callback_data=f"admin_files_list_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(
                types.InlineKeyboardButton("➕ إضافة ملف جديد", callback_data="admin_add_file"),
                types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show files list: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض قائمة الملفات")

    def show_file_management(self, chat_id, file_id):
        """عرض إدارة ملف معين"""
        try:
            # الحصول على معلومات الملف
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT df.*,
                           COUNT(dh.id) as download_count,
                           COUNT(DISTINCT dh.user_id) as unique_downloaders,
                           MAX(dh.created_at) as last_download
                    FROM download_files df
                    LEFT JOIN download_history dh ON df.id = dh.file_id AND dh.download_status = 'completed'
                    WHERE df.id = %s
                    GROUP BY df.id
                """, (file_id,))

                file_info = cursor.fetchone()

                if not file_info:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على الملف {file_id}")
                    return

                # الحصول على آخر التحميلات
                cursor.execute("""
                    SELECT dh.created_at, s.first_name, s.username
                    FROM download_history dh
                    LEFT JOIN subscribers s ON dh.user_id = s.user_id
                    WHERE dh.file_id = %s AND dh.download_status = 'completed'
                    ORDER BY dh.created_at DESC
                    LIMIT 5
                """, (file_id,))

                recent_downloads = cursor.fetchall()

            file_size = self._format_file_size(file_info['file_size'])
            status_text = "✅ نشط" if file_info['is_active'] else "❌ معطل"

            message = f"⚙️ **إدارة الملف**\n\n"
            message += f"📄 **الملف:** {file_info['file_name']}\n"
            message += f"📊 **الحجم:** {file_size}\n"
            message += f"📂 **الفئة:** {file_info['category']}\n"
            message += f"🔄 **الحالة:** {status_text}\n"
            message += f"📅 **تاريخ الإضافة:** {file_info['created_at'].strftime('%Y-%m-%d %H:%M')}\n"

            if file_info['description']:
                message += f"📝 **الوصف:** {file_info['description']}\n"

            message += f"\n📊 **إحصائيات التحميل:**\n"
            message += f"📥 إجمالي التحميلات: {file_info['download_count']}\n"
            message += f"👥 مستخدمين فريدين: {file_info['unique_downloaders']}\n"

            if file_info['last_download']:
                last_download = file_info['last_download'].strftime('%Y-%m-%d %H:%M')
                message += f"⏰ آخر تحميل: {last_download}\n"

            if recent_downloads:
                message += f"\n📋 **آخر التحميلات:**\n"
                for download in recent_downloads:
                    name = download['first_name'] or 'غير محدد'
                    username = download['username'] or 'غير محدد'
                    date = download['created_at'].strftime('%m-%d %H:%M')
                    message += f"• {name} (@{username}) - {date}\n"

            markup = types.InlineKeyboardMarkup(row_width=2)

            # أزرار التحكم
            if file_info['is_active']:
                markup.add(types.InlineKeyboardButton("⏸️ تعطيل الملف", callback_data=f"admin_disable_file_{file_id}"))
            else:
                markup.add(types.InlineKeyboardButton("▶️ تفعيل الملف", callback_data=f"admin_enable_file_{file_id}"))

            markup.add(
                types.InlineKeyboardButton("✏️ تعديل المعلومات", callback_data=f"admin_edit_file_info_{file_id}"),
                types.InlineKeyboardButton("📊 تفاصيل التحميل", callback_data=f"admin_file_download_details_{file_id}")
            )

            markup.add(
                types.InlineKeyboardButton("🗑️ حذف الملف", callback_data=f"admin_confirm_delete_file_{file_id}"),
                types.InlineKeyboardButton("🔄 إعادة تعيين الإحصائيات", callback_data=f"admin_reset_file_stats_{file_id}")
            )

            markup.add(
                types.InlineKeyboardButton("📁 قائمة الملفات", callback_data="admin_files_list"),
                types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show file management: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إدارة الملف")

    def _format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def add_new_file(self, admin_id, file_name, file_path, file_size, category, description):
        """إضافة ملف جديد للتحميل"""
        try:
            # التحقق من عدم وجود ملف بنفس الاسم
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) as count FROM download_files
                    WHERE file_name = %s
                """, (file_name,))

                existing = cursor.fetchone()['count']

                if existing > 0:
                    self.bot.send_message(admin_id,
                        f"❌ **خطأ في الإضافة**\n\n"
                        f"يوجد ملف بنفس الاسم: {file_name}\n"
                        f"💡 يرجى اختيار اسم مختلف",
                        parse_mode='Markdown'
                    )
                    return False

                # إضافة الملف الجديد
                cursor.execute("""
                    INSERT INTO download_files
                    (file_name, file_path, file_size, category, description, is_active)
                    VALUES (%s, %s, %s, %s, %s, TRUE)
                """, (file_name, file_path, file_size, category, description))

                file_id = cursor.lastrowid

            # تنسيق حجم الملف للعرض
            file_size_formatted = self._format_file_size(file_size)

            message = f"✅ **تم إضافة الملف بنجاح!**\n\n"
            message += f"📄 **الاسم:** {file_name}\n"
            message += f"📂 **المسار:** {file_path}\n"
            message += f"📊 **الحجم:** {file_size_formatted}\n"
            message += f"📂 **الفئة:** {category}\n"
            message += f"📝 **الوصف:** {description}\n"
            message += f"🆔 **معرف الملف:** {file_id}\n\n"
            message += f"🎯 الملف متاح الآن للمشتركين!"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("⚙️ إدارة الملف", callback_data=f"admin_manage_file_{file_id}"),
                types.InlineKeyboardButton("📁 قائمة الملفات", callback_data="admin_files_list")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

            self.bot.send_message(admin_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(admin_id, "add_file", f"Added file: {file_name} ({file_size_formatted})")

            return True

        except Exception as e:
            logger.error(f"Failed to add new file: {e}")
            self.bot.send_message(admin_id,
                f"❌ **حدث خطأ في إضافة الملف**\n\n"
                f"تفاصيل الخطأ: {str(e)}\n"
                f"💡 يرجى المحاولة مرة أخرى",
                parse_mode='Markdown'
            )
            return False

    def show_download_statistics(self, chat_id):
        """عرض إحصائيات التحميل المفصلة"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات عامة
                cursor.execute("""
                    SELECT
                        COUNT(DISTINCT df.id) as total_files,
                        COUNT(DISTINCT CASE WHEN df.is_active = TRUE THEN df.id END) as active_files,
                        COUNT(dh.id) as total_downloads,
                        COUNT(DISTINCT dh.user_id) as unique_downloaders
                    FROM download_files df
                    LEFT JOIN download_history dh ON df.id = dh.file_id AND dh.download_status = 'completed'
                """)

                general_stats = cursor.fetchone()

                # إحصائيات اليوم
                cursor.execute("""
                    SELECT COUNT(*) as today_downloads
                    FROM download_history
                    WHERE DATE(created_at) = CURRENT_DATE
                    AND download_status = 'completed'
                """)

                today_stats = cursor.fetchone()

                # أكثر الملفات تحميلاً
                cursor.execute("""
                    SELECT df.file_name, COUNT(dh.id) as download_count
                    FROM download_files df
                    LEFT JOIN download_history dh ON df.id = dh.file_id AND dh.download_status = 'completed'
                    GROUP BY df.id, df.file_name
                    ORDER BY download_count DESC
                    LIMIT 5
                """)

                top_files = cursor.fetchall()

                # إحصائيات الروابط المؤقتة
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_links,
                        COUNT(CASE WHEN is_used = TRUE THEN 1 END) as used_links,
                        COUNT(CASE WHEN expires_at > NOW() AND is_used = FALSE THEN 1 END) as active_links
                    FROM download_links
                """)

                links_stats = cursor.fetchone()

            message = f"📊 **إحصائيات التحميل المفصلة**\n\n"

            # الإحصائيات العامة
            message += f"📁 **الملفات:**\n"
            message += f"• إجمالي الملفات: {general_stats['total_files']}\n"
            message += f"• الملفات النشطة: {general_stats['active_files']}\n"
            message += f"• الملفات المعطلة: {general_stats['total_files'] - general_stats['active_files']}\n\n"

            message += f"📥 **التحميلات:**\n"
            message += f"• إجمالي التحميلات: {general_stats['total_downloads']}\n"
            message += f"• مستخدمين فريدين: {general_stats['unique_downloaders']}\n"
            message += f"• تحميلات اليوم: {today_stats['today_downloads']}\n\n"

            message += f"🔗 **الروابط المؤقتة:**\n"
            message += f"• إجمالي الروابط: {links_stats['total_links']}\n"
            message += f"• روابط مستخدمة: {links_stats['used_links']}\n"
            message += f"• روابط نشطة: {links_stats['active_links']}\n\n"

            if top_files:
                message += f"🏆 **أكثر الملفات تحميلاً:**\n"
                for i, file_info in enumerate(top_files, 1):
                    file_name = file_info['file_name'][:30] + "..." if len(file_info['file_name']) > 30 else file_info['file_name']
                    message += f"{i}. {file_name} ({file_info['download_count']} تحميل)\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📋 سجل التحميلات", callback_data="admin_download_logs"),
                types.InlineKeyboardButton("🧹 تنظيف الروابط", callback_data="admin_cleanup_links")
            )
            markup.add(
                types.InlineKeyboardButton("📁 قائمة الملفات", callback_data="admin_files_list"),
                types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show download statistics: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إحصائيات التحميل")

    def show_download_logs(self, chat_id, page=1, limit=10):
        """عرض سجل التحميلات"""
        try:
            with self.db.get_db_cursor() as cursor:
                offset = (page - 1) * limit

                # الحصول على سجل التحميلات
                cursor.execute("""
                    SELECT dh.*, df.file_name, s.first_name, s.username
                    FROM download_history dh
                    LEFT JOIN download_files df ON dh.file_id = df.id
                    LEFT JOIN subscribers s ON dh.user_id = s.user_id
                    ORDER BY dh.created_at DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                logs = cursor.fetchall()

                # عدد السجلات الإجمالي
                cursor.execute("SELECT COUNT(*) as total FROM download_history")
                total = cursor.fetchone()['total']

            if not logs:
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

                self.bot.send_message(
                    chat_id,
                    "📋 **سجل التحميلات فارغ**\n\n"
                    "لا توجد عمليات تحميل مسجلة حالياً",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return

            message = f"📋 **سجل التحميلات** (صفحة {page})\n\n"
            message += f"📊 **الإحصائيات:**\n"
            message += f"📄 إجمالي السجلات: {total}\n"
            message += f"📋 عرض: {len(logs)} سجل\n\n"

            for i, log in enumerate(logs, 1):
                status_icon = {
                    'completed': '✅',
                    'failed': '❌',
                    'pending': '⏳',
                    'expired': '⏰'
                }.get(log['download_status'], '❓')

                user_name = log['first_name'] or 'غير محدد'
                username = log['username'] or 'غير محدد'
                file_name = log['file_name'] or f"ملف {log['file_id']}"
                date = log['created_at'].strftime('%Y-%m-%d %H:%M')

                message += f"{i}. {status_icon} **{file_name}**\n"
                message += f"   👤 {user_name} (@{username})\n"
                message += f"   📅 {date}\n"
                message += f"   📊 الحالة: {log['download_status']}\n\n"

            markup = types.InlineKeyboardMarkup(row_width=2)

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق", callback_data=f"admin_download_logs_{page-1}"))
            if page * limit < total:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي", callback_data=f"admin_download_logs_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            markup.add(
                types.InlineKeyboardButton("📊 إحصائيات التحميل", callback_data="admin_download_stats"),
                types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show download logs: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض سجل التحميلات")

    def show_download_settings(self, chat_id):
        """عرض إعدادات التحميل"""
        try:
            message = f"🔧 **إعدادات نظام التحميل**\n\n"
            message += f"⚙️ **الإعدادات الحالية:**\n"
            message += f"• مدة صلاحية الروابط: 24 ساعة\n"
            message += f"• عدد التحميلات المسموحة: 1 لكل ملف\n"
            message += f"• حد حجم الملف: غير محدود\n"
            message += f"• التحقق من الاشتراك: مُفعل\n"
            message += f"• تسجيل التحميلات: مُفعل\n\n"
            message += f"🔐 **الأمان:**\n"
            message += f"• روابط مؤقتة آمنة\n"
            message += f"• منع المشاركة\n"
            message += f"• تتبع المستخدمين\n\n"
            message += f"💡 **ملاحظة:** الإعدادات محددة مسبقاً لضمان الأمان"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🧹 تنظيف الروابط", callback_data="admin_cleanup_links"),
                types.InlineKeyboardButton("📊 إحصائيات", callback_data="admin_download_stats")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show download settings: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إعدادات التحميل")

    def cleanup_download_links(self, chat_id):
        """تنظيف روابط التحميل المنتهية الصلاحية"""
        try:
            with self.db.get_db_cursor() as cursor:
                # حذف الروابط المنتهية الصلاحية
                cursor.execute("""
                    DELETE FROM download_links
                    WHERE expires_at < NOW() OR is_used = TRUE
                """)

                deleted_count = cursor.rowcount

                # حذف سجلات التحميل القديمة (أكثر من 30 يوم)
                cursor.execute("""
                    DELETE FROM download_history
                    WHERE created_at < NOW() - INTERVAL '30 days'
                """)

                logs_deleted = cursor.rowcount

            message = f"🧹 **تم تنظيف روابط التحميل بنجاح!**\n\n"
            message += f"🗑️ **النتائج:**\n"
            message += f"• روابط محذوفة: {deleted_count}\n"
            message += f"• سجلات قديمة محذوفة: {logs_deleted}\n\n"
            message += f"✅ تم توفير مساحة في قاعدة البيانات\n"
            message += f"🔐 تم تحسين أمان النظام"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📊 إحصائيات التحميل", callback_data="admin_download_stats"),
                types.InlineKeyboardButton("📋 سجل التحميلات", callback_data="admin_download_logs")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(chat_id, "cleanup_download_links", f"Deleted {deleted_count} links and {logs_deleted} old logs")

        except Exception as e:
            logger.error(f"Failed to cleanup download links: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تنظيف روابط التحميل")

    def toggle_file_status(self, chat_id, file_id, is_active):
        """تفعيل أو تعطيل ملف"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute("""
                    UPDATE download_files
                    SET is_active = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (is_active, file_id))

                if cursor.rowcount == 0:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على الملف {file_id}")
                    return

                # الحصول على اسم الملف
                cursor.execute("SELECT file_name FROM download_files WHERE id = %s", (file_id,))
                file_info = cursor.fetchone()
                file_name = file_info['file_name'] if file_info else f"ملف {file_id}"

            status_text = "تفعيل" if is_active else "تعطيل"
            status_icon = "✅" if is_active else "❌"

            message = f"{status_icon} **تم {status_text} الملف بنجاح!**\n\n"
            message += f"📄 **الملف:** {file_name}\n"
            message += f"📊 **الحالة الجديدة:** {'مُفعل' if is_active else 'مُعطل'}\n\n"

            if is_active:
                message += f"🎯 الملف متاح الآن للمشتركين"
            else:
                message += f"⏸️ الملف غير متاح للتحميل حالياً"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("⚙️ إدارة الملف", callback_data=f"admin_manage_file_{file_id}"),
                types.InlineKeyboardButton("📁 قائمة الملفات", callback_data="admin_files_list")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(chat_id, "toggle_file_status", f"{'Enabled' if is_active else 'Disabled'} file {file_id}")

        except Exception as e:
            logger.error(f"Failed to toggle file status: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تغيير حالة الملف")

    def edit_file_info(self, chat_id, file_id):
        """تعديل معلومات ملف"""
        try:
            # التحقق من وجود الملف
            with self.db.get_db_cursor() as cursor:
                cursor.execute("SELECT * FROM download_files WHERE id = %s", (file_id,))
                file_info = cursor.fetchone()

                if not file_info:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على الملف {file_id}")
                    return

            message = f"✏️ **تعديل معلومات الملف**\n\n"
            message += f"📄 **الملف الحالي:** {file_info['file_name']}\n"
            message += f"📂 **المسار:** {file_info['file_path']}\n"
            message += f"📂 **الفئة:** {file_info['category']}\n"
            message += f"📝 **الوصف:** {file_info['description']}\n\n"
            message += f"💡 **ملاحظة:** حالياً يمكن فقط تفعيل/تعطيل الملف\n"
            message += f"🔧 المزيد من خيارات التعديل ستتوفر قريباً"

            markup = types.InlineKeyboardMarkup()
            if file_info['is_active']:
                markup.add(types.InlineKeyboardButton("⏸️ تعطيل الملف", callback_data=f"admin_disable_file_{file_id}"))
            else:
                markup.add(types.InlineKeyboardButton("▶️ تفعيل الملف", callback_data=f"admin_enable_file_{file_id}"))

            markup.add(
                types.InlineKeyboardButton("⚙️ إدارة الملف", callback_data=f"admin_manage_file_{file_id}"),
                types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to edit file info: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تعديل معلومات الملف")

    def show_file_download_details(self, chat_id, file_id):
        """عرض تفاصيل تحميل ملف معين"""
        try:
            with self.db.get_db_cursor() as cursor:
                # معلومات الملف
                cursor.execute("SELECT * FROM download_files WHERE id = %s", (file_id,))
                file_info = cursor.fetchone()

                if not file_info:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على الملف {file_id}")
                    return

                # إحصائيات التحميل
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_downloads,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as successful_downloads,
                        COUNT(CASE WHEN download_status = 'failed' THEN 1 END) as failed_downloads,
                        MAX(created_at) as last_download
                    FROM download_history
                    WHERE file_id = %s
                """, (file_id,))

                stats = cursor.fetchone()

                # آخر 10 تحميلات
                cursor.execute("""
                    SELECT dh.*, s.first_name, s.username
                    FROM download_history dh
                    LEFT JOIN subscribers s ON dh.user_id = s.user_id
                    WHERE dh.file_id = %s
                    ORDER BY dh.created_at DESC
                    LIMIT 10
                """, (file_id,))

                recent_downloads = cursor.fetchall()

            message = f"📊 **تفاصيل تحميل الملف**\n\n"
            message += f"📄 **الملف:** {file_info['file_name']}\n"
            message += f"📊 **الحجم:** {self._format_file_size(file_info['file_size'])}\n"
            message += f"📂 **الفئة:** {file_info['category']}\n"
            message += f"📅 **تاريخ الإضافة:** {file_info['created_at'].strftime('%Y-%m-%d')}\n\n"

            message += f"📈 **إحصائيات التحميل:**\n"
            message += f"📥 إجمالي المحاولات: {stats['total_downloads']}\n"
            message += f"✅ تحميلات ناجحة: {stats['successful_downloads']}\n"
            message += f"❌ تحميلات فاشلة: {stats['failed_downloads']}\n"
            message += f"👥 مستخدمين فريدين: {stats['unique_users']}\n"

            if stats['last_download']:
                message += f"⏰ آخر تحميل: {stats['last_download'].strftime('%Y-%m-%d %H:%M')}\n"

            if recent_downloads:
                message += f"\n📋 **آخر التحميلات:**\n"
                for download in recent_downloads[:5]:
                    status_icon = '✅' if download['download_status'] == 'completed' else '❌'
                    user_name = download['first_name'] or 'غير محدد'
                    date = download['created_at'].strftime('%m-%d %H:%M')
                    message += f"• {status_icon} {user_name} - {date}\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("⚙️ إدارة الملف", callback_data=f"admin_manage_file_{file_id}"),
                types.InlineKeyboardButton("📋 سجل التحميلات", callback_data="admin_download_logs")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show file download details: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تفاصيل التحميل")

    def confirm_delete_file(self, chat_id, file_id):
        """تأكيد حذف ملف"""
        try:
            # التحقق من وجود الملف
            with self.db.get_db_cursor() as cursor:
                cursor.execute("SELECT file_name FROM download_files WHERE id = %s", (file_id,))
                file_info = cursor.fetchone()

                if not file_info:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على الملف {file_id}")
                    return

            message = f"🗑️ **تأكيد حذف الملف**\n\n"
            message += f"📄 **الملف:** {file_info['file_name']}\n\n"
            message += f"⚠️ **تحذير:**\n"
            message += f"• سيتم حذف الملف نهائياً\n"
            message += f"• سيتم حذف جميع سجلات التحميل المرتبطة\n"
            message += f"• لا يمكن التراجع عن هذا الإجراء\n\n"
            message += f"❓ هل أنت متأكد من الحذف؟"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("✅ نعم، احذف الملف", callback_data=f"admin_delete_file_confirmed_{file_id}"),
                types.InlineKeyboardButton("❌ إلغاء", callback_data=f"admin_manage_file_{file_id}")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to confirm delete file: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تأكيد حذف الملف")

    def delete_file_confirmed(self, chat_id, file_id):
        """حذف ملف مؤكد"""
        try:
            with self.db.get_db_cursor() as cursor:
                # الحصول على معلومات الملف قبل الحذف
                cursor.execute("SELECT file_name FROM download_files WHERE id = %s", (file_id,))
                file_info = cursor.fetchone()

                if not file_info:
                    self.bot.send_message(chat_id, f"❌ لم يتم العثور على الملف {file_id}")
                    return

                file_name = file_info['file_name']

                # حذف سجلات التحميل المرتبطة
                cursor.execute("DELETE FROM download_history WHERE file_id = %s", (file_id,))
                history_deleted = cursor.rowcount

                # حذف الروابط المرتبطة
                cursor.execute("DELETE FROM download_links WHERE file_id = %s", (file_id,))
                links_deleted = cursor.rowcount

                # حذف الملف
                cursor.execute("DELETE FROM download_files WHERE id = %s", (file_id,))

                if cursor.rowcount == 0:
                    self.bot.send_message(chat_id, f"❌ فشل في حذف الملف {file_id}")
                    return

            message = f"✅ **تم حذف الملف بنجاح!**\n\n"
            message += f"📄 **الملف المحذوف:** {file_name}\n"
            message += f"🗑️ **البيانات المحذوفة:**\n"
            message += f"• سجلات التحميل: {history_deleted}\n"
            message += f"• الروابط المؤقتة: {links_deleted}\n\n"
            message += f"🧹 تم تنظيف جميع البيانات المرتبطة"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📁 قائمة الملفات", callback_data="admin_files_list"),
                types.InlineKeyboardButton("➕ إضافة ملف جديد", callback_data="admin_add_file")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة الملفات", callback_data="admin_files"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(chat_id, "delete_file", f"Deleted file {file_id}: {file_name}")

        except Exception as e:
            logger.error(f"Failed to delete file: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في حذف الملف")

    def show_growth_report(self, chat_id):
        """عرض تقرير النمو الشامل"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات النمو الأساسية
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_subscribers,
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as new_this_week,
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_this_month,
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '1 year' THEN 1 END) as new_this_year
                    FROM {self.subscribers_table}
                """)

                growth_stats = cursor.fetchone()

                # نمو المشتركين الشهري (آخر 12 شهر)
                cursor.execute(f"""
                    SELECT
                        DATE_TRUNC('month', created_at) as month,
                        COUNT(*) as new_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_new
                    FROM {self.subscribers_table}
                    WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
                    GROUP BY DATE_TRUNC('month', created_at)
                    ORDER BY month DESC
                    LIMIT 12
                """)

                monthly_growth = cursor.fetchall()

                # نمو المشتركين الأسبوعي (آخر 8 أسابيع)
                cursor.execute(f"""
                    SELECT
                        DATE_TRUNC('week', created_at) as week,
                        COUNT(*) as new_subscribers
                    FROM {self.subscribers_table}
                    WHERE created_at >= CURRENT_DATE - INTERVAL '8 weeks'
                    GROUP BY DATE_TRUNC('week', created_at)
                    ORDER BY week DESC
                    LIMIT 8
                """)

                weekly_growth = cursor.fetchall()

                # نمو الإيرادات الشهري
                cursor.execute(f"""
                    SELECT
                        DATE_TRUNC('month', created_at) as month,
                        COUNT(*) as payments_count,
                        COALESCE(SUM(amount), 0) as revenue
                    FROM {self.db.payments_table}
                    WHERE status = 'approved' AND created_at >= CURRENT_DATE - INTERVAL '12 months'
                    GROUP BY DATE_TRUNC('month', created_at)
                    ORDER BY month DESC
                    LIMIT 12
                """)

                revenue_growth = cursor.fetchall()

                # معدلات النمو
                cursor.execute(f"""
                    SELECT
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as current_month,
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '60 days'
                                   AND created_at < CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as previous_month
                    FROM {self.subscribers_table}
                """)

                growth_rate_data = cursor.fetchone()

                # إحصائيات النشاط
                cursor.execute(f"""
                    SELECT
                        COUNT(DISTINCT user_id) as active_users_today,
                        COUNT(DISTINCT CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN user_id END) as active_users_week
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= CURRENT_DATE
                """)

                activity_stats = cursor.fetchone()

            # حساب معدل النمو الشهري
            current_month = growth_rate_data['current_month']
            previous_month = growth_rate_data['previous_month']

            if previous_month > 0:
                growth_rate = ((current_month - previous_month) / previous_month) * 100
            else:
                growth_rate = 100 if current_month > 0 else 0

            # بناء رسالة التقرير
            message = f"📈 **تقرير النمو الشامل**\n\n"

            # الإحصائيات الأساسية
            message += f"📊 **الإحصائيات الحالية:**\n"
            message += f"👥 إجمالي المشتركين: {growth_stats['total_subscribers']}\n"
            message += f"✅ المشتركين النشطين: {growth_stats['active_subscribers']}\n"
            message += f"📈 معدل النشاط: {(growth_stats['active_subscribers']/max(growth_stats['total_subscribers'],1)*100):.1f}%\n\n"

            # النمو الزمني
            message += f"⏰ **النمو الزمني:**\n"
            message += f"📅 جدد هذا الأسبوع: {growth_stats['new_this_week']}\n"
            message += f"📅 جدد هذا الشهر: {growth_stats['new_this_month']}\n"
            message += f"📅 جدد هذا العام: {growth_stats['new_this_year']}\n\n"

            # معدل النمو
            growth_icon = "📈" if growth_rate >= 0 else "📉"
            message += f"📊 **معدل النمو الشهري:**\n"
            message += f"{growth_icon} {growth_rate:+.1f}% مقارنة بالشهر الماضي\n"
            message += f"• الشهر الحالي: {current_month} مشترك جديد\n"
            message += f"• الشهر الماضي: {previous_month} مشترك جديد\n\n"

            # النمو الشهري
            if monthly_growth:
                message += f"📈 **النمو الشهري (آخر 6 شهور):**\n"
                for i, month_data in enumerate(monthly_growth[:6]):
                    month_name = month_data['month'].strftime('%Y-%m')
                    new_subs = month_data['new_subscribers']
                    active_new = month_data['active_new']
                    message += f"• {month_name}: {new_subs} جديد ({active_new} نشط)\n"
                message += "\n"

            # النمو الأسبوعي
            if weekly_growth:
                message += f"📅 **النمو الأسبوعي (آخر 4 أسابيع):**\n"
                for i, week_data in enumerate(weekly_growth[:4]):
                    week_start = week_data['week'].strftime('%m-%d')
                    new_subs = week_data['new_subscribers']
                    message += f"• أسبوع {week_start}: {new_subs} مشترك جديد\n"
                message += "\n"

            # نمو الإيرادات
            if revenue_growth:
                message += f"💰 **نمو الإيرادات (آخر 6 شهور):**\n"
                for i, revenue_data in enumerate(revenue_growth[:6]):
                    month_name = revenue_data['month'].strftime('%Y-%m')
                    revenue = float(revenue_data['revenue'])
                    payments = revenue_data['payments_count']
                    message += f"• {month_name}: {revenue:.2f} جنيه ({payments} دفعة)\n"
                message += "\n"

            # إحصائيات النشاط
            message += f"🔄 **نشاط المستخدمين:**\n"
            message += f"• نشطين اليوم: {activity_stats['active_users_today']}\n"
            message += f"• نشطين هذا الأسبوع: {activity_stats['active_users_week']}\n\n"

            # توقعات النمو
            if monthly_growth and len(monthly_growth) >= 3:
                recent_months = [m['new_subscribers'] for m in monthly_growth[:3]]
                avg_growth = sum(recent_months) / len(recent_months)
                message += f"🔮 **توقعات النمو:**\n"
                message += f"• متوسط النمو الشهري: {avg_growth:.1f} مشترك\n"
                message += f"• توقع الشهر القادم: ~{int(avg_growth)} مشترك جديد\n"
                message += f"• توقع نهاية العام: ~{int(avg_growth * 12)} مشترك إضافي\n"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("👥 تقرير المشتركين", callback_data="admin_subscribers_report"),
                types.InlineKeyboardButton("💰 تقرير المدفوعات", callback_data="admin_payments_report")
            )
            markup.add(
                types.InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats"),
                types.InlineKeyboardButton("📋 قائمة التقارير", callback_data="admin_reports_menu")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show growth report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تقرير النمو")

    def show_revenue_report(self, chat_id):
        """عرض تقرير الإيرادات الشامل"""
        try:
            with self.db.get_db_cursor() as cursor:
                # إحصائيات الإيرادات الأساسية
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_payments,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as total_revenue,
                        COALESCE(SUM(CASE WHEN status = 'pending' THEN amount END), 0) as pending_revenue,
                        COALESCE(AVG(CASE WHEN status = 'approved' THEN amount END), 0) as avg_payment
                    FROM {self.db.payments_table}
                """)

                revenue_stats = cursor.fetchone()

                # إيرادات اليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as today_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as today_revenue
                    FROM {self.db.payments_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_stats = cursor.fetchone()

                # إيرادات الأسبوع
                cursor.execute(f"""
                    SELECT
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as week_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as week_revenue
                    FROM {self.db.payments_table}
                    WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
                """)

                week_stats = cursor.fetchone()

                # إيرادات الشهر
                cursor.execute(f"""
                    SELECT
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as month_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as month_revenue
                    FROM {self.db.payments_table}
                    WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
                """)

                month_stats = cursor.fetchone()

                # الإيرادات الشهرية (آخر 12 شهر)
                cursor.execute(f"""
                    SELECT
                        DATE_TRUNC('month', created_at) as month,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as payments_count,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as revenue
                    FROM {self.db.payments_table}
                    WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
                    GROUP BY DATE_TRUNC('month', created_at)
                    ORDER BY month DESC
                    LIMIT 12
                """)

                monthly_revenue = cursor.fetchall()

                # أفضل العملاء (أكثر دفعاً)
                cursor.execute(f"""
                    SELECT
                        p.user_id,
                        s.first_name,
                        s.username,
                        COUNT(*) as payment_count,
                        COALESCE(SUM(p.amount), 0) as total_paid
                    FROM {self.db.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    WHERE p.status = 'approved'
                    GROUP BY p.user_id, s.first_name, s.username
                    ORDER BY total_paid DESC
                    LIMIT 10
                """)

                top_customers = cursor.fetchall()

                # طرق الدفع الأكثر استخداماً
                cursor.execute(f"""
                    SELECT
                        payment_method,
                        COUNT(*) as usage_count,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as method_revenue
                    FROM {self.db.payments_table}
                    WHERE status = 'approved'
                    GROUP BY payment_method
                    ORDER BY method_revenue DESC
                """)

                payment_methods = cursor.fetchall()

                # معدل النجاح
                total_payments = revenue_stats['total_payments']
                approved_payments = revenue_stats['approved_payments']
                success_rate = (approved_payments / max(total_payments, 1)) * 100

                # نمو الإيرادات (مقارنة بالشهر الماضي)
                cursor.execute(f"""
                    SELECT
                        COALESCE(SUM(CASE WHEN status = 'approved'
                                         AND created_at >= CURRENT_DATE - INTERVAL '30 days'
                                         THEN amount END), 0) as current_month,
                        COALESCE(SUM(CASE WHEN status = 'approved'
                                         AND created_at >= CURRENT_DATE - INTERVAL '60 days'
                                         AND created_at < CURRENT_DATE - INTERVAL '30 days'
                                         THEN amount END), 0) as previous_month
                    FROM {self.db.payments_table}
                """)

                growth_data = cursor.fetchone()

            # حساب نمو الإيرادات
            current_month_revenue = float(growth_data['current_month'])
            previous_month_revenue = float(growth_data['previous_month'])

            if previous_month_revenue > 0:
                revenue_growth_rate = ((current_month_revenue - previous_month_revenue) / previous_month_revenue) * 100
            else:
                revenue_growth_rate = 100 if current_month_revenue > 0 else 0

            # بناء رسالة التقرير
            message = f"💰 **تقرير الإيرادات الشامل**\n\n"

            # الإحصائيات الأساسية
            message += f"📊 **الإحصائيات العامة:**\n"
            message += f"💳 إجمالي المدفوعات: {total_payments}\n"
            message += f"✅ مدفوعات مؤكدة: {approved_payments}\n"
            message += f"⏳ مدفوعات معلقة: {revenue_stats['pending_payments']}\n"
            message += f"❌ مدفوعات مرفوضة: {revenue_stats['rejected_payments']}\n"
            message += f"📈 معدل النجاح: {success_rate:.1f}%\n\n"

            # الإيرادات
            total_revenue = float(revenue_stats['total_revenue'])
            pending_revenue = float(revenue_stats['pending_revenue'])
            avg_payment = float(revenue_stats['avg_payment'])

            message += f"💰 **الإيرادات:**\n"
            message += f"💎 إجمالي الإيرادات: {total_revenue:.2f} جنيه\n"
            message += f"⏳ إيرادات معلقة: {pending_revenue:.2f} جنيه\n"
            message += f"📊 متوسط الدفعة: {avg_payment:.2f} جنيه\n\n"

            # الإيرادات الزمنية
            today_revenue = float(today_stats['today_revenue'])
            week_revenue = float(week_stats['week_revenue'])
            month_revenue = float(month_stats['month_revenue'])

            message += f"📅 **الإيرادات الزمنية:**\n"
            message += f"• اليوم: {today_revenue:.2f} جنيه ({today_stats['today_payments']} دفعة)\n"
            message += f"• هذا الأسبوع: {week_revenue:.2f} جنيه ({week_stats['week_payments']} دفعة)\n"
            message += f"• هذا الشهر: {month_revenue:.2f} جنيه ({month_stats['month_payments']} دفعة)\n\n"

            # نمو الإيرادات
            growth_icon = "📈" if revenue_growth_rate >= 0 else "📉"
            message += f"📊 **نمو الإيرادات:**\n"
            message += f"{growth_icon} {revenue_growth_rate:+.1f}% مقارنة بالشهر الماضي\n"
            message += f"• الشهر الحالي: {current_month_revenue:.2f} جنيه\n"
            message += f"• الشهر الماضي: {previous_month_revenue:.2f} جنيه\n\n"

            # الإيرادات الشهرية
            if monthly_revenue:
                message += f"📈 **الإيرادات الشهرية (آخر 6 شهور):**\n"
                for i, month_data in enumerate(monthly_revenue[:6]):
                    month_name = month_data['month'].strftime('%Y-%m')
                    revenue = float(month_data['revenue'])
                    payments = month_data['payments_count']
                    message += f"• {month_name}: {revenue:.2f} جنيه ({payments} دفعة)\n"
                message += "\n"

            # طرق الدفع
            if payment_methods:
                message += f"💳 **طرق الدفع الأكثر استخداماً:**\n"
                for method in payment_methods[:5]:
                    method_name = method['payment_method'] or 'غير محدد'
                    method_revenue = float(method['method_revenue'])
                    usage_count = method['usage_count']
                    message += f"• {method_name}: {method_revenue:.2f} جنيه ({usage_count} استخدام)\n"
                message += "\n"

            # أفضل العملاء
            if top_customers:
                message += f"👑 **أفضل العملاء (أكثر دفعاً):**\n"
                for i, customer in enumerate(top_customers[:5], 1):
                    name = customer['first_name'] or 'غير محدد'
                    username = customer['username'] or 'غير محدد'
                    total_paid = float(customer['total_paid'])
                    payment_count = customer['payment_count']
                    message += f"{i}. {name} (@{username})\n"
                    message += f"   💰 {total_paid:.2f} جنيه ({payment_count} دفعة)\n"
                message += "\n"

            # توقعات الإيرادات
            if monthly_revenue and len(monthly_revenue) >= 3:
                recent_revenues = [float(m['revenue']) for m in monthly_revenue[:3]]
                avg_monthly_revenue = sum(recent_revenues) / len(recent_revenues)
                message += f"🔮 **توقعات الإيرادات:**\n"
                message += f"• متوسط الإيرادات الشهرية: {avg_monthly_revenue:.2f} جنيه\n"
                message += f"• توقع الشهر القادم: ~{avg_monthly_revenue:.2f} جنيه\n"
                message += f"• توقع نهاية العام: ~{avg_monthly_revenue * 12:.2f} جنيه\n"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("💳 تقرير المدفوعات", callback_data="admin_payments_report"),
                types.InlineKeyboardButton("📈 تقرير النمو", callback_data="admin_growth_report")
            )
            markup.add(
                types.InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats"),
                types.InlineKeyboardButton("📋 قائمة التقارير", callback_data="admin_reports_menu")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show revenue report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تقرير الإيرادات")

    def show_monthly_report(self, chat_id):
        """عرض التقرير الشهري الشامل"""
        try:
            from datetime import datetime, timedelta
            import calendar

            # تحديد الشهر الحالي والشهر الماضي
            now = datetime.now()
            current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # الشهر الماضي
            if current_month_start.month == 1:
                previous_month_start = current_month_start.replace(year=current_month_start.year - 1, month=12)
            else:
                previous_month_start = current_month_start.replace(month=current_month_start.month - 1)

            # نهاية الشهر الماضي
            previous_month_end = current_month_start - timedelta(days=1)

            with self.db.get_db_cursor() as cursor:
                # إحصائيات المشتركين للشهر الحالي
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as new_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_new_subscribers
                    FROM {self.subscribers_table}
                    WHERE created_at >= %s
                """, (current_month_start,))

                current_month_subs = cursor.fetchone()

                # إحصائيات المشتركين للشهر الماضي
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as new_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_new_subscribers
                    FROM {self.subscribers_table}
                    WHERE created_at >= %s AND created_at <= %s
                """, (previous_month_start, previous_month_end))

                previous_month_subs = cursor.fetchone()

                # إحصائيات المدفوعات للشهر الحالي
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_payments,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as total_revenue
                    FROM {self.db.payments_table}
                    WHERE created_at >= %s
                """, (current_month_start,))

                current_month_payments = cursor.fetchone()

                # إحصائيات المدفوعات للشهر الماضي
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_payments,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as total_revenue
                    FROM {self.db.payments_table}
                    WHERE created_at >= %s AND created_at <= %s
                """, (previous_month_start, previous_month_end))

                previous_month_payments = cursor.fetchone()

                # إحصائيات النشاط للشهر الحالي
                cursor.execute(f"""
                    SELECT
                        COUNT(DISTINCT user_id) as active_users,
                        COUNT(*) as total_activities
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s
                """, (current_month_start,))

                current_month_activity = cursor.fetchone()

                # إحصائيات النشاط للشهر الماضي
                cursor.execute(f"""
                    SELECT
                        COUNT(DISTINCT user_id) as active_users,
                        COUNT(*) as total_activities
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s AND created_at <= %s
                """, (previous_month_start, previous_month_end))

                previous_month_activity = cursor.fetchone()

                # إحصائيات الدعم للشهر الحالي
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
                        AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL
                            THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_resolution_time
                    FROM {self.db.support_queue_table}
                    WHERE created_at >= %s
                """, (current_month_start,))

                current_month_support = cursor.fetchone()

                # إحصائيات التحميل للشهر الحالي
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_downloads,
                        COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as successful_downloads,
                        COUNT(DISTINCT user_id) as unique_downloaders
                    FROM download_history
                    WHERE created_at >= %s
                """, (current_month_start,))

                current_month_downloads = cursor.fetchone()

                # أداء يومي للشهر الحالي
                cursor.execute(f"""
                    SELECT
                        DATE(created_at) as day,
                        COUNT(*) as new_subscribers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_subscribers
                    FROM {self.subscribers_table}
                    WHERE created_at >= %s
                    GROUP BY DATE(created_at)
                    ORDER BY day DESC
                    LIMIT 10
                """, (current_month_start,))

                daily_performance = cursor.fetchall()

                # أفضل أيام الشهر
                cursor.execute(f"""
                    SELECT
                        DATE(p.created_at) as day,
                        COUNT(*) as payments_count,
                        COALESCE(SUM(p.amount), 0) as daily_revenue
                    FROM {self.db.payments_table} p
                    WHERE p.created_at >= %s AND p.status = 'approved'
                    GROUP BY DATE(p.created_at)
                    ORDER BY daily_revenue DESC
                    LIMIT 5
                """, (current_month_start,))

                best_days = cursor.fetchall()

            # حساب النسب المئوية للتغيير
            def calculate_change_percentage(current, previous):
                if previous == 0:
                    return 100 if current > 0 else 0
                return ((current - previous) / previous) * 100

            # حساب التغييرات
            subs_change = calculate_change_percentage(
                current_month_subs['new_subscribers'],
                previous_month_subs['new_subscribers']
            )

            revenue_change = calculate_change_percentage(
                float(current_month_payments['total_revenue']),
                float(previous_month_payments['total_revenue'])
            )

            activity_change = calculate_change_percentage(
                current_month_activity['active_users'],
                previous_month_activity['active_users']
            )

            # بناء رسالة التقرير
            current_month_name = calendar.month_name[now.month]
            previous_month_name = calendar.month_name[previous_month_start.month]

            message = f"📆 **التقرير الشهري - {current_month_name} {now.year}**\n\n"

            # ملخص الشهر
            message += f"📊 **ملخص الشهر:**\n"
            message += f"📅 الفترة: {current_month_start.strftime('%Y-%m-%d')} - {now.strftime('%Y-%m-%d')}\n"
            message += f"📈 أيام مكتملة: {(now - current_month_start).days + 1} يوم\n\n"

            # المشتركين
            subs_icon = "📈" if subs_change >= 0 else "📉"
            message += f"👥 **المشتركين الجدد:**\n"
            message += f"• هذا الشهر: {current_month_subs['new_subscribers']} مشترك\n"
            message += f"• الشهر الماضي: {previous_month_subs['new_subscribers']} مشترك\n"
            message += f"• التغيير: {subs_icon} {subs_change:+.1f}%\n"
            message += f"• نشطين من الجدد: {current_month_subs['active_new_subscribers']}\n\n"

            # الإيرادات
            current_revenue = float(current_month_payments['total_revenue'])
            previous_revenue = float(previous_month_payments['total_revenue'])
            revenue_icon = "📈" if revenue_change >= 0 else "📉"

            message += f"💰 **الإيرادات:**\n"
            message += f"• هذا الشهر: {current_revenue:.2f} جنيه\n"
            message += f"• الشهر الماضي: {previous_revenue:.2f} جنيه\n"
            message += f"• التغيير: {revenue_icon} {revenue_change:+.1f}%\n"
            message += f"• مدفوعات مؤكدة: {current_month_payments['approved_payments']}\n"
            message += f"• معدل النجاح: {(current_month_payments['approved_payments']/max(current_month_payments['total_payments'],1)*100):.1f}%\n\n"

            # النشاط
            activity_icon = "📈" if activity_change >= 0 else "📉"
            message += f"🔄 **نشاط المستخدمين:**\n"
            message += f"• مستخدمين نشطين: {current_month_activity['active_users']}\n"
            message += f"• الشهر الماضي: {previous_month_activity['active_users']}\n"
            message += f"• التغيير: {activity_icon} {activity_change:+.1f}%\n"
            message += f"• إجمالي الأنشطة: {current_month_activity['total_activities']}\n\n"

            # الدعم
            if current_month_support['total_requests'] > 0:
                support_completion_rate = (current_month_support['completed_requests'] / current_month_support['total_requests']) * 100
                avg_resolution = current_month_support['avg_resolution_time'] or 0

                message += f"🎧 **نظام الدعم:**\n"
                message += f"• طلبات الدعم: {current_month_support['total_requests']}\n"
                message += f"• طلبات مكتملة: {current_month_support['completed_requests']}\n"
                message += f"• معدل الإنجاز: {support_completion_rate:.1f}%\n"
                message += f"• متوسط وقت الحل: {avg_resolution:.1f} دقيقة\n\n"

            # التحميلات
            if current_month_downloads and current_month_downloads['total_downloads'] > 0:
                download_success_rate = (current_month_downloads['successful_downloads'] / current_month_downloads['total_downloads']) * 100

                message += f"📁 **ملفات التحميل:**\n"
                message += f"• إجمالي التحميلات: {current_month_downloads['total_downloads']}\n"
                message += f"• تحميلات ناجحة: {current_month_downloads['successful_downloads']}\n"
                message += f"• معدل النجاح: {download_success_rate:.1f}%\n"
                message += f"• مستخدمين فريدين: {current_month_downloads['unique_downloaders']}\n\n"

            # الأداء اليومي
            if daily_performance:
                message += f"📈 **الأداء اليومي (آخر 10 أيام):**\n"
                for day_data in daily_performance[:5]:
                    day_str = day_data['day'].strftime('%m-%d')
                    message += f"• {day_str}: {day_data['new_subscribers']} مشترك ({day_data['active_subscribers']} نشط)\n"
                message += "\n"

            # أفضل أيام الشهر
            if best_days:
                message += f"🏆 **أفضل أيام الشهر (إيرادات):**\n"
                for i, day_data in enumerate(best_days[:3], 1):
                    day_str = day_data['day'].strftime('%m-%d')
                    revenue = float(day_data['daily_revenue'])
                    payments = day_data['payments_count']
                    message += f"{i}. {day_str}: {revenue:.2f} جنيه ({payments} دفعة)\n"
                message += "\n"

            # توقعات نهاية الشهر
            days_passed = (now - current_month_start).days + 1
            days_in_month = calendar.monthrange(now.year, now.month)[1]
            days_remaining = days_in_month - days_passed

            if days_remaining > 0:
                daily_avg_subs = current_month_subs['new_subscribers'] / days_passed
                daily_avg_revenue = current_revenue / days_passed

                projected_subs = current_month_subs['new_subscribers'] + (daily_avg_subs * days_remaining)
                projected_revenue = current_revenue + (daily_avg_revenue * days_remaining)

                message += f"🔮 **توقعات نهاية الشهر:**\n"
                message += f"• متوقع المشتركين: ~{int(projected_subs)} مشترك\n"
                message += f"• متوقع الإيرادات: ~{projected_revenue:.2f} جنيه\n"
                message += f"• أيام متبقية: {days_remaining} يوم\n\n"

            # تقييم الأداء
            message += f"⭐ **تقييم الأداء:**\n"

            performance_score = 0
            if subs_change > 0:
                performance_score += 25
            if revenue_change > 0:
                performance_score += 25
            if activity_change > 0:
                performance_score += 25
            if current_month_support and current_month_support['total_requests'] > 0:
                if (current_month_support['completed_requests'] / current_month_support['total_requests']) > 0.8:
                    performance_score += 25

            if performance_score >= 75:
                message += "🌟 أداء ممتاز! استمر على هذا المنوال"
            elif performance_score >= 50:
                message += "👍 أداء جيد مع إمكانية للتحسين"
            elif performance_score >= 25:
                message += "⚠️ أداء متوسط، يحتاج تحسين"
            else:
                message += "🔴 أداء ضعيف، يحتاج مراجعة شاملة"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("📈 تقرير النمو", callback_data="admin_growth_report"),
                types.InlineKeyboardButton("💰 تقرير الإيرادات", callback_data="admin_revenue_report")
            )
            markup.add(
                types.InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats"),
                types.InlineKeyboardButton("📋 قائمة التقارير", callback_data="admin_reports_menu")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show monthly report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض التقرير الشهري")

    def show_daily_report(self, chat_id):
        """عرض التقرير اليومي الشامل"""
        try:
            from datetime import datetime, timedelta

            # تحديد اليوم الحالي والأمس
            now = datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            yesterday_start = today_start - timedelta(days=1)
            yesterday_end = today_start - timedelta(seconds=1)

            with self.db.get_db_cursor() as cursor:
                # إحصائيات المشتركين لليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as new_subscribers_today,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_new_today
                    FROM {self.subscribers_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_subs = cursor.fetchone()

                # إحصائيات المشتركين للأمس
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as new_subscribers_yesterday,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_new_yesterday
                    FROM {self.subscribers_table}
                    WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
                """)

                yesterday_subs = cursor.fetchone()

                # إحصائيات المدفوعات لليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_payments_today,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_payments_today,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments_today,
                        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_payments_today,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as revenue_today
                    FROM {self.db.payments_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_payments = cursor.fetchone()

                # إحصائيات المدفوعات للأمس
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_payments_yesterday,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_payments_yesterday,
                        COALESCE(SUM(CASE WHEN status = 'approved' THEN amount END), 0) as revenue_yesterday
                    FROM {self.db.payments_table}
                    WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
                """)

                yesterday_payments = cursor.fetchone()

                # إحصائيات النشاط لليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(DISTINCT user_id) as active_users_today,
                        COUNT(*) as total_activities_today
                    FROM {self.db.activity_logs_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_activity = cursor.fetchone()

                # إحصائيات النشاط للأمس
                cursor.execute(f"""
                    SELECT
                        COUNT(DISTINCT user_id) as active_users_yesterday,
                        COUNT(*) as total_activities_yesterday
                    FROM {self.db.activity_logs_table}
                    WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
                """)

                yesterday_activity = cursor.fetchone()

                # إحصائيات الدعم لليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as support_requests_today,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests_today,
                        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_sessions_today,
                        AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL
                            THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_resolution_today
                    FROM {self.db.support_queue_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_support = cursor.fetchone()

                # إحصائيات التحميل لليوم
                cursor.execute("""
                    SELECT
                        COUNT(*) as downloads_today,
                        COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as successful_downloads_today,
                        COUNT(DISTINCT user_id) as unique_downloaders_today
                    FROM download_history
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_downloads = cursor.fetchone()

                # إحصائيات التحميل للأمس
                cursor.execute("""
                    SELECT
                        COUNT(*) as downloads_yesterday,
                        COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as successful_downloads_yesterday
                    FROM download_history
                    WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
                """)

                yesterday_downloads = cursor.fetchone()

                # أنشطة اليوم بالساعة
                cursor.execute(f"""
                    SELECT
                        HOUR(created_at) as hour,
                        COUNT(*) as activities_count
                    FROM {self.db.activity_logs_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                    GROUP BY HOUR(created_at)
                    ORDER BY hour
                """)

                hourly_activity = cursor.fetchall()

                # أكثر المستخدمين نشاطاً اليوم
                cursor.execute(f"""
                    SELECT
                        al.user_id,
                        s.first_name,
                        s.username,
                        COUNT(*) as activity_count
                    FROM {self.db.activity_logs_table} al
                    LEFT JOIN {self.subscribers_table} s ON al.user_id = s.user_id
                    WHERE DATE(al.created_at) = CURRENT_DATE
                    GROUP BY al.user_id, s.first_name, s.username
                    ORDER BY activity_count DESC
                    LIMIT 5
                """)

                top_active_users = cursor.fetchall()

                # أحدث المشتركين اليوم
                cursor.execute(f"""
                    SELECT
                        user_id,
                        first_name,
                        username,
                        created_at,
                        is_active
                    FROM {self.subscribers_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                    ORDER BY created_at DESC
                    LIMIT 10
                """)

                latest_subscribers = cursor.fetchall()

                # أحدث المدفوعات اليوم
                cursor.execute(f"""
                    SELECT
                        p.user_id,
                        s.first_name,
                        s.username,
                        p.amount,
                        p.status,
                        p.payment_method,
                        p.created_at
                    FROM {self.db.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    WHERE DATE(p.created_at) = CURRENT_DATE
                    ORDER BY p.created_at DESC
                    LIMIT 10
                """)

                latest_payments = cursor.fetchall()

            # حساب النسب المئوية للتغيير
            def calculate_change_percentage(current, previous):
                if previous == 0:
                    return 100 if current > 0 else 0
                return ((current - previous) / previous) * 100

            # حساب التغييرات
            subs_change = calculate_change_percentage(
                today_subs['new_subscribers_today'],
                yesterday_subs['new_subscribers_yesterday']
            )

            revenue_change = calculate_change_percentage(
                float(today_payments['revenue_today']),
                float(yesterday_payments['revenue_yesterday'])
            )

            activity_change = calculate_change_percentage(
                today_activity['active_users_today'],
                yesterday_activity['active_users_yesterday']
            )

            downloads_change = calculate_change_percentage(
                today_downloads['downloads_today'],
                yesterday_downloads['downloads_yesterday']
            )

            # بناء رسالة التقرير
            today_str = now.strftime('%Y-%m-%d')
            day_name = now.strftime('%A')

            # ترجمة أسماء الأيام
            day_names = {
                'Monday': 'الاثنين',
                'Tuesday': 'الثلاثاء',
                'Wednesday': 'الأربعاء',
                'Thursday': 'الخميس',
                'Friday': 'الجمعة',
                'Saturday': 'السبت',
                'Sunday': 'الأحد'
            }

            day_name_ar = day_names.get(day_name, day_name)

            message = f"📅 **التقرير اليومي - {day_name_ar}**\n"
            message += f"📆 **التاريخ:** {today_str}\n"
            message += f"⏰ **الوقت:** {now.strftime('%H:%M')}\n\n"

            # ملخص اليوم
            message += f"📊 **ملخص اليوم:**\n"

            # المشتركين
            subs_icon = "📈" if subs_change >= 0 else "📉"
            message += f"👥 **المشتركين الجدد:**\n"
            message += f"• اليوم: {today_subs['new_subscribers_today']} مشترك\n"
            message += f"• الأمس: {yesterday_subs['new_subscribers_yesterday']} مشترك\n"
            message += f"• التغيير: {subs_icon} {subs_change:+.1f}%\n"
            message += f"• نشطين من الجدد: {today_subs['active_new_today']}\n\n"

            # الإيرادات
            revenue_today = float(today_payments['revenue_today'])
            revenue_yesterday = float(yesterday_payments['revenue_yesterday'])
            revenue_icon = "📈" if revenue_change >= 0 else "📉"

            message += f"💰 **الإيرادات:**\n"
            message += f"• اليوم: {revenue_today:.2f} جنيه\n"
            message += f"• الأمس: {revenue_yesterday:.2f} جنيه\n"
            message += f"• التغيير: {revenue_icon} {revenue_change:+.1f}%\n"
            message += f"• مدفوعات مؤكدة: {today_payments['approved_payments_today']}\n"
            if today_payments['total_payments_today'] > 0:
                success_rate = (today_payments['approved_payments_today'] / today_payments['total_payments_today']) * 100
                message += f"• معدل النجاح: {success_rate:.1f}%\n"
            message += "\n"

            # النشاط
            activity_icon = "📈" if activity_change >= 0 else "📉"
            message += f"🔄 **نشاط المستخدمين:**\n"
            message += f"• مستخدمين نشطين: {today_activity['active_users_today']}\n"
            message += f"• الأمس: {yesterday_activity['active_users_yesterday']}\n"
            message += f"• التغيير: {activity_icon} {activity_change:+.1f}%\n"
            message += f"• إجمالي الأنشطة: {today_activity['total_activities_today']}\n\n"

            # الدعم
            if today_support['support_requests_today'] > 0:
                support_completion_rate = (today_support['completed_requests_today'] / today_support['support_requests_today']) * 100
                avg_resolution = today_support['avg_resolution_today'] or 0

                message += f"🎧 **نظام الدعم:**\n"
                message += f"• طلبات جديدة: {today_support['support_requests_today']}\n"
                message += f"• طلبات مكتملة: {today_support['completed_requests_today']}\n"
                message += f"• جلسات نشطة: {today_support['active_sessions_today']}\n"
                message += f"• معدل الإنجاز: {support_completion_rate:.1f}%\n"
                message += f"• متوسط وقت الحل: {avg_resolution:.1f} دقيقة\n\n"

            # التحميلات
            if today_downloads['downloads_today'] > 0:
                download_success_rate = (today_downloads['successful_downloads_today'] / today_downloads['downloads_today']) * 100
                downloads_icon = "📈" if downloads_change >= 0 else "📉"

                message += f"📁 **ملفات التحميل:**\n"
                message += f"• تحميلات اليوم: {today_downloads['downloads_today']}\n"
                message += f"• تحميلات الأمس: {yesterday_downloads['downloads_yesterday']}\n"
                message += f"• التغيير: {downloads_icon} {downloads_change:+.1f}%\n"
                message += f"• تحميلات ناجحة: {today_downloads['successful_downloads_today']}\n"
                message += f"• معدل النجاح: {download_success_rate:.1f}%\n"
                message += f"• مستخدمين فريدين: {today_downloads['unique_downloaders_today']}\n\n"

            # النشاط بالساعة
            if hourly_activity:
                message += f"⏰ **النشاط بالساعة (أكثر 5 ساعات نشاطاً):**\n"
                sorted_hours = sorted(hourly_activity, key=lambda x: x['activities_count'], reverse=True)
                for hour_data in sorted_hours[:5]:
                    hour = hour_data['hour']
                    count = hour_data['activities_count']
                    message += f"• {hour:02d}:00 - {count} نشاط\n"
                message += "\n"

            # أكثر المستخدمين نشاطاً
            if top_active_users:
                message += f"🏆 **أكثر المستخدمين نشاطاً اليوم:**\n"
                for i, user in enumerate(top_active_users[:3], 1):
                    name = user['first_name'] or 'غير محدد'
                    username = user['username'] or 'غير محدد'
                    count = user['activity_count']
                    message += f"{i}. {name} (@{username}) - {count} نشاط\n"
                message += "\n"

            # أحدث المشتركين
            if latest_subscribers:
                message += f"🆕 **أحدث المشتركين (آخر 5):**\n"
                for sub in latest_subscribers[:5]:
                    name = sub['first_name'] or 'غير محدد'
                    username = sub['username'] or 'غير محدد'
                    time = sub['created_at'].strftime('%H:%M')
                    status = "✅ نشط" if sub['is_active'] else "⏸️ غير نشط"
                    message += f"• {time} - {name} (@{username}) {status}\n"
                message += "\n"

            # أحدث المدفوعات
            if latest_payments:
                message += f"💳 **أحدث المدفوعات (آخر 5):**\n"
                for payment in latest_payments[:5]:
                    name = payment['first_name'] or 'غير محدد'
                    amount = float(payment['amount'])
                    status = payment['status']
                    time = payment['created_at'].strftime('%H:%M')

                    status_icons = {
                        'approved': '✅',
                        'pending': '⏳',
                        'rejected': '❌'
                    }
                    status_icon = status_icons.get(status, '❓')

                    message += f"• {time} - {name}: {amount:.2f} جنيه {status_icon}\n"
                message += "\n"

            # تقييم اليوم
            message += f"⭐ **تقييم أداء اليوم:**\n"

            performance_score = 0
            if today_subs['new_subscribers_today'] > 0:
                performance_score += 25
            if revenue_today > 0:
                performance_score += 25
            if today_activity['active_users_today'] > yesterday_activity['active_users_yesterday']:
                performance_score += 25
            if today_support['support_requests_today'] == 0 or (today_support['completed_requests_today'] / max(today_support['support_requests_today'], 1)) > 0.8:
                performance_score += 25

            if performance_score >= 75:
                message += "🌟 يوم ممتاز! أداء رائع"
            elif performance_score >= 50:
                message += "👍 يوم جيد مع نشاط مقبول"
            elif performance_score >= 25:
                message += "⚠️ يوم متوسط، يمكن تحسينه"
            else:
                message += "🔴 يوم هادئ، قد تحتاج لمزيد من التسويق"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("📆 التقرير الشهري", callback_data="admin_monthly_report"),
                types.InlineKeyboardButton("📈 تقرير النمو", callback_data="admin_growth_report")
            )
            markup.add(
                types.InlineKeyboardButton("💰 تقرير الإيرادات", callback_data="admin_revenue_report"),
                types.InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show daily report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض التقرير اليومي")

    def show_activity_report(self, chat_id):
        """عرض تقرير النشاط الشامل"""
        try:
            from datetime import datetime, timedelta

            # تحديد الفترات الزمنية
            now = datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            week_start = today_start - timedelta(days=7)
            month_start = today_start - timedelta(days=30)

            with self.db.get_db_cursor() as cursor:
                # إحصائيات النشاط العامة
                cursor.execute(f"""
                    SELECT
                        COUNT(DISTINCT user_id) as total_active_users,
                        COUNT(*) as total_activities,
                        COUNT(DISTINCT DATE(created_at)) as active_days
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s
                """, (month_start,))

                general_stats = cursor.fetchone()

                # النشاط اليومي (آخر 30 يوم)
                cursor.execute(f"""
                    SELECT
                        DATE(created_at) as activity_date,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(*) as total_activities
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s
                    GROUP BY DATE(created_at)
                    ORDER BY activity_date DESC
                    LIMIT 30
                """, (month_start,))

                daily_activity = cursor.fetchall()

                # النشاط بالساعة (آخر 7 أيام)
                cursor.execute(f"""
                    SELECT
                        HOUR(created_at) as activity_hour,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(*) as total_activities
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s
                    GROUP BY HOUR(created_at)
                    ORDER BY total_activities DESC
                """, (week_start,))

                hourly_activity = cursor.fetchall()

                # أنواع الأنشطة الأكثر شيوعاً
                cursor.execute(f"""
                    SELECT
                        action_type,
                        COUNT(*) as action_count,
                        COUNT(DISTINCT user_id) as unique_users
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s
                    GROUP BY action_type
                    ORDER BY action_count DESC
                    LIMIT 10
                """, (month_start,))

                activity_types = cursor.fetchall()

                # أكثر المستخدمين نشاطاً (آخر 30 يوم)
                cursor.execute(f"""
                    SELECT
                        al.user_id,
                        s.first_name,
                        s.username,
                        COUNT(*) as activity_count,
                        COUNT(DISTINCT DATE(al.created_at)) as active_days,
                        MAX(al.created_at) as last_activity
                    FROM {self.db.activity_logs_table} al
                    LEFT JOIN {self.subscribers_table} s ON al.user_id = s.user_id
                    WHERE al.created_at >= %s
                    GROUP BY al.user_id, s.first_name, s.username
                    ORDER BY activity_count DESC
                    LIMIT 15
                """, (month_start,))

                top_active_users = cursor.fetchall()

                # المستخدمين الجدد النشطين
                cursor.execute(f"""
                    SELECT
                        s.user_id,
                        s.first_name,
                        s.username,
                        s.created_at as join_date,
                        COUNT(al.id) as activity_count
                    FROM {self.subscribers_table} s
                    LEFT JOIN {self.db.activity_logs_table} al ON s.user_id = al.user_id
                    WHERE s.created_at >= %s
                    GROUP BY s.user_id, s.first_name, s.username, s.created_at
                    HAVING activity_count > 0
                    ORDER BY activity_count DESC
                    LIMIT 10
                """, (week_start,))

                new_active_users = cursor.fetchall()

                # المستخدمين غير النشطين (لم يسجلوا نشاط لأكثر من 7 أيام)
                cursor.execute(f"""
                    SELECT
                        s.user_id,
                        s.first_name,
                        s.username,
                        s.is_active,
                        MAX(al.created_at) as last_activity
                    FROM {self.subscribers_table} s
                    LEFT JOIN {self.db.activity_logs_table} al ON s.user_id = al.user_id
                    WHERE s.is_active = TRUE
                    GROUP BY s.user_id, s.first_name, s.username, s.is_active
                    HAVING last_activity < %s OR last_activity IS NULL
                    ORDER BY last_activity ASC
                    LIMIT 10
                """, (week_start,))

                inactive_users = cursor.fetchall()

                # إحصائيات الأنشطة حسب نوع المستخدم
                cursor.execute(f"""
                    SELECT
                        CASE
                            WHEN s.is_active = TRUE THEN 'مشترك نشط'
                            WHEN s.is_active = FALSE THEN 'مشترك منتهي'
                            ELSE 'غير مشترك'
                        END as user_type,
                        COUNT(DISTINCT al.user_id) as unique_users,
                        COUNT(al.id) as total_activities
                    FROM {self.db.activity_logs_table} al
                    LEFT JOIN {self.subscribers_table} s ON al.user_id = s.user_id
                    WHERE al.created_at >= %s
                    GROUP BY user_type
                    ORDER BY total_activities DESC
                """, (month_start,))

                activity_by_user_type = cursor.fetchall()

                # معدل النشاط الأسبوعي
                cursor.execute(f"""
                    SELECT
                        WEEK(created_at) as activity_week,
                        YEAR(created_at) as activity_year,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(*) as total_activities
                    FROM {self.db.activity_logs_table}
                    WHERE created_at >= %s
                    GROUP BY YEAR(created_at), WEEK(created_at)
                    ORDER BY activity_year DESC, activity_week DESC
                    LIMIT 8
                """, (month_start,))

                weekly_activity = cursor.fetchall()

                # أحدث الأنشطة
                cursor.execute(f"""
                    SELECT
                        al.user_id,
                        s.first_name,
                        s.username,
                        al.action_type,
                        al.details,
                        al.created_at
                    FROM {self.db.activity_logs_table} al
                    LEFT JOIN {self.subscribers_table} s ON al.user_id = s.user_id
                    ORDER BY al.created_at DESC
                    LIMIT 20
                """)

                recent_activities = cursor.fetchall()

            # بناء رسالة التقرير
            message = f"🔄 **تقرير النشاط الشامل**\n\n"

            # الإحصائيات العامة
            message += f"📊 **الإحصائيات العامة (آخر 30 يوم):**\n"
            message += f"👥 إجمالي المستخدمين النشطين: {general_stats['total_active_users']}\n"
            message += f"🔄 إجمالي الأنشطة: {general_stats['total_activities']}\n"
            message += f"📅 أيام النشاط: {general_stats['active_days']} من 30 يوم\n"

            if general_stats['total_active_users'] > 0:
                avg_activities_per_user = general_stats['total_activities'] / general_stats['total_active_users']
                message += f"📈 متوسط الأنشطة لكل مستخدم: {avg_activities_per_user:.1f}\n"
            message += "\n"

            # النشاط اليومي (آخر 7 أيام)
            if daily_activity:
                message += f"📅 **النشاط اليومي (آخر 7 أيام):**\n"
                for day_data in daily_activity[:7]:
                    date_str = day_data['activity_date'].strftime('%m-%d')
                    users = day_data['unique_users']
                    activities = day_data['total_activities']
                    message += f"• {date_str}: {users} مستخدم ({activities} نشاط)\n"
                message += "\n"

            # أكثر الساعات نشاطاً
            if hourly_activity:
                message += f"⏰ **أكثر الساعات نشاطاً (آخر 7 أيام):**\n"
                for hour_data in hourly_activity[:5]:
                    hour = hour_data['activity_hour']
                    users = hour_data['unique_users']
                    activities = hour_data['total_activities']
                    message += f"• {hour:02d}:00 - {users} مستخدم ({activities} نشاط)\n"
                message += "\n"

            # أنواع الأنشطة الأكثر شيوعاً
            if activity_types:
                message += f"📋 **أنواع الأنشطة الأكثر شيوعاً:**\n"
                activity_names = {
                    'login': '🔐 تسجيل دخول',
                    'payment_attempt': '💳 محاولة دفع',
                    'subscription_check': '📄 فحص الاشتراك',
                    'support_request': '🎧 طلب دعم',
                    'file_download': '📁 تحميل ملف',
                    'profile_update': '👤 تحديث الملف الشخصي',
                    'menu_navigation': '🧭 تصفح القوائم',
                    'notification_received': '🔔 استلام إشعار'
                }

                for activity in activity_types[:6]:
                    action_type = activity['action_type']
                    action_name = activity_names.get(action_type, action_type)
                    count = activity['action_count']
                    users = activity['unique_users']
                    message += f"• {action_name}: {count} مرة ({users} مستخدم)\n"
                message += "\n"

            # أكثر المستخدمين نشاطاً
            if top_active_users:
                message += f"🏆 **أكثر المستخدمين نشاطاً (آخر 30 يوم):**\n"
                for i, user in enumerate(top_active_users[:5], 1):
                    name = user['first_name'] or 'غير محدد'
                    username = user['username'] or 'غير محدد'
                    count = user['activity_count']
                    days = user['active_days']
                    last_activity = user['last_activity'].strftime('%m-%d %H:%M')
                    message += f"{i}. {name} (@{username})\n"
                    message += f"   🔄 {count} نشاط في {days} يوم\n"
                    message += f"   ⏰ آخر نشاط: {last_activity}\n"
                message += "\n"

            # المستخدمين الجدد النشطين
            if new_active_users:
                message += f"🆕 **المستخدمين الجدد النشطين (آخر 7 أيام):**\n"
                for user in new_active_users[:3]:
                    name = user['first_name'] or 'غير محدد'
                    username = user['username'] or 'غير محدد'
                    count = user['activity_count']
                    join_date = user['join_date'].strftime('%m-%d')
                    message += f"• {name} (@{username})\n"
                    message += f"  📅 انضم: {join_date} - {count} نشاط\n"
                message += "\n"

            # النشاط حسب نوع المستخدم
            if activity_by_user_type:
                message += f"👥 **النشاط حسب نوع المستخدم:**\n"
                for user_type_data in activity_by_user_type:
                    user_type = user_type_data['user_type']
                    users = user_type_data['unique_users']
                    activities = user_type_data['total_activities']
                    message += f"• {user_type}: {users} مستخدم ({activities} نشاط)\n"
                message += "\n"

            # المستخدمين غير النشطين
            if inactive_users:
                message += f"😴 **مستخدمين غير نشطين (أكثر من 7 أيام):**\n"
                for user in inactive_users[:3]:
                    name = user['first_name'] or 'غير محدد'
                    username = user['username'] or 'غير محدد'
                    last_activity = user['last_activity']
                    if last_activity:
                        days_ago = (now - last_activity).days
                        message += f"• {name} (@{username}) - {days_ago} يوم\n"
                    else:
                        message += f"• {name} (@{username}) - لا يوجد نشاط\n"
                message += "\n"

            # أحدث الأنشطة
            if recent_activities:
                message += f"🕐 **أحدث الأنشطة (آخر 5):**\n"
                for activity in recent_activities[:5]:
                    name = activity['first_name'] or 'غير محدد'
                    action = activity['action_type']
                    time = activity['created_at'].strftime('%H:%M')

                    action_icons = {
                        'login': '🔐',
                        'payment_attempt': '💳',
                        'subscription_check': '📄',
                        'support_request': '🎧',
                        'file_download': '📁',
                        'profile_update': '👤',
                        'menu_navigation': '🧭'
                    }

                    icon = action_icons.get(action, '🔄')
                    message += f"• {time} - {icon} {name}: {action}\n"
                message += "\n"

            # تحليل الاتجاهات
            if daily_activity and len(daily_activity) >= 7:
                recent_week = sum([day['total_activities'] for day in daily_activity[:7]])
                previous_week = sum([day['total_activities'] for day in daily_activity[7:14]]) if len(daily_activity) >= 14 else recent_week

                if previous_week > 0:
                    trend = ((recent_week - previous_week) / previous_week) * 100
                    trend_icon = "📈" if trend >= 0 else "📉"
                    message += f"📊 **اتجاه النشاط:**\n"
                    message += f"{trend_icon} {trend:+.1f}% مقارنة بالأسبوع الماضي\n"
                    message += f"• هذا الأسبوع: {recent_week} نشاط\n"
                    message += f"• الأسبوع الماضي: {previous_week} نشاط\n\n"

            # توصيات
            message += f"💡 **توصيات لزيادة النشاط:**\n"

            if general_stats['total_active_users'] < 50:
                message += "• 📢 زيادة التسويق لجذب مستخدمين جدد\n"

            if len(inactive_users) > 5:
                message += "• 🔔 إرسال رسائل تذكيرية للمستخدمين غير النشطين\n"

            if hourly_activity:
                peak_hour = hourly_activity[0]['activity_hour']
                message += f"• ⏰ التركيز على النشاطات في الساعة {peak_hour:02d}:00 (أكثر الأوقات نشاطاً)\n"

            message += "• 🎁 إضافة محتوى تفاعلي ومكافآت للمستخدمين النشطين"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("📅 التقرير اليومي", callback_data="admin_daily_report"),
                types.InlineKeyboardButton("📆 التقرير الشهري", callback_data="admin_monthly_report")
            )
            markup.add(
                types.InlineKeyboardButton("📈 تقرير النمو", callback_data="admin_growth_report"),
                types.InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show activity report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تقرير النشاط")

    def show_issues_report(self, chat_id):
        """عرض تقرير المشاكل والأخطاء الشامل"""
        try:
            from datetime import datetime, timedelta
            import os

            # تحديد الفترات الزمنية
            now = datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            week_start = today_start - timedelta(days=7)
            month_start = today_start - timedelta(days=30)

            with self.db.get_db_cursor() as cursor:
                # إحصائيات المدفوعات المرفوضة
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as rejected_payments,
                        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as rejected_today,
                        COUNT(CASE WHEN created_at >= %s THEN 1 END) as rejected_week,
                        payment_method,
                        COUNT(*) as method_rejections
                    FROM {self.db.payments_table}
                    WHERE status = 'rejected'
                    GROUP BY payment_method
                    ORDER BY method_rejections DESC
                """, (week_start,))

                payment_issues = cursor.fetchall()

                # إحصائيات طلبات الدعم المعلقة
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_support_requests,
                        COUNT(CASE WHEN status = 'waiting' THEN 1 END) as waiting_requests,
                        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_requests,
                        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_requests,
                        AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL
                            THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_resolution_time
                    FROM {self.db.support_queue_table}
                    WHERE created_at >= %s
                """, (week_start,))

                support_issues = cursor.fetchone()

                # المستخدمين المحظورين
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_banned,
                        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as banned_today,
                        COUNT(CASE WHEN created_at >= %s THEN 1 END) as banned_week,
                        ban_reason,
                        COUNT(*) as reason_count
                    FROM {self.db.banned_users_table}
                    GROUP BY ban_reason
                    ORDER BY reason_count DESC
                """, (week_start,))

                banned_users_stats = cursor.fetchall()

                # الاشتراكات المنتهية الصلاحية
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as expired_subscriptions,
                        COUNT(CASE WHEN expiry_date >= CURRENT_DATE - INTERVAL '3 days'
                                   AND expiry_date < CURRENT_DATE THEN 1 END) as recently_expired,
                        COUNT(CASE WHEN expiry_date >= CURRENT_DATE
                                   AND expiry_date <= CURRENT_DATE + INTERVAL '3 days' THEN 1 END) as expiring_soon
                    FROM {self.subscribers_table}
                    WHERE is_active = FALSE OR expiry_date < CURRENT_DATE
                """)

                subscription_issues = cursor.fetchone()

                # التحميلات الفاشلة
                cursor.execute("""
                    SELECT
                        COUNT(*) as failed_downloads,
                        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as failed_today,
                        COUNT(CASE WHEN created_at >= %s THEN 1 END) as failed_week,
                        file_id,
                        COUNT(*) as file_failures
                    FROM download_history
                    WHERE download_status = 'failed'
                    GROUP BY file_id
                    ORDER BY file_failures DESC
                    LIMIT 5
                """, (week_start,))

                download_issues = cursor.fetchall()

                # أخطاء النظام من سجل الأنشطة
                cursor.execute(f"""
                    SELECT
                        action_type,
                        details,
                        COUNT(*) as error_count,
                        MAX(created_at) as last_occurrence
                    FROM {self.db.activity_logs_table}
                    WHERE (action_type LIKE '%error%' OR action_type LIKE '%failed%'
                           OR details LIKE '%error%' OR details LIKE '%failed%')
                    AND created_at >= %s
                    GROUP BY action_type, details
                    ORDER BY error_count DESC
                    LIMIT 10
                """, (week_start,))

                system_errors = cursor.fetchall()

                # المستخدمين الذين واجهوا مشاكل متكررة
                cursor.execute(f"""
                    SELECT
                        p.user_id,
                        s.first_name,
                        s.username,
                        COUNT(CASE WHEN p.status = 'rejected' THEN 1 END) as payment_failures,
                        COUNT(CASE WHEN sq.status = 'cancelled' THEN 1 END) as support_cancellations
                    FROM {self.db.payments_table} p
                    LEFT JOIN {self.subscribers_table} s ON p.user_id = s.user_id
                    LEFT JOIN {self.db.support_queue_table} sq ON p.user_id = sq.user_id
                    WHERE p.created_at >= %s
                    GROUP BY p.user_id, s.first_name, s.username
                    HAVING payment_failures > 2 OR support_cancellations > 1
                    ORDER BY (payment_failures + support_cancellations) DESC
                    LIMIT 10
                """, (month_start,))

                problematic_users = cursor.fetchall()

                # إحصائيات الروابط المنتهية الصلاحية
                cursor.execute("""
                    SELECT
                        COUNT(*) as expired_links,
                        COUNT(CASE WHEN DATE(expires_at) = CURRENT_DATE THEN 1 END) as expiring_today
                    FROM download_links
                    WHERE expires_at < NOW() OR is_used = TRUE
                """)

                link_issues = cursor.fetchone()

            # فحص حالة النظام
            system_health = self._check_system_health()

            # بناء رسالة التقرير
            message = f"⚠️ **تقرير المشاكل والأخطاء الشامل**\n\n"

            # حالة النظام العامة
            message += f"🔍 **حالة النظام:**\n"
            if system_health['status'] == 'healthy':
                message += "✅ النظام يعمل بشكل طبيعي\n"
            elif system_health['status'] == 'warning':
                message += "⚠️ النظام يعمل مع بعض التحذيرات\n"
            else:
                message += "🔴 النظام يواجه مشاكل خطيرة\n"

            message += f"📊 نقاط الفحص: {system_health['checks_passed']}/{system_health['total_checks']}\n\n"

            # مشاكل المدفوعات
            if payment_issues:
                total_rejected = sum([issue['rejected_payments'] for issue in payment_issues])
                rejected_today = payment_issues[0]['rejected_today'] if payment_issues else 0
                rejected_week = payment_issues[0]['rejected_week'] if payment_issues else 0

                message += f"💳 **مشاكل المدفوعات:**\n"
                message += f"❌ إجمالي المرفوضة: {total_rejected}\n"
                message += f"📅 مرفوضة اليوم: {rejected_today}\n"
                message += f"📊 مرفوضة هذا الأسبوع: {rejected_week}\n"

                if payment_issues:
                    message += f"\n🔍 **أكثر طرق الدفع مشاكل:**\n"
                    for issue in payment_issues[:3]:
                        method = issue['payment_method'] or 'غير محدد'
                        count = issue['method_rejections']
                        message += f"• {method}: {count} مرفوضة\n"
                message += "\n"

            # مشاكل الدعم
            if support_issues:
                message += f"🎧 **مشاكل نظام الدعم:**\n"
                message += f"⏳ طلبات في الانتظار: {support_issues['waiting_requests']}\n"
                message += f"🔄 جلسات نشطة: {support_issues['active_requests']}\n"
                message += f"❌ طلبات ملغية: {support_issues['cancelled_requests']}\n"

                avg_time = support_issues['avg_resolution_time'] or 0
                if avg_time > 60:  # أكثر من ساعة
                    message += f"⚠️ متوسط وقت الحل: {avg_time:.1f} دقيقة (مرتفع!)\n"
                else:
                    message += f"⏱️ متوسط وقت الحل: {avg_time:.1f} دقيقة\n"
                message += "\n"

            # المستخدمين المحظورين
            if banned_users_stats:
                total_banned = sum([stat['total_banned'] for stat in banned_users_stats])
                banned_today = banned_users_stats[0]['banned_today'] if banned_users_stats else 0
                banned_week = banned_users_stats[0]['banned_week'] if banned_users_stats else 0

                message += f"🚫 **المستخدمين المحظورين:**\n"
                message += f"📊 إجمالي المحظورين: {total_banned}\n"
                message += f"📅 محظورين اليوم: {banned_today}\n"
                message += f"📈 محظورين هذا الأسبوع: {banned_week}\n"

                if banned_users_stats:
                    message += f"\n📋 **أسباب الحظر الأكثر شيوعاً:**\n"
                    for stat in banned_users_stats[:3]:
                        reason = stat['ban_reason'] or 'غير محدد'
                        count = stat['reason_count']
                        message += f"• {reason}: {count} حالة\n"
                message += "\n"

            # مشاكل الاشتراكات
            if subscription_issues:
                message += f"📋 **مشاكل الاشتراكات:**\n"
                message += f"❌ اشتراكات منتهية: {subscription_issues['expired_subscriptions']}\n"
                message += f"⏰ منتهية مؤخراً: {subscription_issues['recently_expired']}\n"
                message += f"⚠️ ستنتهي قريباً: {subscription_issues['expiring_soon']}\n\n"

            # مشاكل التحميل
            if download_issues:
                total_failed = sum([issue['failed_downloads'] for issue in download_issues])
                failed_today = download_issues[0]['failed_today'] if download_issues else 0
                failed_week = download_issues[0]['failed_week'] if download_issues else 0

                message += f"📁 **مشاكل التحميل:**\n"
                message += f"❌ تحميلات فاشلة: {total_failed}\n"
                message += f"📅 فاشلة اليوم: {failed_today}\n"
                message += f"📊 فاشلة هذا الأسبوع: {failed_week}\n"

                if download_issues:
                    message += f"\n🔍 **الملفات الأكثر مشاكل:**\n"
                    for issue in download_issues[:3]:
                        file_id = issue['file_id']
                        failures = issue['file_failures']
                        message += f"• ملف {file_id}: {failures} فشل\n"
                message += "\n"

            # مشاكل الروابط
            if link_issues:
                message += f"🔗 **مشاكل الروابط:**\n"
                message += f"⏰ روابط منتهية: {link_issues['expired_links']}\n"
                message += f"📅 تنتهي اليوم: {link_issues['expiring_today']}\n\n"

            # أخطاء النظام
            if system_errors:
                message += f"🔧 **أخطاء النظام (آخر أسبوع):**\n"
                for error in system_errors[:5]:
                    action = error['action_type']
                    count = error['error_count']
                    last_time = error['last_occurrence'].strftime('%m-%d %H:%M')
                    message += f"• {action}: {count} مرة (آخر: {last_time})\n"
                message += "\n"

            # المستخدمين المشكوك فيهم
            if problematic_users:
                message += f"👤 **مستخدمين يواجهون مشاكل متكررة:**\n"
                for user in problematic_users[:3]:
                    name = user['first_name'] or 'غير محدد'
                    username = user['username'] or 'غير محدد'
                    payment_fails = user['payment_failures']
                    support_cancels = user['support_cancellations']
                    message += f"• {name} (@{username})\n"
                    message += f"  💳 فشل دفع: {payment_fails} | 🎧 إلغاء دعم: {support_cancels}\n"
                message += "\n"

            # تحليل الاتجاهات
            message += f"📈 **تحليل الاتجاهات:**\n"

            # تقييم مستوى المشاكل
            total_issues = 0
            if payment_issues:
                total_issues += sum([issue['rejected_payments'] for issue in payment_issues])
            if support_issues:
                total_issues += support_issues['waiting_requests'] + support_issues['cancelled_requests']
            if download_issues:
                total_issues += sum([issue['failed_downloads'] for issue in download_issues])

            if total_issues == 0:
                message += "✅ لا توجد مشاكل كبيرة حالياً\n"
            elif total_issues < 10:
                message += "⚠️ مستوى مشاكل منخفض - مراقبة عادية\n"
            elif total_issues < 50:
                message += "🔶 مستوى مشاكل متوسط - يحتاج انتباه\n"
            else:
                message += "🔴 مستوى مشاكل مرتفع - يحتاج تدخل فوري!\n"

            message += f"📊 إجمالي المشاكل المكتشفة: {total_issues}\n\n"

            # توصيات الحلول
            message += f"💡 **توصيات الحلول:**\n"

            if payment_issues and sum([issue['rejected_payments'] for issue in payment_issues]) > 10:
                message += "• 💳 مراجعة إعدادات طرق الدفع والتحقق من صحة البيانات\n"

            if support_issues and support_issues['waiting_requests'] > 5:
                message += "• 🎧 زيادة ساعات الدعم أو إضافة مشغلين جدد\n"

            if subscription_issues and subscription_issues['expiring_soon'] > 10:
                message += "• 📧 إرسال رسائل تذكيرية للاشتراكات المنتهية قريباً\n"

            if download_issues and sum([issue['failed_downloads'] for issue in download_issues]) > 5:
                message += "• 📁 فحص سلامة الملفات وإصلاح الروابط المعطلة\n"

            if banned_users_stats and sum([stat['banned_week'] for stat in banned_users_stats]) > 5:
                message += "• 🔒 تعزيز أمان النظام ومراجعة سياسات الحظر\n"

            message += "• 🔄 تنظيف دوري للبيانات القديمة والروابط المنتهية"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("🔧 إصلاح تلقائي", callback_data="admin_auto_fix_issues"),
                types.InlineKeyboardButton("🧹 تنظيف النظام", callback_data="admin_cleanup_system")
            )
            markup.add(
                types.InlineKeyboardButton("📊 تقرير مفصل", callback_data="admin_detailed_issues"),
                types.InlineKeyboardButton("🔄 تحديث التقرير", callback_data="admin_issues_report")
            )
            markup.add(
                types.InlineKeyboardButton("📈 تقرير النشاط", callback_data="admin_activity_report"),
                types.InlineKeyboardButton("📋 قائمة التقارير", callback_data="admin_reports_menu")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show issues report: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تقرير المشاكل")

    def _check_system_health(self):
        """فحص حالة النظام العامة"""
        try:
            checks_passed = 0
            total_checks = 6
            issues = []

            # فحص قاعدة البيانات
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute("SELECT 1")
                    checks_passed += 1
            except:
                issues.append("قاعدة البيانات غير متاحة")

            # فحص جداول قاعدة البيانات الأساسية
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"SELECT COUNT(*) FROM {self.subscribers_table}")
                    cursor.execute(f"SELECT COUNT(*) FROM {self.db.payments_table}")
                    checks_passed += 1
            except:
                issues.append("جداول قاعدة البيانات تالفة")

            # فحص مساحة التخزين (تقديري)
            try:
                import shutil
                disk_usage = shutil.disk_usage('/')
                free_space_gb = disk_usage.free / (1024**3)
                if free_space_gb > 1:  # أكثر من 1 جيجا متاح
                    checks_passed += 1
                else:
                    issues.append("مساحة التخزين منخفضة")
            except:
                checks_passed += 1  # تجاهل إذا لم نتمكن من الفحص

            # فحص عدد المدفوعات المعلقة
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"SELECT COUNT(*) as pending FROM {self.db.payments_table} WHERE status = 'pending'")
                    result = cursor.fetchone()
                    if result['pending'] < 50:  # أقل من 50 معلقة
                        checks_passed += 1
                    else:
                        issues.append("عدد كبير من المدفوعات المعلقة")
            except:
                issues.append("فشل فحص المدفوعات")

            # فحص طلبات الدعم المتراكمة
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"SELECT COUNT(*) as waiting FROM {self.db.support_queue_table} WHERE status = 'waiting'")
                    result = cursor.fetchone()
                    if result['waiting'] < 20:  # أقل من 20 في الانتظار
                        checks_passed += 1
                    else:
                        issues.append("تراكم في طلبات الدعم")
            except:
                issues.append("فشل فحص نظام الدعم")

            # فحص المستخدمين المحظورين حديثاً
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"SELECT COUNT(*) as recent_bans FROM {self.db.banned_users_table} WHERE DATE(created_at) = CURRENT_DATE")
                    result = cursor.fetchone()
                    if result['recent_bans'] < 10:  # أقل من 10 محظورين اليوم
                        checks_passed += 1
                    else:
                        issues.append("عدد كبير من المحظورين اليوم")
            except:
                issues.append("فشل فحص المستخدمين المحظورين")

            # تحديد حالة النظام
            if checks_passed == total_checks:
                status = 'healthy'
            elif checks_passed >= total_checks * 0.7:  # 70% أو أكثر
                status = 'warning'
            else:
                status = 'critical'

            return {
                'status': status,
                'checks_passed': checks_passed,
                'total_checks': total_checks,
                'issues': issues
            }

        except Exception as e:
            logger.error(f"Failed to check system health: {e}")
            return {
                'status': 'unknown',
                'checks_passed': 0,
                'total_checks': 6,
                'issues': ['فشل في فحص حالة النظام']
            }

    def reduce_subscription_days(self, admin_id, user_id, days):
        """تقليل أيام من اشتراك مشترك"""
        try:
            # الحصول على معلومات المشترك الحالية
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT first_name, username, expire_time, is_active
                    FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    self.bot.send_message(admin_id, f"❌ لم يتم العثور على المشترك {user_id}")
                    return False

                if not subscriber['is_active'] or not subscriber['expire_time']:
                    self.bot.send_message(admin_id, f"❌ المشترك {user_id} غير نشط أو لا يوجد له تاريخ انتهاء")
                    return False

                # حساب تاريخ الانتهاء الجديد
                current_expire = subscriber['expire_time']
                new_expire = current_expire - (days * 86400)

                # التحقق من أن التاريخ الجديد لا يكون في الماضي
                current_time = int(datetime.now().timestamp())
                if new_expire < current_time:
                    # إذا كان التاريخ الجديد في الماضي، اجعل الاشتراك ينتهي الآن
                    new_expire = current_time
                    actual_days_reduced = int((current_expire - current_time) / 86400)
                    message_note = f"\n⚠️ تم تقليل {actual_days_reduced} يوم فقط لأن التقليل المطلوب سيجعل الاشتراك منتهي"
                else:
                    actual_days_reduced = days
                    message_note = ""

                # تحديث الاشتراك
                cursor.execute(f"""
                    UPDATE {self.subscribers_table}
                    SET expire_time = %s, updated_at = %s
                    WHERE user_id = %s
                """, (new_expire, datetime.now(), user_id))

                updated_count = cursor.rowcount

            if updated_count > 0:
                new_expire_date = datetime.fromtimestamp(new_expire).strftime('%Y-%m-%d %H:%M')

                message = f"✅ **تم تقليل أيام الاشتراك بنجاح!**\n\n"
                message += f"👤 **المشترك:**\n"
                message += f"📝 الاسم: {subscriber['first_name'] or 'غير محدد'}\n"
                message += f"📱 اسم المستخدم: @{subscriber['username'] or 'غير محدد'}\n"
                message += f"🆔 المعرف: `{user_id}`\n\n"
                message += f"⏰ **التقليل:**\n"
                message += f"📅 تم تقليل: {actual_days_reduced} {'يوم' if actual_days_reduced == 1 else 'أيام'}\n"
                message += f"📅 ينتهي الآن في: {new_expire_date}\n"
                message += message_note
                message += f"\n📝 تم تسجيل هذا الإجراء في سجل النشاط"

                # إشعار المشترك بالتقليل
                try:
                    subscriber_message = f"⚠️ **تم تعديل اشتراكك**\n\n"
                    subscriber_message += f"📅 تم تقليل {actual_days_reduced} {'يوم' if actual_days_reduced == 1 else 'أيام'} من اشتراكك\n"
                    subscriber_message += f"📅 اشتراكك ينتهي الآن في: {new_expire_date}\n\n"

                    if new_expire <= current_time:
                        subscriber_message += f"⚠️ اشتراكك انتهى الآن\n"
                        subscriber_message += f"🔄 يمكنك التجديد في أي وقت"
                    else:
                        days_left = int((new_expire - current_time) / 86400)
                        subscriber_message += f"⏰ متبقي: {days_left} {'يوم' if days_left == 1 else 'أيام'}\n"
                        subscriber_message += f"💡 لا تنس التجديد قبل انتهاء الاشتراك"

                    self.bot.send_message(user_id, subscriber_message, parse_mode='Markdown')
                except Exception as e:
                    logger.warning(f"Failed to notify subscriber {user_id} of reduction: {e}")

                # تسجيل النشاط
                self.db.log_activity(admin_id, "reduce_subscription_days", f"Reduced subscription for {user_id} by {actual_days_reduced} days")

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("👤 عرض المشترك", callback_data=f"view_subscriber_{user_id}"),
                    types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers")
                )
                markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

                self.bot.send_message(admin_id, message, reply_markup=markup, parse_mode='Markdown')
                return True
            else:
                self.bot.send_message(admin_id, "❌ فشل في تقليل أيام الاشتراك")
                return False

        except Exception as e:
            logger.error(f"Failed to reduce subscription days: {e}")
            self.bot.send_message(admin_id, "❌ حدث خطأ في تقليل أيام الاشتراك")
            return False

    def quick_extend_subscription(self, admin_id, user_id):
        """تمديد سريع للاشتراك بخيارات محددة مسبقاً"""
        try:
            # الحصول على معلومات المشترك
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT first_name, username, expire_time, is_active
                    FROM {self.subscribers_table}
                    WHERE user_id = %s
                """, (user_id,))

                subscriber = cursor.fetchone()

                if not subscriber:
                    self.bot.send_message(admin_id, f"❌ لم يتم العثور على المشترك {user_id}")
                    return

            # حساب الأيام المتبقية
            if subscriber['is_active'] and subscriber['expire_time']:
                days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))
                expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d %H:%M')
                status_text = f"✅ نشط ({days_left} يوم متبقي)\n📅 ينتهي في: {expire_date}"
            else:
                status_text = "❌ غير نشط"

            message = f"⏰ **تمديد سريع للاشتراك**\n\n"
            message += f"👤 **المشترك:**\n"
            message += f"📝 الاسم: {subscriber['first_name'] or 'غير محدد'}\n"
            message += f"📱 اسم المستخدم: @{subscriber['username'] or 'غير محدد'}\n"
            message += f"🆔 المعرف: `{user_id}`\n\n"
            message += f"📊 **الحالة الحالية:**\n{status_text}\n\n"
            message += f"⏰ **اختر مدة التمديد:**"

            markup = types.InlineKeyboardMarkup(row_width=2)

            # خيارات التمديد السريع
            quick_options = [
                ("7 أيام", 7),
                ("15 يوم", 15),
                ("30 يوم", 30),
                ("60 يوم", 60),
                ("90 يوم", 90),
                ("180 يوم", 180)
            ]

            for text, days in quick_options:
                markup.add(types.InlineKeyboardButton(
                    f"➕ {text}",
                    callback_data=f"quick_extend_{user_id}_{days}"
                ))

            markup.add(
                types.InlineKeyboardButton("📝 تمديد مخصص", callback_data=f"extend_subscriber_{user_id}"),
                types.InlineKeyboardButton("➖ تقليل أيام", callback_data=f"reduce_days_subscriber_{user_id}")
            )
            markup.add(types.InlineKeyboardButton("🔙 عرض المشترك", callback_data=f"view_subscriber_{user_id}"))

            self.bot.send_message(admin_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show quick extend menu: {e}")
            self.bot.send_message(admin_id, "❌ حدث خطأ في عرض قائمة التمديد السريع")

    def show_settings_menu(self, chat_id):
        """عرض قائمة الإعدادات"""
        markup = types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            types.InlineKeyboardButton("💰 إعدادات الاشتراك", callback_data="admin_subscription_settings"),
            types.InlineKeyboardButton("💳 إعدادات الدفع", callback_data="admin_payment_settings")
        )

        markup.add(
            types.InlineKeyboardButton("🔔 إعدادات التنبيهات", callback_data="admin_notification_settings"),
            types.InlineKeyboardButton("👥 إعدادات الجروب", callback_data="admin_group_settings")
        )

        markup.add(
            types.InlineKeyboardButton("📊 إعدادات التقارير", callback_data="admin_report_settings"),
            types.InlineKeyboardButton("🛡️ إعدادات الأمان", callback_data="admin_security_settings")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_menu"))

        message = "⚙️ إعدادات النظام\n\n" \
                 "اختر نوع الإعدادات التي تريد تعديلها:"

        self.bot.send_message(chat_id, message, reply_markup=markup)

    def search_subscriber(self, chat_id, search_query):
        """البحث عن مشترك بطريقة محسنة"""
        try:
            logger.info(f"Searching for subscriber with query: {search_query}")

            with self.db.get_db_cursor() as cursor:
                # البحث المحسن بمعرف المستخدم أو اسم المستخدم أو الاسم الأول
                cursor.execute(f"""
                    SELECT user_id, username, first_name, join_time, expire_time,
                           is_active, subscription_count, total_paid, created_at
                    FROM {self.subscribers_table}
                    WHERE user_id::text = %s
                    OR username ILIKE %s
                    OR first_name ILIKE %s
                    OR user_id::text LIKE %s
                    ORDER BY
                        CASE
                            WHEN user_id::text = %s THEN 1
                            WHEN username ILIKE %s THEN 2
                            WHEN first_name ILIKE %s THEN 3
                            ELSE 4
                        END,
                        join_time DESC
                    LIMIT 15
                """, (
                    search_query, f"%{search_query}%", f"%{search_query}%", f"%{search_query}%",
                    search_query, f"{search_query}%", f"{search_query}%"
                ))

                results = cursor.fetchall()
                logger.info(f"Search returned {len(results)} results")

            if not results:
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("🔍 بحث جديد", callback_data="admin_search_subscriber"),
                    types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers")
                )

                message = f"❌ **لم يتم العثور على نتائج**\n\n" \
                         f"🔍 البحث عن: `{search_query}`\n\n" \
                         f"💡 **اقتراحات:**\n" \
                         f"• تأكد من صحة المعرف أو الاسم\n" \
                         f"• جرب البحث بجزء من الاسم\n" \
                         f"• تأكد من أن المستخدم مشترك فعلاً\n\n" \
                         f"📝 **أمثلة صحيحة:**\n" \
                         f"• `123456789` (معرف كامل)\n" \
                         f"• `username` (بدون @)\n" \
                         f"• `محمد` (جزء من الاسم)"

                self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')
                return

            # عرض النتائج مع تفاصيل محسنة
            message = f"🔍 **نتائج البحث عن:** `{search_query}`\n"
            message += f"📊 **عدد النتائج:** {len(results)}\n\n"

            for i, subscriber in enumerate(results, 1):
                first_name = subscriber['first_name'] or 'غير محدد'
                username = subscriber['username'] or 'غير محدد'

                # تحديد حالة الاشتراك
                if subscriber['is_active']:
                    if subscriber.get('expire_time'):
                        days_left = int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400)
                        if days_left > 0:
                            status = f"✅ نشط ({days_left} يوم متبقي)"
                        else:
                            status = "⚠️ منتهي حديثاً"
                    else:
                        status = "✅ نشط"
                else:
                    status = "❌ منتهي الصلاحية"

                # تاريخ الانضمام
                if subscriber.get('join_time') and subscriber['join_time'] > 86400:
                    join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d')
                else:
                    join_date = 'غير محدد'

                # تاريخ الانتهاء
                if subscriber.get('expire_time'):
                    expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d')
                else:
                    expire_date = 'غير محدد'

                message += f"**{i}. {first_name}** (@{username})\n" \
                          f"🆔 المعرف: `{subscriber['user_id']}`\n" \
                          f"📅 انضم: {join_date}\n" \
                          f"⏰ ينتهي: {expire_date}\n" \
                          f"📊 الحالة: {status}\n" \
                          f"🔄 التجديدات: {subscriber.get('subscription_count', 0)}\n" \
                          f"💰 المدفوعات: {float(subscriber.get('total_paid', 0)):.2f} جنيه\n\n"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)

            # إذا كانت النتائج قليلة، أضف أزرار إدارة سريعة
            if len(results) == 1:
                subscriber = results[0]
                markup.add(
                    types.InlineKeyboardButton("👤 تفاصيل المشترك", callback_data=f"view_subscriber_{subscriber['user_id']}"),
                    types.InlineKeyboardButton("⚙️ إدارة المشترك", callback_data=f"manage_subscriber_{subscriber['user_id']}")
                )

            markup.add(
                types.InlineKeyboardButton("🔍 بحث جديد", callback_data="admin_search_subscriber"),
                types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers")
            )
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to search subscriber: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في البحث عن المشترك")

    def add_test_subscriber(self, chat_id):
        """إضافة مشترك تجريبي للاختبار"""
        try:
            # إنشاء مشترك تجريبي
            test_user_id = 999999999  # معرف تجريبي
            test_username = "test_user"
            test_first_name = "مشترك تجريبي"

            # التحقق من وجود المستخدم التجريبي مسبقاً
            existing = self.db.get_subscriber(test_user_id)
            if existing:
                self.bot.send_message(chat_id, "⚠️ المشترك التجريبي موجود مسبقاً")
                return

            # إضافة المشترك التجريبي
            expire_time = int((datetime.now() + timedelta(days=30)).timestamp())

            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.subscribers_table}
                    (user_id, username, first_name, is_active, expire_time, join_time, subscription_count, total_paid)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (test_user_id, test_username, test_first_name, True, expire_time, int(datetime.now().timestamp()), 1, 50.0))

            self.bot.send_message(chat_id,
                f"✅ تم إضافة المشترك التجريبي بنجاح!\n\n"
                f"👤 الاسم: {test_first_name}\n"
                f"🆔 المعرف: {test_user_id}\n"
                f"⏰ ينتهي الاشتراك: {datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d %H:%M')}\n\n"
                f"🔄 يمكنك الآن عرض قائمة المشتركين")

            # تسجيل النشاط
            self.db.log_activity(chat_id, "add_test_subscriber", f"Added test subscriber {test_user_id}")

        except Exception as e:
            logger.error(f"Failed to add test subscriber: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إضافة المشترك التجريبي")

    def add_subscriber_manually(self, chat_id, target_user_id):
        """إضافة مشترك يدوياً مع إجراءات أمنية شاملة"""
        try:
            logger.info(f"Admin {chat_id} attempting to add subscriber {target_user_id}")

            # التحقق من وجود المستخدم مسبقاً
            existing = self.db.get_subscriber(target_user_id)
            if existing:
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("👤 عرض التفاصيل", callback_data=f"view_subscriber_{target_user_id}"),
                    types.InlineKeyboardButton("⏰ تمديد الاشتراك", callback_data=f"extend_subscription_{target_user_id}")
                )
                markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

                self.bot.send_message(
                    chat_id,
                    f"⚠️ **المستخدم موجود مسبقاً**\n\n"
                    f"🆔 المعرف: `{target_user_id}`\n"
                    f"📊 الحالة: {'✅ نشط' if existing.get('is_active') else '❌ منتهي'}\n\n"
                    f"💡 يمكنك عرض تفاصيله أو تمديد اشتراكه",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return False

            # محاولة الحصول على معلومات المستخدم من تليجرام
            try:
                user_info = self.bot.get_chat(target_user_id)
                username = user_info.username
                first_name = user_info.first_name
                user_exists_on_telegram = True
            except Exception as e:
                logger.warning(f"Could not get user info for {target_user_id}: {e}")
                username = None
                first_name = "مستخدم جديد"
                user_exists_on_telegram = False

            # حساب تاريخ انتهاء الاشتراك
            now = datetime.now()
            expire_time = int((now + timedelta(days=self.config.SUBSCRIPTION_DURATION_DAYS)).timestamp())
            expire_date = datetime.fromtimestamp(expire_time)

            # إضافة المشترك إلى قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.subscribers_table}
                    (user_id, username, first_name, is_active, expire_time, join_time,
                     subscription_count, total_paid, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    target_user_id, username, first_name, True, expire_time,
                    int(now.timestamp()), 1, 0, now
                ))

                added_count = cursor.rowcount

            if added_count == 0:
                self.bot.send_message(chat_id, "❌ فشل في إضافة المشترك إلى قاعدة البيانات")
                return False

            # إنشاء رابط دعوة آمن
            invite_link = None
            group_action = "لم يتم إنشاء رابط"

            try:
                from invite_manager import InviteManager
                invite_manager = InviteManager(self.bot, self.db, self.config)

                # إنشاء رابط دعوة آمن
                invite_result = invite_manager.create_secure_invite_link(
                    target_user_id,
                    expire_time,
                    "manual_admin_addition"
                )

                if invite_result['success']:
                    invite_link = invite_result['invite_link']
                    group_action = "تم إنشاء رابط دعوة آمن"
                else:
                    group_action = f"فشل في إنشاء الرابط: {invite_result.get('error', 'خطأ غير معروف')}"

            except Exception as e:
                logger.error(f"Failed to create secure invite link: {e}")

                # محاولة إنشاء رابط تقليدي
                try:
                    invite_link_obj = self.bot.create_chat_invite_link(
                        self.config.GROUP_ID,
                        member_limit=1,
                        expire_date=expire_time
                    )
                    invite_link = invite_link_obj.invite_link
                    group_action = "تم إنشاء رابط دعوة تقليدي"
                except Exception as link_error:
                    logger.error(f"Failed to create traditional invite link: {link_error}")
                    group_action = "فشل في إنشاء رابط الدعوة"

            # إرسال رسالة ترحيب للمستخدم الجديد
            welcome_sent = False
            if user_exists_on_telegram:
                try:
                    welcome_message = f"🎉 **مرحباً بك في خدمة الاشتراك!**\n\n" \
                                    f"✅ **تم تفعيل اشتراكك بنجاح**\n" \
                                    f"👤 الاسم: {first_name}\n" \
                                    f"📅 مدة الاشتراك: {self.config.SUBSCRIPTION_DURATION_DAYS} يوم\n" \
                                    f"⏰ ينتهي في: {expire_date.strftime('%Y-%m-%d')}\n\n"

                    if invite_link:
                        welcome_message += f"🔗 **رابط الانضمام للجروب:**\n{invite_link}\n\n"
                        welcome_message += f"⚠️ **ملاحظة:** هذا الرابط خاص بك فقط ولا يجب مشاركته\n\n"

                    welcome_message += f"📞 **للدعم:** راسل الإدارة مباشرة\n" \
                                     f"🔄 **للتجديد:** استخدم نفس البوت قبل انتهاء الاشتراك"

                    self.bot.send_message(target_user_id, welcome_message, parse_mode='Markdown')
                    welcome_sent = True
                except Exception as msg_error:
                    logger.warning(f"Could not send welcome message to {target_user_id}: {msg_error}")

            # إعداد رسالة النجاح للأدمن
            success_message = f"✅ **تم إضافة المشترك بنجاح!**\n\n"
            success_message += f"👤 **تفاصيل المشترك:**\n"
            success_message += f"📝 الاسم: {first_name}\n"
            success_message += f"📱 اسم المستخدم: @{username or 'غير محدد'}\n"
            success_message += f"🆔 المعرف: `{target_user_id}`\n"
            success_message += f"📅 تاريخ الانضمام: {now.strftime('%Y-%m-%d %H:%M')}\n"
            success_message += f"⏰ تاريخ الانتهاء: {expire_date.strftime('%Y-%m-%d')}\n\n"

            success_message += f"🔧 **الإجراءات المتخذة:**\n"
            success_message += f"• {'✅' if user_exists_on_telegram else '⚠️'} التحقق من وجود المستخدم على تليجرام\n"
            success_message += f"• ✅ إضافة البيانات إلى قاعدة البيانات\n"
            success_message += f"• {'✅' if invite_link else '❌'} {group_action}\n"
            success_message += f"• {'✅' if welcome_sent else '❌'} إرسال رسالة الترحيب\n"
            success_message += f"• ✅ تسجيل النشاط في السجل"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("👤 عرض التفاصيل", callback_data=f"view_subscriber_{target_user_id}"),
                types.InlineKeyboardButton("➕ إضافة مشترك آخر", callback_data="admin_add_subscriber")
            )
            markup.add(
                types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers"),
                types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers")
            )

            self.bot.send_message(chat_id, success_message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(
                chat_id,
                "add_subscriber_manually",
                f"Added user {target_user_id} ({first_name}), invite: {bool(invite_link)}"
            )

            logger.info(f"Successfully added subscriber {target_user_id} by admin {chat_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to add subscriber manually: {e}")

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 إدارة المشتركين", callback_data="admin_subscribers"))

            self.bot.send_message(
                chat_id,
                f"❌ **حدث خطأ في إضافة المشترك**\n\n"
                f"🔧 تفاصيل الخطأ: {str(e)}\n\n"
                f"💡 حاول مرة أخرى أو راجع سجل الأخطاء",
                reply_markup=markup,
                parse_mode='Markdown'
            )
            return False

    def remove_subscriber_manually(self, chat_id, target_user_id):
        """حذف مشترك يدوياً مع إلغاء روابط الدعوة"""
        try:
            # التحقق من وجود المستخدم
            subscriber = self.db.get_subscriber(target_user_id)
            if not subscriber:
                self.bot.send_message(chat_id, f"❌ المستخدم {target_user_id} غير موجود في النظام")
                return

            # استخدام مدير روابط الدعوة لإزالة المستخدم بأمان
            from invite_manager import InviteManager
            invite_manager = InviteManager(self.bot, self.db, self.config)

            removal_result = invite_manager.handle_user_removal(target_user_id, "manual_admin_removal")

            # حذف المشترك من قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"DELETE FROM {self.subscribers_table} WHERE user_id = %s", (target_user_id,))

            # إرسال رسالة للمستخدم
            try:
                self.bot.send_message(target_user_id,
                    "❌ تم إنهاء اشتراكك بواسطة الإدارة\n\n"
                    "📞 للاستفسار: راسل الإدارة")
            except:
                pass

            self.bot.send_message(chat_id,
                f"✅ تم حذف المشترك بنجاح!\n\n"
                f"👤 المستخدم: {subscriber['first_name']} (@{subscriber['username'] or 'غير محدد'})\n"
                f"🆔 المعرف: {target_user_id}\n\n"
                f"🔒 الإجراءات الأمنية المتخذة:\n"
                f"• تم إلغاء {removal_result['links_revoked']} رابط دعوة\n"
                f"• تم {'إزالته من الجروب' if removal_result['group_removed'] else 'محاولة إزالته من الجروب'}\n"
                f"• تم حذف جميع بياناته من النظام")

            # تسجيل النشاط
            self.db.log_activity(chat_id, "remove_subscriber_manually",
                               f"Removed user {target_user_id}, revoked {removal_result['links_revoked']} links")

        except Exception as e:
            logger.error(f"Failed to remove subscriber manually: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في حذف المشترك")

    def send_reminder_to_all_expiring(self, chat_id):
        """إرسال تذكير لجميع المشتركين المنتهية صلاحيتهم قريباً"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, first_name, expire_time FROM {self.subscribers_table}
                    WHERE is_active = TRUE
                    AND expire_time <= %s
                    AND expire_time > %s
                    ORDER BY expire_time ASC
                """, (
                    int((datetime.now() + timedelta(days=7)).timestamp()),
                    int(datetime.now().timestamp())
                ))
                subscribers = cursor.fetchall()

            if not subscribers:
                self.bot.send_message(chat_id, "❌ لا يوجد مشتركين ستنتهي صلاحيتهم قريباً")
                return

            sent_count = 0
            failed_count = 0

            for subscriber in subscribers:
                try:
                    days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))

                    reminder_msg = f"⚠️ تذكير: اشتراكك سينتهي قريباً!\n\n" \
                                  f"⏰ ينتهي خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}\n" \
                                  f"💳 جدد اشتراكك الآن لتجنب الانقطاع\n" \
                                  f"📞 للمساعدة: راسل الإدارة"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(types.InlineKeyboardButton("💳 تجديد الاشتراك", callback_data="payment_methods"))

                    self.bot.send_message(subscriber['user_id'], reminder_msg, reply_markup=markup)
                    sent_count += 1
                except Exception as e:
                    logger.error(f"Failed to send reminder to user {subscriber['user_id']}: {e}")
                    failed_count += 1

            result_message = f"📧 تم إرسال التذكيرات:\n\n" \
                           f"✅ نجح: {sent_count}\n" \
                           f"❌ فشل: {failed_count}\n" \
                           f"📊 الإجمالي: {len(subscribers)}"

            self.bot.send_message(chat_id, result_message)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "mass_reminder", f"Sent reminders to {sent_count} users")

        except Exception as e:
            logger.error(f"Failed to send mass reminders: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إرسال التذكيرات")

    def check_group_settings(self, chat_id):
        """فحص إعدادات الجروب والبوت"""
        try:
            message = "🔍 فحص إعدادات الجروب:\n\n"

            # فحص معرف الجروب
            message += f"🆔 معرف الجروب المُعين: {self.config.GROUP_ID}\n"

            # محاولة الحصول على معلومات الجروب
            try:
                chat_info = self.bot.get_chat(self.config.GROUP_ID)
                message += f"✅ الجروب موجود: {chat_info.title}\n"
                message += f"📝 نوع الجروب: {chat_info.type}\n"

                # فحص صلاحيات البوت
                try:
                    bot_member = self.bot.get_chat_member(self.config.GROUP_ID, self.bot.get_me().id)
                    message += f"👤 حالة البوت: {bot_member.status}\n"

                    if bot_member.status == 'administrator':
                        message += "✅ البوت مشرف في الجروب\n"

                        # فحص صلاحيات محددة
                        if hasattr(bot_member, 'can_invite_users') and bot_member.can_invite_users:
                            message += "✅ البوت يمكنه دعوة المستخدمين\n"
                        else:
                            message += "❌ البوت لا يمكنه دعوة المستخدمين\n"

                        # اختبار إنشاء رابط دعوة
                        try:
                            test_link = self.bot.create_chat_invite_link(
                                self.config.GROUP_ID,
                                member_limit=1,
                                expire_date=int((datetime.now() + timedelta(minutes=1)).timestamp())
                            )
                            message += "✅ اختبار إنشاء رابط الدعوة نجح\n"
                            message += f"🔗 رابط تجريبي: {test_link.invite_link}\n"

                            # حذف الرابط التجريبي
                            try:
                                self.bot.revoke_chat_invite_link(self.config.GROUP_ID, test_link.invite_link)
                                message += "✅ تم حذف الرابط التجريبي\n"
                            except:
                                message += "⚠️ لم يتم حذف الرابط التجريبي\n"

                        except Exception as link_error:
                            message += f"❌ فشل اختبار إنشاء رابط الدعوة: {str(link_error)}\n"

                    else:
                        message += f"❌ البوت ليس مشرف (الحالة: {bot_member.status})\n"

                except Exception as member_error:
                    message += f"❌ فشل فحص صلاحيات البوت: {str(member_error)}\n"

            except Exception as chat_error:
                message += f"❌ فشل الوصول للجروب: {str(chat_error)}\n"
                message += "🔧 تحقق من:\n"
                message += "• صحة معرف الجروب (GROUP_ID)\n"
                message += "• إضافة البوت للجروب\n"
                message += "• منح البوت صلاحيات الإدارة\n"

            # إرشادات الإصلاح
            message += "\n🛠️ خطوات الإصلاح:\n"
            message += "1️⃣ تأكد من إضافة البوت للجروب\n"
            message += "2️⃣ اجعل البوت مشرف في الجروب\n"
            message += "3️⃣ امنح البوت صلاحية 'دعوة المستخدمين'\n"
            message += "4️⃣ تأكد من صحة GROUP_ID في ملف .env\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔄 إعادة الفحص", callback_data="admin_check_group"),
                types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to check group settings: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في فحص إعدادات الجروب: {str(e)}")

    def test_subscribers_functions(self, chat_id):
        """اختبار دوال إدارة المشتركين"""
        try:
            logger.info(f"Testing subscribers functions for admin {chat_id}")

            message = "🧪 اختبار دوال إدارة المشتركين:\n\n"

            # اختبار الاتصال بقاعدة البيانات
            try:
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {self.subscribers_table}")
                    result = cursor.fetchone()
                    total_subscribers = result['count']
                    message += f"✅ قاعدة البيانات: متصلة ({total_subscribers} مشترك)\n"
            except Exception as db_error:
                message += f"❌ قاعدة البيانات: خطأ - {str(db_error)}\n"

            # اختبار الدوال
            functions_to_test = [
                'show_subscribers_management',
                'show_subscribers_list',
                'show_expiring_soon',
                'show_expired_subscribers',
                'search_subscriber',
                'add_subscriber_manually',
                'remove_subscriber_manually',
                'cleanup_expired_subscribers',
                'send_reminder_to_all_expiring'
            ]

            for func_name in functions_to_test:
                if hasattr(self, func_name):
                    message += f"✅ {func_name}: موجودة\n"
                else:
                    message += f"❌ {func_name}: مفقودة\n"

            # اختبار البوت
            try:
                bot_info = self.bot.get_me()
                message += f"✅ البوت: متصل (@{bot_info.username})\n"
            except Exception as bot_error:
                message += f"❌ البوت: خطأ - {str(bot_error)}\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔄 إعادة الاختبار", callback_data="admin_test_subscribers"),
                types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to test subscribers functions: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في اختبار الدوال: {str(e)}")

    def fix_group_settings(self, chat_id):
        """إصلاح إعدادات الجروب"""
        try:
            message = "🔧 إصلاح إعدادات الجروب\n\n"

            # فحص الجروب أولاً
            try:
                chat_info = self.bot.get_chat(self.config.GROUP_ID)
                message += f"✅ تم العثور على الجروب: {chat_info.title}\n"
                message += f"🆔 معرف الجروب: {self.config.GROUP_ID}\n"
                message += f"👥 عدد الأعضاء: {self.bot.get_chat_member_count(self.config.GROUP_ID)}\n\n"

                # فحص صلاحيات البوت
                bot_member = self.bot.get_chat_member(self.config.GROUP_ID, self.bot.get_me().id)

                if bot_member.status == 'administrator':
                    message += "✅ البوت مشرف في الجروب\n"

                    # فحص الصلاحيات المطلوبة
                    required_permissions = {
                        'can_invite_users': 'دعوة المستخدمين',
                        'can_restrict_members': 'حظر/إزالة الأعضاء',
                        'can_delete_messages': 'حذف الرسائل'
                    }

                    missing_permissions = []
                    for perm, desc in required_permissions.items():
                        if hasattr(bot_member, perm) and getattr(bot_member, perm):
                            message += f"✅ {desc}: متاح\n"
                        else:
                            message += f"❌ {desc}: غير متاح\n"
                            missing_permissions.append(desc)

                    if missing_permissions:
                        message += f"\n⚠️ صلاحيات مفقودة:\n"
                        for perm in missing_permissions:
                            message += f"• {perm}\n"
                        message += "\n🔧 الحلول:\n"
                        message += "1. اذهب لإعدادات الجروب\n"
                        message += "2. اختر 'المشرفين'\n"
                        message += "3. اختر البوت\n"
                        message += "4. فعّل الصلاحيات المفقودة\n"
                    else:
                        message += "\n✅ جميع الصلاحيات متوفرة!\n"

                        # اختبار إنشاء رابط دعوة
                        try:
                            test_link = self.bot.create_chat_invite_link(
                                self.config.GROUP_ID,
                                member_limit=1,
                                expire_date=int((datetime.now() + timedelta(minutes=1)).timestamp())
                            )
                            message += "✅ اختبار إنشاء رابط الدعوة: نجح\n"

                            # حذف الرابط التجريبي
                            try:
                                self.bot.revoke_chat_invite_link(self.config.GROUP_ID, test_link.invite_link)
                                message += "✅ تم حذف الرابط التجريبي\n"
                            except:
                                pass

                        except Exception as link_error:
                            message += f"❌ اختبار إنشاء رابط الدعوة: فشل\n"
                            message += f"السبب: {str(link_error)}\n"

                else:
                    message += f"❌ البوت ليس مشرف (الحالة: {bot_member.status})\n"
                    message += "\n🔧 الحل:\n"
                    message += "1. اذهب لإعدادات الجروب\n"
                    message += "2. اختر 'المشرفين'\n"
                    message += "3. أضف البوت كمشرف\n"
                    message += "4. امنحه الصلاحيات المطلوبة\n"

            except Exception as group_error:
                message += f"❌ خطأ في الوصول للجروب: {str(group_error)}\n"
                message += "\n🔧 الحلول المحتملة:\n"
                message += "1. تأكد من صحة GROUP_ID في ملف .env\n"
                message += "2. تأكد من إضافة البوت للجروب\n"
                message += "3. تأكد من أن الجروب ليس محذوف\n"
                message += f"4. GROUP_ID الحالي: {self.config.GROUP_ID}\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔄 إعادة الفحص", callback_data="admin_fix_group"),
                types.InlineKeyboardButton("📋 معلومات الجروب", callback_data="admin_group_info")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to fix group settings: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في إصلاح إعدادات الجروب: {str(e)}")

    def show_group_info(self, chat_id):
        """عرض معلومات الجروب التفصيلية"""
        try:
            message = "📋 معلومات الجروب التفصيلية\n\n"

            try:
                chat_info = self.bot.get_chat(self.config.GROUP_ID)

                message += f"📛 اسم الجروب: {chat_info.title}\n"
                message += f"🆔 معرف الجروب: {chat_info.id}\n"
                message += f"📝 الوصف: {chat_info.description or 'لا يوجد'}\n"
                message += f"🔗 اسم المستخدم: @{chat_info.username or 'لا يوجد'}\n"
                message += f"👥 عدد الأعضاء: {self.bot.get_chat_member_count(self.config.GROUP_ID)}\n"
                message += f"📅 تاريخ الإنشاء: غير متاح\n\n"

                # معلومات البوت في الجروب
                bot_info = self.bot.get_me()
                bot_member = self.bot.get_chat_member(self.config.GROUP_ID, bot_info.id)

                message += f"🤖 معلومات البوت:\n"
                message += f"• الاسم: {bot_info.first_name}\n"
                message += f"• المعرف: @{bot_info.username}\n"
                message += f"• الحالة: {bot_member.status}\n"

                if bot_member.status == 'administrator':
                    message += f"• تاريخ الترقية: {bot_member.until_date or 'غير محدد'}\n"

                message += "\n🔧 إعدادات البيئة:\n"
                message += f"• GROUP_ID: {self.config.GROUP_ID}\n"
                message += f"• ADMIN_ID: {self.config.ADMIN_ID}\n"
                message += f"• BOT_TOKEN: {'✅ موجود' if self.config.API_TOKEN else '❌ مفقود'}\n"

            except Exception as info_error:
                message += f"❌ خطأ في جلب معلومات الجروب: {str(info_error)}\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔧 إصلاح الجروب", callback_data="admin_fix_group"),
                types.InlineKeyboardButton("👥 فحص الجروب", callback_data="admin_check_group")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

        except Exception as e:
            logger.error(f"Failed to show group info: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في عرض معلومات الجروب: {str(e)}")

    def get_group_id_helper(self, chat_id):
        """مساعد للحصول على GROUP_ID"""
        try:
            message = "🆔 الحصول على GROUP_ID\n\n"
            message += "📋 لتحديد GROUP_ID الصحيح، اتبع هذه الخطوات:\n\n"

            message += "🔹 **الطريقة الأولى - استخدام البوت:**\n"
            message += "1️⃣ أضف البوت @userinfobot للجروب\n"
            message += "2️⃣ اكتب `/id` في الجروب\n"
            message += "3️⃣ انسخ الرقم الذي يبدأ بـ `-100`\n\n"

            message += "🔹 **الطريقة الثانية - من رابط الجروب:**\n"
            message += f"🔗 رابط الجروب الحالي:\n`https://t.me/+ucUhjTwuvcBmNTRk`\n\n"

            message += "🔹 **الطريقة الثالثة - اختبار البوت:**\n"
            message += "1️⃣ تأكد من إضافة هذا البوت للجروب\n"
            message += "2️⃣ اجعل البوت مشرف\n"
            message += "3️⃣ اكتب `/get_chat_id` في الجروب\n\n"

            message += "📝 **معلومات مهمة:**\n"
            message += f"• GROUP_ID الحالي في النظام: `{self.config.GROUP_ID}`\n"
            message += "• يجب أن يكون GROUP_ID رقم سالب يبدأ بـ `-100`\n"
            message += "• مثال: `-1001234567890`\n\n"

            message += "⚠️ **إذا كان GROUP_ID خاطئ:**\n"
            message += "1. احصل على GROUP_ID الصحيح\n"
            message += "2. حدث ملف `.env` على الخادم\n"
            message += "3. أعد تشغيل البوت\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔧 إصلاح الجروب", callback_data="admin_fix_group"),
                types.InlineKeyboardButton("📋 معلومات الجروب", callback_data="admin_group_info")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show group ID helper: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في عرض مساعد GROUP_ID: {str(e)}")

    def update_group_link_helper(self, chat_id):
        """مساعد لتحديث رابط الجروب"""
        try:
            message = "🔗 تحديث رابط الجروب\n\n"
            message += f"🔗 **الرابط الحالي:**\n`https://t.me/+ucUhjTwuvcBmNTRk`\n\n"

            message += "📋 **خطوات التحديث:**\n\n"

            message += "1️⃣ **تحديث GROUP_ID:**\n"
            message += "• احصل على GROUP_ID الصحيح\n"
            message += "• حدث ملف `.env` على الخادم\n"
            message += "• أعد تشغيل البوت\n\n"

            message += "2️⃣ **إعداد صلاحيات البوت:**\n"
            message += "• أضف البوت للجروب\n"
            message += "• اجعله مشرف\n"
            message += "• امنحه صلاحية 'دعوة المستخدمين'\n\n"

            message += "3️⃣ **اختبار النظام:**\n"
            message += "• استخدم 'إصلاح الجروب' للتحقق\n"
            message += "• اختبر إنشاء رابط دعوة\n\n"

            message += "🎯 **النتيجة المطلوبة:**\n"
            message += "✅ البوت يستطيع إنشاء روابط دعوة تلقائية\n"
            message += "✅ المشتركين الجدد يحصلون على رابط الجروب فوراً\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🆔 الحصول على GROUP_ID", callback_data="admin_get_group_id"),
                types.InlineKeyboardButton("🔧 إصلاح الجروب", callback_data="admin_fix_group")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show group link helper: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في عرض مساعد تحديث الرابط: {str(e)}")

    def fix_join_dates(self, chat_id):
        """إصلاح تواريخ الانضمام المفقودة"""
        try:
            message = "📅 إصلاح تواريخ الانضمام\n\n"

            with self.db.get_db_cursor() as cursor:
                # البحث عن المشتركين بدون تاريخ انضمام
                cursor.execute(f"""
                    SELECT user_id, first_name, username, expire_time
                    FROM {self.subscribers_table}
                    WHERE join_time IS NULL OR join_time = 0
                """)

                subscribers_without_join_date = cursor.fetchall()

                if not subscribers_without_join_date:
                    message += "✅ جميع المشتركين لديهم تواريخ انضمام صحيحة!"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

                    self.bot.send_message(chat_id, message, reply_markup=markup)
                    return

                message += f"🔍 تم العثور على {len(subscribers_without_join_date)} مشترك بدون تاريخ انضمام\n\n"

                # إصلاح التواريخ
                fixed_count = 0
                current_time = int(datetime.now().timestamp())

                for subscriber in subscribers_without_join_date:
                    try:
                        # استخدام تاريخ انتهاء الاشتراك لتقدير تاريخ الانضمام
                        if subscriber['expire_time']:
                            # تقدير تاريخ الانضمام (30 يوم قبل انتهاء الاشتراك)
                            estimated_join_time = subscriber['expire_time'] - (30 * 24 * 60 * 60)
                        else:
                            # استخدام التاريخ الحالي إذا لم يكن هناك تاريخ انتهاء
                            estimated_join_time = current_time

                        # تحديث تاريخ الانضمام
                        cursor.execute(f"""
                            UPDATE {self.subscribers_table}
                            SET join_time = %s
                            WHERE user_id = %s
                        """, (estimated_join_time, subscriber['user_id']))

                        fixed_count += 1

                        # إضافة تفاصيل المشترك المُصلح
                        join_date = datetime.fromtimestamp(estimated_join_time).strftime('%Y-%m-%d')
                        message += f"✅ {subscriber['first_name']} (@{subscriber['username'] or 'غير محدد'}) - {join_date}\n"

                    except Exception as fix_error:
                        logger.error(f"Error fixing join date for user {subscriber['user_id']}: {fix_error}")
                        message += f"❌ خطأ في إصلاح {subscriber['first_name']}\n"

                message += f"\n🎉 تم إصلاح {fixed_count} من {len(subscribers_without_join_date)} مشترك بنجاح!\n\n"
                message += "📝 ملاحظة: التواريخ المُصلحة تقديرية بناءً على تاريخ انتهاء الاشتراك"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📋 عرض قائمة المشتركين", callback_data="admin_list_subscribers"),
                types.InlineKeyboardButton("🔄 إعادة الإصلاح", callback_data="admin_fix_join_dates")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "fix_join_dates", f"Fixed {fixed_count} join dates")

        except Exception as e:
            logger.error(f"Failed to fix join dates: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في إصلاح تواريخ الانضمام: {str(e)}")

    def update_subscriber_data(self, chat_id):
        """تحديث بيانات المشتركين"""
        try:
            message = "🔄 تحديث بيانات المشتركين\n\n"

            with self.db.get_db_cursor() as cursor:
                # إحصائيات قبل التحديث
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN join_time IS NULL OR join_time = 0 THEN 1 END) as missing_join_time,
                        COUNT(CASE WHEN first_name IS NULL OR first_name = '' THEN 1 END) as missing_names,
                        COUNT(CASE WHEN is_active IS NULL THEN 1 END) as missing_status
                    FROM {self.subscribers_table}
                """)

                stats = cursor.fetchone()

                message += f"📊 إحصائيات البيانات:\n"
                message += f"• إجمالي المشتركين: {stats['total']}\n"
                message += f"• بدون تاريخ انضمام: {stats['missing_join_time']}\n"
                message += f"• بدون أسماء: {stats['missing_names']}\n"
                message += f"• بدون حالة: {stats['missing_status']}\n\n"

                updates_made = 0

                # إصلاح تواريخ الانضمام المفقودة
                if stats['missing_join_time'] > 0:
                    current_time = int(datetime.now().timestamp())
                    cursor.execute(f"""
                        UPDATE {self.subscribers_table}
                        SET join_time = %s
                        WHERE join_time IS NULL OR join_time = 0
                    """, (current_time,))
                    updates_made += cursor.rowcount
                    message += f"✅ تم إصلاح {cursor.rowcount} تاريخ انضمام\n"

                # إصلاح الأسماء المفقودة
                if stats['missing_names'] > 0:
                    cursor.execute(f"""
                        UPDATE {self.subscribers_table}
                        SET first_name = 'مستخدم'
                        WHERE first_name IS NULL OR first_name = ''
                    """)
                    updates_made += cursor.rowcount
                    message += f"✅ تم إصلاح {cursor.rowcount} اسم مفقود\n"

                # إصلاح الحالات المفقودة
                if stats['missing_status'] > 0:
                    cursor.execute(f"""
                        UPDATE {self.subscribers_table}
                        SET is_active = TRUE
                        WHERE is_active IS NULL
                    """)
                    updates_made += cursor.rowcount
                    message += f"✅ تم إصلاح {cursor.rowcount} حالة مفقودة\n"

                if updates_made == 0:
                    message += "✅ جميع البيانات سليمة ولا تحتاج إصلاح!"
                else:
                    message += f"\n🎉 تم إجراء {updates_made} تحديث بنجاح!"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📋 عرض قائمة المشتركين", callback_data="admin_list_subscribers"),
                types.InlineKeyboardButton("📊 إحصائيات النظام", callback_data="admin_stats")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, message, reply_markup=markup)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "update_subscriber_data", f"Made {updates_made} updates")

        except Exception as e:
            logger.error(f"Failed to update subscriber data: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في تحديث البيانات: {str(e)}")

    def create_backup(self, chat_id):
        """إنشاء نسخة احتياطية من بيانات المشتركين"""
        try:
            # إرسال رسالة بدء العملية
            progress_msg = self.bot.send_message(chat_id, "💾 جاري إنشاء النسخة الاحتياطية...\n⏳ يرجى الانتظار")

            # جلب جميع بيانات المشتركين
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT
                        user_id,
                        username,
                        first_name,
                        last_name,
                        is_active,
                        join_time,
                        expire_time,
                        subscription_count,
                        total_paid,
                        last_payment_date,
                        created_at,
                        updated_at
                    FROM {self.subscribers_table}
                    ORDER BY join_time DESC
                """)

                subscribers = cursor.fetchall()

            if not subscribers:
                self.bot.edit_message_text(
                    "❌ لا يوجد مشتركين لإنشاء نسخة احتياطية منهم",
                    chat_id,
                    progress_msg.message_id
                )
                return

            # إنشاء ملف CSV
            csv_content = io.StringIO()
            csv_writer = csv.writer(csv_content)

            # كتابة العناوين
            headers = [
                'معرف المستخدم',
                'اسم المستخدم',
                'الاسم الأول',
                'الاسم الأخير',
                'نشط',
                'تاريخ الانضمام',
                'تاريخ الانتهاء',
                'عدد الاشتراكات',
                'إجمالي المدفوعات',
                'آخر دفعة',
                'تاريخ الإنشاء',
                'تاريخ التحديث'
            ]
            csv_writer.writerow(headers)

            # كتابة البيانات
            for subscriber in subscribers:
                # تحويل التواريخ إلى تنسيق قابل للقراءة
                join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d %H:%M:%S') if subscriber.get('join_time') else 'غير محدد'
                expire_date = datetime.fromtimestamp(subscriber['expire_time']).strftime('%Y-%m-%d %H:%M:%S') if subscriber.get('expire_time') else 'غير محدد'
                last_payment = datetime.fromtimestamp(subscriber['last_payment_date']).strftime('%Y-%m-%d %H:%M:%S') if subscriber.get('last_payment_date') else 'غير محدد'
                created_at = subscriber['created_at'].strftime('%Y-%m-%d %H:%M:%S') if subscriber.get('created_at') else 'غير محدد'
                updated_at = subscriber['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if subscriber.get('updated_at') else 'غير محدد'

                row = [
                    subscriber['user_id'],
                    subscriber['username'] or 'غير محدد',
                    subscriber['first_name'] or 'غير محدد',
                    subscriber['last_name'] or 'غير محدد',
                    'نعم' if subscriber['is_active'] else 'لا',
                    join_date,
                    expire_date,
                    subscriber['subscription_count'] or 0,
                    f"{float(subscriber['total_paid'] or 0):.2f} جنيه",
                    last_payment,
                    created_at,
                    updated_at
                ]
                csv_writer.writerow(row)

            # إنشاء ملف JSON أيضاً للاستيراد المستقبلي
            json_data = []
            for subscriber in subscribers:
                json_data.append({
                    'user_id': subscriber['user_id'],
                    'username': subscriber['username'],
                    'first_name': subscriber['first_name'],
                    'last_name': subscriber['last_name'],
                    'is_active': subscriber['is_active'],
                    'join_time': subscriber['join_time'],
                    'expire_time': subscriber['expire_time'],
                    'subscription_count': subscriber['subscription_count'],
                    'total_paid': float(subscriber['total_paid'] or 0),
                    'last_payment_date': subscriber['last_payment_date'],
                    'created_at': subscriber['created_at'].isoformat() if subscriber.get('created_at') else None,
                    'updated_at': subscriber['updated_at'].isoformat() if subscriber.get('updated_at') else None
                })

            # إنشاء اسم الملف مع التاريخ والوقت
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename_csv = f"subscribers_backup_{timestamp}.csv"
            filename_json = f"subscribers_backup_{timestamp}.json"

            # تحويل المحتوى إلى bytes
            csv_bytes = csv_content.getvalue().encode('utf-8-sig')  # UTF-8 with BOM للدعم العربي
            json_bytes = json.dumps(json_data, ensure_ascii=False, indent=2).encode('utf-8')

            # إرسال ملف CSV
            self.bot.send_document(
                chat_id,
                document=csv_bytes,
                visible_file_name=filename_csv,
                caption=f"📊 نسخة احتياطية CSV\n\n"
                       f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                       f"👥 عدد المشتركين: {len(subscribers)}\n"
                       f"📝 يمكن فتح هذا الملف في Excel أو Google Sheets"
            )

            # إرسال ملف JSON
            self.bot.send_document(
                chat_id,
                document=json_bytes,
                visible_file_name=filename_json,
                caption=f"🔧 نسخة احتياطية JSON\n\n"
                       f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                       f"👥 عدد المشتركين: {len(subscribers)}\n"
                       f"⚙️ يمكن استخدام هذا الملف لاستعادة البيانات"
            )

            # حذف رسالة التقدم وإرسال رسالة النجاح
            try:
                self.bot.delete_message(chat_id, progress_msg.message_id)
            except:
                pass

            # إحصائيات النسخة الاحتياطية
            active_count = sum(1 for s in subscribers if s['is_active'])
            inactive_count = len(subscribers) - active_count
            total_revenue = sum(float(s['total_paid'] or 0) for s in subscribers)

            success_message = f"✅ تم إنشاء النسخة الاحتياطية بنجاح!\n\n" \
                            f"📊 إحصائيات النسخة الاحتياطية:\n" \
                            f"👥 إجمالي المشتركين: {len(subscribers)}\n" \
                            f"✅ المشتركين النشطين: {active_count}\n" \
                            f"❌ المشتركين غير النشطين: {inactive_count}\n" \
                            f"💰 إجمالي الإيرادات: {total_revenue:.2f} جنيه\n\n" \
                            f"📁 تم إنشاء ملفين:\n" \
                            f"• ملف CSV للعرض والتحليل\n" \
                            f"• ملف JSON لاستعادة البيانات\n\n" \
                            f"💡 احفظ هذه الملفات في مكان آمن!"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📊 إحصائيات النظام", callback_data="admin_stats"),
                types.InlineKeyboardButton("👥 قائمة المشتركين", callback_data="admin_list_subscribers")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="admin_tools"))

            self.bot.send_message(chat_id, success_message, reply_markup=markup)

            # تسجيل النشاط
            self.db.log_activity(chat_id, "backup_created", f"Created backup with {len(subscribers)} subscribers")

        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            try:
                self.bot.edit_message_text(
                    f"❌ حدث خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}",
                    chat_id,
                    progress_msg.message_id
                )
            except:
                self.bot.send_message(chat_id, f"❌ حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def show_security_menu(self, chat_id):
        """عرض قائمة الأمان"""
        try:
            # الحصول على إحصائيات الأمان
            blacklist_stats = self.db.get_blacklist_stats()

            message = "🛡️ **نظام الأمان المتقدم**\n\n"

            if blacklist_stats:
                message += f"📊 **إحصائيات قائمة الحظر:**\n"
                message += f"🚫 إجمالي المحظورين: {blacklist_stats['total_banned']}\n"
                message += f"🔒 حظر دائم: {blacklist_stats['permanent_bans']}\n"
                message += f"⏰ حظر مؤقت: {blacklist_stats['temporary_bans']}\n"
                message += f"📅 محظورين اليوم: {blacklist_stats['banned_today']}\n\n"

            message += f"🔧 **أدوات الأمان المتاحة:**\n"
            message += f"• إدارة قائمة الحظر\n"
            message += f"• حظر/إلغاء حظر المستخدمين\n"
            message += f"• مراقبة النشاط المشبوه\n"
            message += f"• تقارير الأمان\n\n"
            message += f"⚡ **الحماية النشطة:**\n"
            message += f"✅ فلترة تلقائية للمحظورين\n"
            message += f"✅ منع الوصول للبوت والجروب\n"
            message += f"✅ تسجيل محاولات الوصول غير المصرح بها"

            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("👥 قائمة المستخدمين", callback_data="security_users_list"),
                types.InlineKeyboardButton("🚫 قائمة الحظر", callback_data="admin_blacklist")
            )
            markup.add(
                types.InlineKeyboardButton("➕ حظر مستخدم", callback_data="admin_ban_user"),
                types.InlineKeyboardButton("🔍 بحث بالمعرف", callback_data="security_search_user")
            )
            markup.add(
                types.InlineKeyboardButton("📊 تقرير الأمان", callback_data="admin_security_report"),
                types.InlineKeyboardButton("🔴 عالي الخطر", callback_data="security_filter_high_risk")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show security menu: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض قائمة الأمان")

    def show_blacklist_menu(self, chat_id, page=1):
        """عرض قائمة المستخدمين المحظورين المحسنة"""
        try:
            page_size = 8  # تقليل العدد لإفساح مجال للأزرار
            offset = (page - 1) * page_size

            blacklisted_users = self.db.get_blacklisted_users(limit=page_size, offset=offset)
            total_banned = self.db.get_blacklisted_users_count()
            total_pages = (total_banned + page_size - 1) // page_size

            if not blacklisted_users:
                message = "✅ **قائمة الحظر فارغة**\n\n"
                message += "🎉 لا يوجد مستخدمين محظورين حالياً\n"
                message += "🛡️ النظام آمن ونظيف!"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("➕ حظر مستخدم", callback_data="admin_ban_user"),
                    types.InlineKeyboardButton("🔍 بحث", callback_data="security_search_user")
                )
                markup.add(types.InlineKeyboardButton("🔙 قائمة الأمان", callback_data="admin_security"))

                self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')
                return

            message = f"🚫 **قائمة المستخدمين المحظورين**\n\n"
            message += f"📊 **الإحصائيات:**\n"
            message += f"🚫 إجمالي المحظورين: {total_banned}\n"
            message += f"📄 الصفحة: {page}/{total_pages}\n"
            message += f"📋 عرض: {len(blacklisted_users)} مستخدم\n\n"

            # عرض المستخدمين المحظورين
            for i, user in enumerate(blacklisted_users, 1):
                ban_date = user['banned_at'].strftime('%Y-%m-%d') if user.get('banned_at') else 'غير محدد'
                ban_type_emoji = "🔒" if user.get('ban_type') == 'permanent' else "⏰"

                message += f"{i}. 🚫 **{user['first_name'] or 'غير محدد'}**\n"
                message += f"   🆔 `{user['user_id']}`\n"

                if user['username']:
                    message += f"   📝 @{user['username']}\n"

                message += f"   📝 السبب: {user['reason'][:40]}{'...' if len(user['reason']) > 40 else ''}\n"
                message += f"   📅 تاريخ الحظر: {ban_date}\n"
                message += f"   {ban_type_emoji} النوع: {user['ban_type'] or 'دائم'}\n\n"

            # أزرار رفع الحظر لكل مستخدم
            markup = types.InlineKeyboardMarkup(row_width=2)

            # إضافة أزرار رفع الحظر
            for user in blacklisted_users:
                user_name = user['first_name'] or f"ID:{user['user_id']}"
                if len(user_name) > 15:
                    user_name = user_name[:12] + "..."

                markup.add(
                    types.InlineKeyboardButton(f"👤 {user_name}", callback_data=f"view_banned_user_{user['user_id']}"),
                    types.InlineKeyboardButton(f"✅ رفع الحظر", callback_data=f"unban_user_{user['user_id']}")
                )

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق", callback_data=f"blacklist_page_{page-1}"))

            nav_buttons.append(types.InlineKeyboardButton(f"📄 {page}/{total_pages}", callback_data="blacklist_info"))

            if page < total_pages:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي", callback_data=f"blacklist_page_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            # أزرار الإجراءات
            markup.add(
                types.InlineKeyboardButton("➕ حظر مستخدم", callback_data="admin_ban_user"),
                types.InlineKeyboardButton("🔍 بحث محظور", callback_data="admin_search_banned")
            )
            markup.add(
                types.InlineKeyboardButton("🗑️ مسح الكل", callback_data="clear_all_bans"),
                types.InlineKeyboardButton("📊 إحصائيات", callback_data="ban_statistics")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة الأمان", callback_data="admin_security"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show blacklist menu: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض قائمة الحظر")

    def show_banned_user_details(self, chat_id, user_id):
        """عرض تفاصيل مستخدم محظور"""
        try:
            ban_info = self.db.is_user_blacklisted(user_id)

            if not ban_info:
                self.bot.send_message(chat_id, f"❌ المستخدم {user_id} غير محظور")
                return

            # الحصول على معلومات إضافية
            user_details = self.db.get_user_detailed_info(user_id)

            message = f"🚫 **تفاصيل المستخدم المحظور**\n\n"

            # معلومات الحظر
            message += f"👤 **معلومات الحظر:**\n"
            message += f"🆔 المعرف: `{user_id}`\n"
            message += f"📝 الاسم: {ban_info['first_name'] or 'غير محدد'}\n"
            message += f"📱 اسم المستخدم: @{ban_info['username'] or 'غير محدد'}\n"
            message += f"📝 سبب الحظر: {ban_info['reason']}\n"
            message += f"📅 تاريخ الحظر: {ban_info['banned_at'].strftime('%Y-%m-%d %H:%M')}\n"
            message += f"👮 محظور بواسطة: {ban_info['banned_by']}\n"
            message += f"🔒 نوع الحظر: {ban_info['ban_type']}\n"

            if ban_info.get('notes'):
                message += f"📋 ملاحظات: {ban_info['notes']}\n"

            # معلومات إضافية إذا توفرت
            if user_details and user_details['user_info']:
                user_info = user_details['user_info']
                message += f"\n📊 **معلومات النشاط:**\n"
                message += f"📈 إجمالي الأنشطة: {user_info['total_activities']}\n"
                message += f"🕐 أول نشاط: {user_info['first_activity'].strftime('%Y-%m-%d %H:%M')}\n"
                message += f"⏰ آخر نشاط: {user_info['last_activity'].strftime('%Y-%m-%d %H:%M')}\n"

                # معلومات الاشتراك إذا كان مشترك
                if user_details['subscription_info']:
                    sub_info = user_details['subscription_info']
                    message += f"\n💎 **معلومات الاشتراك:**\n"
                    message += f"✅ كان مشترك: نعم\n"
                    message += f"💰 إجمالي المدفوعات: {sub_info['total_paid'] or 0}\n"
                    message += f"🔄 عدد التجديدات: {sub_info['subscription_count'] or 0}\n"

            # أزرار الإجراءات
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("✅ رفع الحظر", callback_data=f"unban_user_{user_id}"),
                types.InlineKeyboardButton("📝 تعديل السبب", callback_data=f"edit_ban_reason_{user_id}")
            )
            markup.add(
                types.InlineKeyboardButton("💬 راسل المستخدم", url=f"tg://user?id={user_id}"),
                types.InlineKeyboardButton("📊 المزيد من التفاصيل", callback_data=f"security_user_details_{user_id}")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة الحظر", callback_data="admin_blacklist"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show banned user details: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تفاصيل المستخدم المحظور")

    def show_ban_statistics(self, chat_id):
        """عرض إحصائيات الحظر"""
        try:
            stats = self.db.get_blacklist_stats()

            if not stats:
                self.bot.send_message(chat_id, "❌ فشل في الحصول على إحصائيات الحظر")
                return

            message = f"📊 **إحصائيات نظام الحظر**\n\n"

            # إحصائيات عامة
            message += f"🚫 **الإحصائيات العامة:**\n"
            message += f"• إجمالي المحظورين: {stats['total_banned']}\n"
            message += f"• حظر دائم: {stats['permanent_bans']}\n"
            message += f"• حظر مؤقت: {stats['temporary_bans']}\n"
            message += f"• محظورين اليوم: {stats['banned_today']}\n"
            message += f"• محظورين هذا الأسبوع: {stats.get('banned_this_week', 0)}\n"
            message += f"• محظورين هذا الشهر: {stats.get('banned_this_month', 0)}\n\n"

            # أسباب الحظر الأكثر شيوعاً
            if stats.get('top_ban_reasons'):
                message += f"📝 **أسباب الحظر الأكثر شيوعاً:**\n"
                for reason, count in stats['top_ban_reasons'][:5]:
                    message += f"• {reason}: {count} مرة\n"
                message += "\n"

            # معدل الحظر
            total_users = self.db.get_bot_users_count()
            if total_users > 0:
                ban_rate = (stats['total_banned'] / total_users) * 100
                message += f"📈 **معدل الحظر:** {ban_rate:.2f}% من إجمالي المستخدمين\n\n"

            message += f"🛡️ **حالة النظام:** {'🟢 آمن' if ban_rate < 5 else '🟡 متوسط' if ban_rate < 15 else '🔴 عالي'}"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🚫 قائمة الحظر", callback_data="admin_blacklist"),
                types.InlineKeyboardButton("📊 تقرير مفصل", callback_data="detailed_ban_report")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة الأمان", callback_data="admin_security"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show ban statistics: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إحصائيات الحظر")

    def confirm_clear_all_bans(self, chat_id):
        """تأكيد مسح جميع المحظورين"""
        try:
            total_banned = self.db.get_blacklisted_users_count()

            if total_banned == 0:
                self.bot.send_message(chat_id, "✅ قائمة الحظر فارغة بالفعل!")
                return

            message = f"⚠️ **تأكيد مسح جميع المحظورين**\n\n"
            message += f"🚫 سيتم رفع الحظر عن {total_banned} مستخدم\n"
            message += f"⚠️ هذا الإجراء لا يمكن التراجع عنه!\n\n"
            message += f"❓ هل أنت متأكد من المتابعة؟"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("✅ نعم، امسح الكل", callback_data="confirm_clear_all_bans"),
                types.InlineKeyboardButton("❌ إلغاء", callback_data="admin_blacklist")
            )

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show clear all bans confirmation: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تأكيد المسح")

    def clear_all_bans(self, chat_id):
        """مسح جميع المحظورين"""
        try:
            cleared_count = self.db.clear_all_blacklist()

            if cleared_count > 0:
                message = f"✅ **تم مسح قائمة الحظر بنجاح!**\n\n"
                message += f"🔓 تم رفع الحظر عن {cleared_count} مستخدم\n"
                message += f"🛡️ النظام الآن نظيف وآمن\n\n"
                message += f"📝 تم تسجيل هذا الإجراء في سجل النشاط"

                # تسجيل النشاط
                self.db.log_activity(chat_id, "clear_all_bans", f"Cleared {cleared_count} banned users")
            else:
                message = "ℹ️ لم يتم العثور على مستخدمين محظورين لمسحهم"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🚫 قائمة الحظر", callback_data="admin_blacklist"),
                types.InlineKeyboardButton("📊 إحصائيات", callback_data="ban_statistics")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة الأمان", callback_data="admin_security"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to clear all bans: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في مسح قائمة الحظر")

    def ban_user(self, chat_id, target_user_id, reason="مخالفة قوانين البوت", ban_type="permanent", notes=None):
        """حظر مستخدم"""
        try:
            # التحقق من أن المستخدم ليس محظور مسبقاً
            existing_ban = self.db.is_user_blacklisted(target_user_id)
            if existing_ban:
                self.bot.send_message(chat_id, f"⚠️ المستخدم {target_user_id} محظور مسبقاً")
                return

            # الحصول على معلومات المستخدم إذا كان مشترك
            subscriber = self.db.get_subscriber(target_user_id)
            username = subscriber.get('username') if subscriber else None
            first_name = subscriber.get('first_name') if subscriber else None

            # إضافة للقائمة السوداء
            ban_id = self.db.add_to_blacklist(
                user_id=target_user_id,
                username=username,
                first_name=first_name,
                reason=reason,
                banned_by=chat_id,
                notes=notes,
                ban_type=ban_type
            )

            if ban_id:
                # إزالة المستخدم من النظام إذا كان مشترك
                if subscriber:
                    # استخدام مدير روابط الدعوة لإزالة المستخدم بأمان
                    from invite_manager import InviteManager
                    invite_manager = InviteManager(self.bot, self.db, self.config)

                    removal_result = invite_manager.handle_user_removal(target_user_id, "banned_by_admin")

                    # حذف من قاعدة البيانات
                    with self.db.get_db_cursor() as cursor:
                        cursor.execute(f"DELETE FROM {self.subscribers_table} WHERE user_id = %s", (target_user_id,))

                # إرسال رسالة للمستخدم المحظور
                try:
                    ban_message = f"🚫 **تم حظرك من استخدام هذا البوت**\n\n" \
                                 f"📝 **السبب:** {reason}\n" \
                                 f"🔒 **نوع الحظر:** {ban_type}\n" \
                                 f"📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n"

                    if notes:
                        ban_message += f"📋 **ملاحظات:** {notes}\n\n"

                    ban_message += f"📞 **للاستفسار:** راسل الإدارة مباشرة"

                    self.bot.send_message(target_user_id, ban_message, parse_mode='Markdown')
                except:
                    pass  # المستخدم قد يكون أوقف البوت

                # رسالة تأكيد للإدارة
                success_message = f"✅ **تم حظر المستخدم بنجاح!**\n\n"
                success_message += f"🆔 **المعرف:** {target_user_id}\n"
                success_message += f"👤 **الاسم:** {first_name or 'غير محدد'} (@{username or 'غير محدد'})\n"
                success_message += f"📝 **السبب:** {reason}\n"
                success_message += f"🔒 **النوع:** {ban_type}\n"
                success_message += f"📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n"

                if subscriber:
                    success_message += f"🔒 **الإجراءات المتخذة:**\n"
                    success_message += f"• تم إلغاء {removal_result.get('links_revoked', 0)} رابط دعوة\n"
                    success_message += f"• تم إزالته من الجروب\n"
                    success_message += f"• تم حذف بيانات الاشتراك\n"
                    success_message += f"• تم إضافته لقائمة الحظر"

                self.bot.send_message(chat_id, success_message, parse_mode='Markdown')

                # تسجيل النشاط
                self.db.log_activity(chat_id, "user_banned", f"Banned user {target_user_id}: {reason}")

            else:
                self.bot.send_message(chat_id, "❌ فشل في حظر المستخدم")

        except Exception as e:
            logger.error(f"Failed to ban user {target_user_id}: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في حظر المستخدم: {str(e)}")

    def unban_user(self, chat_id, target_user_id):
        """إلغاء حظر مستخدم"""
        try:
            # التحقق من أن المستخدم محظور
            ban_info = self.db.is_user_blacklisted(target_user_id)
            if not ban_info:
                self.bot.send_message(chat_id, f"⚠️ المستخدم {target_user_id} غير محظور")
                return

            # إزالة من قائمة الحظر
            success = self.db.remove_from_blacklist(target_user_id)

            if success:
                # إرسال رسالة للمستخدم
                try:
                    unban_message = f"✅ **تم إلغاء حظرك من البوت**\n\n" \
                                   f"🎉 يمكنك الآن استخدام البوت بشكل طبيعي\n" \
                                   f"📅 **تاريخ إلغاء الحظر:** {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n" \
                                   f"💡 **تذكير:** يرجى الالتزام بقوانين البوت\n" \
                                   f"📞 **للاستفسار:** راسل الإدارة"

                    self.bot.send_message(target_user_id, unban_message, parse_mode='Markdown')
                except:
                    pass

                # رسالة تأكيد للإدارة
                success_message = f"✅ **تم إلغاء حظر المستخدم بنجاح!**\n\n"
                success_message += f"🆔 **المعرف:** {target_user_id}\n"
                success_message += f"📅 **تاريخ إلغاء الحظر:** {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
                success_message += f"📝 **السبب الأصلي:** {ban_info['reason']}\n\n"
                success_message += f"💡 **ملاحظة:** المستخدم يمكنه الآن استخدام البوت"

                self.bot.send_message(chat_id, success_message, parse_mode='Markdown')

                # تسجيل النشاط
                self.db.log_activity(chat_id, "user_unbanned", f"Unbanned user {target_user_id}")

            else:
                self.bot.send_message(chat_id, "❌ فشل في إلغاء حظر المستخدم")

        except Exception as e:
            logger.error(f"Failed to unban user {target_user_id}: {e}")
            self.bot.send_message(chat_id, f"❌ حدث خطأ في إلغاء حظر المستخدم: {str(e)}")

    def show_support_menu(self, chat_id):
        """عرض قائمة نظام الدعم"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # التحقق من أن هذا أول دخول لنظام الدعم اليوم
            is_first_access_today = self._check_first_support_access_today(chat_id)

            if is_first_access_today:
                # إرسال إشعار جماعي لجميع المشتركين
                notification_result = support_system.notify_support_system_started()

                if notification_result['success']:
                    # إشعار الإدارة بنتيجة الإشعار الجماعي
                    self.bot.send_message(
                        chat_id,
                        f"🔔 **تم إرسال إشعار بدء نظام الدعم**\n\n"
                        f"✅ تم إشعار: {notification_result['notified_count']} مشترك\n"
                        f"❌ فشل: {notification_result['failed_count']} مشترك\n"
                        f"📊 إجمالي المشتركين: {notification_result['total_subscribers']}\n\n"
                        f"🎉 **جميع المشتركين يعلمون الآن أن فريق الدعم متاح!**"
                    )

            # الحصول على إحصائيات الدعم
            stats = support_system.get_support_stats()

            message = "🎧 **نظام الدعم المتقدم**\n\n"

            if stats['success']:
                message += f"📊 **إحصائيات الدعم:**\n"
                message += f"⏳ في الانتظار: {stats['waiting_count']}\n"
                message += f"💬 جلسات نشطة: {stats['in_progress_count']}/{stats['max_concurrent']}\n"
                message += f"✅ مكتملة اليوم: {stats['completed_today']}\n"
                message += f"🔓 مقاعد متاحة: {stats['available_slots']}\n\n"

            # الحصول على حالة الإدارة
            admin_status = support_system.get_admin_status(chat_id)

            if admin_status['success']:
                status_text = {
                    'available': '🟢 متاح',
                    'busy': '🔴 مشغول',
                    'offline': '⚫ غير متصل'
                }.get(admin_status['status'], admin_status['status'])

                message += f"👤 **حالتك:** {status_text}\n"

                if admin_status['current_user']:
                    message += f"💬 **تدعم حالياً:** {admin_status['current_user']}\n"

            message += f"\n🔧 **أدوات الدعم المتاحة:**\n"
            message += f"• إدارة طابور الدعم\n"
            message += f"• بدء/إنهاء جلسات الدعم\n"
            message += f"• تغيير حالة التوفر\n"
            message += f"• مراقبة الإحصائيات"

            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue"),
                types.InlineKeyboardButton("💬 الجلسات النشطة", callback_data="admin_active_sessions")
            )

            # أزرار تغيير الحالة - دائماً متاحة مع مؤشر الحالة الحالية
            if admin_status['success']:
                current_status = admin_status['status']

                # زر المتاح
                if current_status == 'available':
                    available_text = "🟢 متصل ✅"
                else:
                    available_text = "🟢 تغيير لمتصل"

                # زر غير متصل
                if current_status == 'offline':
                    offline_text = "⚫ غير متصل ✅"
                else:
                    offline_text = "⚫ تغيير لغير متصل"

                markup.add(
                    types.InlineKeyboardButton(available_text, callback_data="admin_set_available"),
                    types.InlineKeyboardButton(offline_text, callback_data="admin_set_offline")
                )
            else:
                # في حالة عدم وجود حالة، أزرار عادية
                markup.add(
                    types.InlineKeyboardButton("🟢 تغيير لمتصل", callback_data="admin_set_available"),
                    types.InlineKeyboardButton("⚫ تغيير لغير متصل", callback_data="admin_set_offline")
                )

            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show support menu: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض قائمة الدعم")

    def show_support_queue(self, chat_id):
        """عرض طابور الدعم"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # الحصول على المستخدم التالي
            next_user_result = support_system.get_next_user_for_support()

            if next_user_result['success']:
                user_data = next_user_result['user_data']

                message = f"📞 **طابور الدعم**\n\n"
                message += f"👤 **المستخدم التالي:**\n"
                message += f"🆔 المعرف: `{user_data['user_id']}`\n"
                message += f"👤 الاسم: {user_data['first_name']}\n"
                message += f"📝 الموضوع: {user_data['subject']}\n"
                message += f"📋 الوصف: {user_data['description']}\n"
                message += f"⏰ وقت الطلب: {user_data['created_at'].strftime('%H:%M')}\n"
                message += f"📍 الموضع: {user_data['queue_position']}"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("✅ بدء الدعم", callback_data=f"start_support_{user_data['id']}"),
                    types.InlineKeyboardButton("❌ تخطي", callback_data=f"skip_support_{user_data['id']}")
                )
                markup.add(
                    types.InlineKeyboardButton("📊 إحصائيات الطابور", callback_data="admin_queue_stats"),
                    types.InlineKeyboardButton("🔄 تحديث", callback_data="admin_support_queue")
                )
                markup.add(types.InlineKeyboardButton("🔙 نظام الدعم", callback_data="admin_support"))

            else:
                message = "📭 **طابور الدعم فارغ**\n\n"
                message += "لا يوجد مستخدمين في انتظار الدعم حالياً"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("📊 إحصائيات الطابور", callback_data="admin_queue_stats"),
                    types.InlineKeyboardButton("🔄 تحديث", callback_data="admin_support_queue")
                )
                markup.add(types.InlineKeyboardButton("🔙 نظام الدعم", callback_data="admin_support"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show support queue: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض طابور الدعم")

    def start_support_session_admin(self, chat_id, queue_id):
        """بدء جلسة دعم من الإدارة"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # بدء الجلسة
            result = support_system.start_support_session(queue_id, chat_id)

            if result['success']:
                # الحصول على بيانات المستخدم من الطابور
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"""
                        SELECT user_id, first_name, subject, description
                        FROM {self.db.support_queue_table}
                        WHERE id = %s
                    """, (queue_id,))
                    user_data = cursor.fetchone()

                if user_data:
                    user_id = user_data['user_id']

                    # إشعار المستخدم
                    support_system.notify_user_turn(user_id)

                    # رسالة للإدارة
                    admin_message = f"✅ **تم بدء جلسة الدعم!**\n\n"
                    admin_message += f"👤 المستخدم: {user_data['first_name']}\n"
                    admin_message += f"🆔 المعرف: `{user_id}`\n"
                    admin_message += f"📝 الموضوع: {user_data['subject']}\n"
                    admin_message += f"📋 الوصف: {user_data['description']}\n\n"
                    admin_message += f"💬 **الجلسة نشطة الآن!**\n"
                    admin_message += f"📨 ستصلك رسائل المستخدم هنا مباشرة"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(types.InlineKeyboardButton("🔚 إنهاء الجلسة", callback_data=f"end_support_{result['session_id']}"))
                    markup.add(types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue"))

                    self.bot.send_message(chat_id, admin_message, reply_markup=markup, parse_mode='Markdown')

                    # تسجيل النشاط
                    self.db.log_activity(chat_id, "support_session_started", f"Started session with user {user_id}")
                else:
                    self.bot.send_message(chat_id, "❌ لم يتم العثور على بيانات المستخدم")
            else:
                self.bot.send_message(chat_id, f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Failed to start support session: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في بدء جلسة الدعم")

    def end_support_session_admin(self, chat_id, session_id):
        """إنهاء جلسة دعم من الإدارة"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # إنهاء الجلسة
            result = support_system.end_support_session(session_id, chat_id)

            if result['success']:
                message = "✅ **تم إنهاء جلسة الدعم بنجاح!**\n\n"
                message += "🔄 يمكنك الآن بدء جلسة دعم جديدة\n"
                message += "📞 تحقق من طابور الدعم للمستخدم التالي"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue"),
                    types.InlineKeyboardButton("🎧 نظام الدعم", callback_data="admin_support")
                )

                self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

                # تسجيل النشاط
                self.db.log_activity(chat_id, "support_session_ended", f"Ended session {session_id}")
            else:
                self.bot.send_message(chat_id, f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Failed to end support session: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إنهاء جلسة الدعم")

    def skip_support_user(self, chat_id, queue_id):
        """تخطي مستخدم في طابور الدعم"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # الحصول على بيانات المستخدم قبل التخطي
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, first_name, subject, description, queue_position
                    FROM {self.db.support_queue_table}
                    WHERE id = %s AND status = 'waiting'
                """, (queue_id,))
                user_data = cursor.fetchone()

            if not user_data:
                self.bot.send_message(chat_id, "❌ لم يتم العثور على المستخدم في الطابور")
                return

            # تخطي المستخدم
            result = support_system.skip_user_in_queue(queue_id)

            if result['success']:
                # إشعار المستخدم بالتخطي
                user_id = user_data['user_id']
                try:
                    skip_message = f"⏭️ **تم تأجيل طلب الدعم الخاص بك**\n\n" \
                                 f"📋 **السبب:** الإدارة مشغولة حالياً\n" \
                                 f"📍 **موضعك الجديد:** آخر الطابور\n" \
                                 f"⏰ **سيتم التعامل معك قريباً**\n\n" \
                                 f"🙏 **نعتذر عن التأخير ونشكرك على صبرك**"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(
                        types.InlineKeyboardButton("📊 حالة الطابور", callback_data="check_support_status"),
                        types.InlineKeyboardButton("❌ إلغاء الطلب", callback_data="cancel_support_request")
                    )
                    markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

                    self.bot.send_message(user_id, skip_message, reply_markup=markup, parse_mode='Markdown')
                except Exception as e:
                    logger.warning(f"Failed to notify skipped user {user_id}: {e}")

                # رسالة للإدارة
                admin_message = f"⏭️ **تم تخطي المستخدم بنجاح!**\n\n"
                admin_message += f"👤 المستخدم: {user_data['first_name']}\n"
                admin_message += f"🆔 المعرف: `{user_id}`\n"
                admin_message += f"📝 الموضوع: {user_data['subject']}\n"
                admin_message += f"📍 الموضع السابق: {user_data['queue_position']}\n\n"
                admin_message += f"🔄 **تم نقله لآخر الطابور**\n"
                admin_message += f"📞 **يمكنك الآن التعامل مع المستخدم التالي**"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue"),
                    types.InlineKeyboardButton("🎧 نظام الدعم", callback_data="admin_support")
                )

                self.bot.send_message(chat_id, admin_message, reply_markup=markup, parse_mode='Markdown')

                # تسجيل النشاط
                self.db.log_activity(chat_id, "support_user_skipped", f"Skipped user {user_id} from queue")
            else:
                self.bot.send_message(chat_id, f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Failed to skip support user: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تخطي المستخدم")

    def show_support_statistics(self, chat_id):
        """عرض إحصائيات نظام الدعم المفصلة"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # الحصول على إحصائيات الدعم
            stats = support_system.get_support_stats()

            if not stats['success']:
                self.bot.send_message(chat_id, "❌ فشل في الحصول على إحصائيات الدعم")
                return

            # الحصول على إحصائيات إضافية من قاعدة البيانات
            with self.db.get_db_cursor() as cursor:
                # إحصائيات اليوم
                cursor.execute(f"""
                    SELECT
                        COUNT(CASE WHEN status = 'waiting' THEN 1 END) as waiting_today,
                        COUNT(CASE WHEN status = 'completed' AND DATE(completed_at) = CURRENT_DATE THEN 1 END) as completed_today,
                        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_sessions,
                        AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL
                            THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_wait_time
                    FROM {self.db.support_queue_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)

                today_stats = cursor.fetchone()

                # إحصائيات الأسبوع
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
                        AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL
                            THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_resolution_time
                    FROM {self.db.support_queue_table}
                    WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
                """)

                week_stats = cursor.fetchone()

                # أكثر المستخدمين طلباً للدعم
                cursor.execute(f"""
                    SELECT
                        first_name,
                        username,
                        user_id,
                        COUNT(*) as request_count
                    FROM {self.db.support_queue_table}
                    WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
                    GROUP BY user_id, first_name, username
                    ORDER BY request_count DESC
                    LIMIT 5
                """)

                top_users = cursor.fetchall()

                # إحصائيات الحالة
                cursor.execute(f"""
                    SELECT
                        status,
                        COUNT(*) as count
                    FROM {self.db.support_queue_table}
                    WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
                    GROUP BY status
                """)

                status_stats = cursor.fetchall()

            # بناء رسالة الإحصائيات
            message = f"📊 **إحصائيات نظام الدعم المفصلة**\n\n"

            # الحالة الحالية
            message += f"🔄 **الحالة الحالية:**\n"
            message += f"⏳ في الانتظار: {stats['waiting_count']}\n"
            message += f"💬 جلسات نشطة: {stats['in_progress_count']}/{stats['max_concurrent']}\n"
            message += f"🔓 مقاعد متاحة: {stats['available_slots']}\n\n"

            # إحصائيات اليوم
            message += f"📅 **إحصائيات اليوم:**\n"
            message += f"📝 طلبات جديدة: {today_stats['waiting_today'] + today_stats['completed_today'] + today_stats['active_sessions']}\n"
            message += f"✅ طلبات مكتملة: {today_stats['completed_today']}\n"
            message += f"⏱️ متوسط وقت الانتظار: {today_stats['avg_wait_time']:.1f if today_stats['avg_wait_time'] else 0} دقيقة\n\n"

            # إحصائيات الأسبوع
            message += f"📈 **إحصائيات الأسبوع:**\n"
            message += f"📊 إجمالي الطلبات: {week_stats['total_requests']}\n"
            message += f"✅ طلبات مكتملة: {week_stats['completed_requests']}\n"
            completion_rate = (week_stats['completed_requests'] / max(week_stats['total_requests'], 1)) * 100
            message += f"📈 معدل الإنجاز: {completion_rate:.1f}%\n"
            message += f"⏱️ متوسط وقت الحل: {week_stats['avg_resolution_time']:.1f if week_stats['avg_resolution_time'] else 0} دقيقة\n\n"

            # توزيع الحالات
            if status_stats:
                message += f"📋 **توزيع الحالات (آخر 7 أيام):**\n"
                status_names = {
                    'waiting': '⏳ انتظار',
                    'in_progress': '💬 قيد المعالجة',
                    'completed': '✅ مكتمل',
                    'cancelled': '❌ ملغي'
                }
                for status_info in status_stats:
                    status_name = status_names.get(status_info['status'], status_info['status'])
                    message += f"• {status_name}: {status_info['count']}\n"
                message += "\n"

            # أكثر المستخدمين طلباً
            if top_users:
                message += f"👥 **أكثر المستخدمين طلباً (آخر 30 يوم):**\n"
                for i, user_info in enumerate(top_users, 1):
                    name = user_info['first_name'] or 'غير محدد'
                    username = user_info['username'] or 'غير محدد'
                    message += f"{i}. {name} (@{username}) - {user_info['request_count']} طلب\n"

            # أزرار التحكم
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue"),
                types.InlineKeyboardButton("💬 الجلسات النشطة", callback_data="admin_active_sessions")
            )
            markup.add(
                types.InlineKeyboardButton("🔄 تحديث الإحصائيات", callback_data="admin_support_stats"),
                types.InlineKeyboardButton("🎧 نظام الدعم", callback_data="admin_support")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="admin_menu"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show support statistics: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض إحصائيات الدعم")

    def set_admin_support_status(self, chat_id, status):
        """تحديث حالة الإدارة في نظام الدعم"""
        try:
            from support_system import SupportSystem
            support_system = SupportSystem(self.bot)

            # الحصول على الحالة السابقة
            old_status_result = support_system.get_admin_status(chat_id)
            old_status = old_status_result.get('status') if old_status_result.get('success') else None

            result = support_system.set_admin_status(chat_id, status)

            if result['success']:
                status_text = {
                    'available': '🟢 متصل ومتاح للدعم',
                    'busy': '🔴 مشغول',
                    'offline': '⚫ غير متصل'
                }.get(status, status)

                # رسالة مختلفة حسب التغيير
                if old_status == status:
                    message = f"ℹ️ **حالتك بالفعل:** {status_text}\n\n"
                    message += "لا يوجد تغيير في الحالة"
                else:
                    message = f"✅ **تم تحديث حالتك إلى:** {status_text}\n\n"

                    if status == 'available':
                        message += "🔔 **ما يحدث الآن:**\n"
                        message += "• ستتلقى إشعارات طلبات الدعم الجديدة\n"
                        message += "• يمكنك بدء جلسات دعم جديدة\n"
                        message += "• المشتركون سيعلمون أنك متاح\n\n"
                        message += "💪 **أنت الآن جاهز لاستقبال طلبات الدعم!**"
                    elif status == 'offline':
                        message += "⏸️ **ما يحدث الآن:**\n"
                        message += "• لن تتلقى إشعارات طلبات الدعم\n"
                        message += "• الجلسات النشطة ستتوقف مؤقتاً\n"
                        message += "• المشتركون سيعلمون أنك غير متاح\n\n"
                        message += "😴 **يمكنك أخذ استراحة الآن**"

                markup = types.InlineKeyboardMarkup(row_width=2)

                # أزرار سريعة للتبديل
                if status == 'available':
                    markup.add(
                        types.InlineKeyboardButton("📞 طابور الدعم", callback_data="admin_support_queue"),
                        types.InlineKeyboardButton("⚫ تغيير لغير متصل", callback_data="admin_set_offline")
                    )
                else:
                    markup.add(
                        types.InlineKeyboardButton("🟢 تغيير لمتصل", callback_data="admin_set_available"),
                        types.InlineKeyboardButton("📊 إحصائيات الدعم", callback_data="admin_support_stats")
                    )

                markup.add(types.InlineKeyboardButton("🔙 نظام الدعم", callback_data="admin_support"))

                self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')
            else:
                self.bot.send_message(chat_id, f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Failed to set admin support status: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في تحديث الحالة")

    def _check_first_support_access_today(self, admin_id):
        """التحقق من أن هذا أول دخول لنظام الدعم اليوم"""
        try:
            # التحقق من آخر دخول لنظام الدعم
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT created_at FROM {self.db.activity_logs_table}
                    WHERE user_id = %s AND action = 'support_system_accessed'
                    AND DATE(created_at) = CURRENT_DATE
                    ORDER BY created_at DESC LIMIT 1
                """, (admin_id,))

                last_access = cursor.fetchone()

                if not last_access:
                    # لم يدخل اليوم، سجل الدخول
                    self.db.log_activity(admin_id, "support_system_accessed", "First access today")
                    return True
                else:
                    # دخل من قبل اليوم
                    return False

        except Exception as e:
            logger.error(f"Failed to check first support access: {e}")
            # في حالة الخطأ، لا نرسل إشعار لتجنب الإزعاج
            return False

    def show_security_users_list(self, chat_id, page=1):
        """عرض قائمة المستخدمين للمراجعة الأمنية"""
        try:
            page_size = 10
            offset = (page - 1) * page_size

            # الحصول على المستخدمين
            users = self.db.get_all_bot_users(limit=page_size, offset=offset)
            total_users = self.db.get_bot_users_count()
            total_pages = (total_users + page_size - 1) // page_size

            if not users:
                self.bot.send_message(chat_id, "📭 لا يوجد مستخدمين للعرض")
                return

            message = f"🛡️ **قائمة الأمان - مراجعة المستخدمين**\n\n"
            message += f"📊 **الإحصائيات:**\n"
            message += f"👥 إجمالي المستخدمين: {total_users}\n"
            message += f"📄 الصفحة: {page}/{total_pages}\n"
            message += f"📋 عرض: {len(users)} مستخدم\n\n"

            for i, user in enumerate(users, 1):
                status_emoji = self._get_status_emoji(user['status'])
                risk_emoji = self._get_risk_emoji(user['risk_level'])

                message += f"{i}. {status_emoji} **{user['first_name'] or 'غير محدد'}**\n"
                message += f"   🆔 `{user['user_id']}`\n"

                if user['username']:
                    message += f"   📝 @{user['username']}\n"

                message += f"   📊 النشاط: {user['activity_count']} | {risk_emoji} {user['risk_level']}\n"

                if user['blocked_reason']:
                    message += f"   🚫 محظور: {user['blocked_reason'][:30]}...\n"

                message += f"   ⏰ آخر نشاط: {user['last_activity'].strftime('%Y-%m-%d %H:%M')}\n\n"

            # أزرار التنقل والإجراءات
            markup = types.InlineKeyboardMarkup(row_width=3)

            # أزرار التنقل
            nav_buttons = []
            if page > 1:
                nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق", callback_data=f"security_users_page_{page-1}"))

            nav_buttons.append(types.InlineKeyboardButton(f"📄 {page}/{total_pages}", callback_data="security_users_info"))

            if page < total_pages:
                nav_buttons.append(types.InlineKeyboardButton("➡️ التالي", callback_data=f"security_users_page_{page+1}"))

            if nav_buttons:
                markup.row(*nav_buttons)

            # أزرار الفلترة
            markup.add(
                types.InlineKeyboardButton("🔴 عالي الخطر", callback_data="security_filter_high_risk"),
                types.InlineKeyboardButton("🚫 المحظورين", callback_data="security_filter_blocked")
            )
            markup.add(
                types.InlineKeyboardButton("👥 المشتركين", callback_data="security_filter_subscribers"),
                types.InlineKeyboardButton("👻 الزوار", callback_data="security_filter_visitors")
            )

            # أزرار الإجراءات
            markup.add(
                types.InlineKeyboardButton("🔍 بحث بالمعرف", callback_data="security_search_user"),
                types.InlineKeyboardButton("🚫 حظر سريع", callback_data="security_quick_ban")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة الأمان", callback_data="admin_security"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show security users list: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض قائمة المستخدمين")

    def show_user_security_details(self, chat_id, user_id):
        """عرض تفاصيل أمنية مفصلة عن مستخدم"""
        try:
            user_details = self.db.get_user_detailed_info(user_id)

            if not user_details:
                self.bot.send_message(chat_id, f"❌ لم يتم العثور على المستخدم {user_id}")
                return

            user_info = user_details['user_info']
            subscription_info = user_details['subscription_info']
            block_info = user_details['block_info']
            recent_activities = user_details['recent_activities']

            message = f"🔍 **تفاصيل أمنية مفصلة**\n\n"

            # معلومات أساسية
            message += f"👤 **المعلومات الأساسية:**\n"
            message += f"🆔 المعرف: `{user_info['user_id']}`\n"
            message += f"📝 الاسم: {user_info['first_name'] or 'غير محدد'}\n"
            message += f"📱 اسم المستخدم: @{user_info['username'] or 'غير محدد'}\n"
            message += f"📊 إجمالي الأنشطة: {user_info['total_activities']}\n"
            message += f"🕐 أول نشاط: {user_info['first_activity'].strftime('%Y-%m-%d %H:%M')}\n"
            message += f"⏰ آخر نشاط: {user_info['last_activity'].strftime('%Y-%m-%d %H:%M')}\n\n"

            # معلومات الاشتراك
            if subscription_info:
                message += f"💎 **معلومات الاشتراك:**\n"
                message += f"✅ نشط: {'نعم' if subscription_info['is_active'] else 'لا'}\n"

                if subscription_info['expire_time']:
                    expire_date = datetime.fromtimestamp(subscription_info['expire_time'])
                    message += f"⏰ انتهاء الاشتراك: {expire_date.strftime('%Y-%m-%d %H:%M')}\n"

                message += f"💰 إجمالي المدفوعات: {subscription_info['total_paid'] or 0}\n"
                message += f"🔄 عدد التجديدات: {subscription_info['subscription_count'] or 0}\n\n"
            else:
                message += f"👻 **غير مشترك**\n\n"

            # معلومات الحظر
            if block_info:
                message += f"🚫 **محظور:**\n"
                message += f"📝 السبب: {block_info['reason']}\n"
                message += f"⏰ تاريخ الحظر: {block_info['blocked_at'].strftime('%Y-%m-%d %H:%M')}\n\n"

            # آخر الأنشطة
            if recent_activities:
                message += f"📋 **آخر الأنشطة:**\n"
                for activity in recent_activities[:5]:
                    message += f"• {activity['action']}: {activity['details'] or 'لا توجد تفاصيل'}\n"
                    message += f"  ⏰ {activity['created_at'].strftime('%m-%d %H:%M')}\n"

            # أزرار الإجراءات
            markup = types.InlineKeyboardMarkup(row_width=2)

            if not block_info:
                markup.add(
                    types.InlineKeyboardButton("🚫 حظر المستخدم", callback_data=f"security_ban_user_{user_id}"),
                    types.InlineKeyboardButton("⚠️ حظر مؤقت", callback_data=f"security_temp_ban_{user_id}")
                )
            else:
                markup.add(types.InlineKeyboardButton("✅ إلغاء الحظر", callback_data=f"security_unban_user_{user_id}"))

            markup.add(
                types.InlineKeyboardButton("📊 المزيد من الأنشطة", callback_data=f"security_user_activities_{user_id}"),
                types.InlineKeyboardButton("💬 راسل المستخدم", url=f"tg://user?id={user_id}")
            )
            markup.add(types.InlineKeyboardButton("🔙 قائمة المستخدمين", callback_data="security_users_list"))

            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Failed to show user security details: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض تفاصيل المستخدم")

    def _get_status_emoji(self, status):
        """الحصول على رمز الحالة"""
        status_emojis = {
            'active_subscriber': '💎',
            'expired_subscriber': '⏰',
            'visitor': '👻',
            'blocked': '🚫'
        }
        return status_emojis.get(status, '❓')

    def _get_risk_emoji(self, risk_level):
        """الحصول على رمز مستوى الخطر"""
        risk_emojis = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🔴'
        }
        return risk_emojis.get(risk_level, '⚪')