"""
إعدادات النظام المتقدم لبوت الاشتراكات
Advanced Configuration for Subscription Bot
"""
import os
from dotenv import load_dotenv  # type: ignore

load_dotenv()

class Config:
    # إعدادات البوت الأساسية
    API_TOKEN = os.environ.get("API_TOKEN")
    ADMIN_ID = int(os.environ.get("ADMIN_ID", "1449694969"))
    GROUP_ID = int(os.environ.get("GROUP_ID", "-1001234567890"))
    WEBHOOK_URL = os.environ.get("WEBHOOK_URL")

    # إعدادات قاعدة البيانات
    DB_HOST = os.environ.get("DB_HOST")
    DB_NAME = os.environ.get("DB_NAME")
    DB_USER = os.environ.get("DB_USER")
    DB_PASS = os.environ.get("DB_PASS")
    DB_PORT = int(os.environ.get("DB_PORT", "5432"))

    # بادئة الجداول لعزل النظام الجديد عن القديم
    TABLE_PREFIX = os.environ.get("TABLE_PREFIX", "advanced_")

    # إعدادات الاشتراك
    SUBSCRIPTION_PRICE = int(os.environ.get("SUBSCRIPTION_PRICE", "100"))
    SUBSCRIPTION_DURATION_DAYS = int(os.environ.get("SUBSCRIPTION_DURATION_DAYS", "30"))
    WARNING_DAYS_BEFORE_EXPIRY = int(os.environ.get("WARNING_DAYS_BEFORE_EXPIRY", "3"))

    # إعدادات الدفع
    VODAFONE_CASH_NUMBER = os.environ.get("VODAFONE_CASH_NUMBER", "01009046911")
    INSTAPAY_NUMBER = os.environ.get("INSTAPAY_NUMBER", "01009046911")

    # إعدادات التنبيهات
    CHECK_INTERVAL_MINUTES = int(os.environ.get("CHECK_INTERVAL_MINUTES", "60"))
    NOTIFICATION_ENABLED = os.environ.get("NOTIFICATION_ENABLED", "true").lower() == "true"

    # إعدادات الأمان
    MAX_PAYMENT_ATTEMPTS = int(os.environ.get("MAX_PAYMENT_ATTEMPTS", "3"))
    RATE_LIMIT_MESSAGES = int(os.environ.get("RATE_LIMIT_MESSAGES", "10"))

    # إعدادات السيرفر
    PORT = int(os.environ.get('PORT', 5000))
    DEBUG = os.environ.get("DEBUG", "false").lower() == "true"

    # رسائل النظام
    MESSAGES = {
        'welcome': "🌟 مرحبًا بك في نظام الاشتراكات المتقدم!\n\n"
                  "💎 احصل على اشتراك شهري للوصول إلى المحتوى الحصري\n"
                  "⚡ نظام دفع سريع وآمن\n"
                  "🔔 تنبيهات تلقائية قبل انتهاء الاشتراك\n\n"
                  "اختر ما تريد من القائمة أدناه:",

        'subscription_info': f"💰 سعر الاشتراك الشهري: {SUBSCRIPTION_PRICE} جنيه\n"
                           f"⏰ مدة الاشتراك: {SUBSCRIPTION_DURATION_DAYS} يوم\n"
                           f"🔔 تنبيه قبل انتهاء الاشتراك: {WARNING_DAYS_BEFORE_EXPIRY} أيام\n\n"
                           "💳 طرق الدفع المتاحة:\n"
                           f"📱 فودافون كاش: {VODAFONE_CASH_NUMBER}\n"
                           f"💸 انستاباي: {INSTAPAY_NUMBER}",

        'payment_instructions': "📋 تعليمات الدفع:\n\n"
                              "1️⃣ اختر طريقة الدفع المناسبة\n"
                              "2️⃣ قم بتحويل المبلغ\n"
                              "3️⃣ أرسل صورة إثبات الدفع أو رقم العملية\n"
                              "4️⃣ انتظر موافقة الإدارة (عادة خلال دقائق)\n\n"
                              "⚠️ تأكد من إرسال إثبات دفع واضح وصحيح",

        'help_text': "❓ مساعدة وأسئلة شائعة:\n\n"
                    "🔸 كيف أدفع الاشتراك؟\n"
                    "استخدم زر 'دفع الاشتراك' واتبع التعليمات\n\n"
                    "🔸 متى يتم تفعيل اشتراكي؟\n"
                    "فور موافقة الإدارة على إثبات الدفع\n\n"
                    "🔸 كيف أعرف موعد انتهاء اشتراكي؟\n"
                    "استخدم زر 'حالة اشتراكي' أو ستصلك تنبيهات تلقائية\n\n"
                    "🔸 هل يمكنني تجديد الاشتراك مبكرًا؟\n"
                    "نعم، سيتم إضافة المدة الجديدة للمدة المتبقية\n\n"
                    "📞 للدعم الفني: راسل الإدارة مباشرة"
    }

    # إعدادات اللوجز
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
    LOG_FILE = os.environ.get("LOG_FILE", "bot.log")

    @classmethod
    def validate_config(cls):
        """التحقق من صحة الإعدادات"""
        required_vars = ['API_TOKEN', 'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS']
        missing_vars = []

        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        return True
