"""
🤖 نظام البوت المتقدم للاشتراكات الشهرية
Advanced Monthly Subscription Bot System

المميزات:
- نظام دفع متعدد الطرق
- تنبيهات تلقائية قبل انتهاء الاشتراك
- لوحة تحكم إدارية متقدمة
- نظام أمان وحماية
- إحصائيات وتقارير شاملة
- معالجة أخطاء محسنة
"""

import logging
import signal
import sys
from flask import Flask, request, jsonify  # type: ignore
import telebot  # type: ignore
from telebot import types  # type: ignore
from datetime import datetime

# استيراد الوحدات المخصصة
from config import Config
from database import DatabaseManager
from notifications import NotificationManager
from admin_panel import AdminPanel
from payment_handler import PaymentHandler
from invite_manager import InviteManager
from security_filter import SecurityFilter
from utils import (
    LogManager, RateLimiter, SecurityManager, MessageFormatter,
    log_user_activity, <PERSON><PERSON><PERSON><PERSON>, DateTimeHelper
)
from download_system import SecureDownloadSystem

# إعداد النظام
config = Config()
config.validate_config()

# إعداد السجلات
LogManager.setup_logging(config.LOG_LEVEL, config.LOG_FILE)
logger = logging.getLogger(__name__)

# إنشاء التطبيقات
bot = telebot.TeleBot(config.API_TOKEN)
app = Flask(__name__)

# إنشاء مديري النظام
db_manager = DatabaseManager()
notification_manager = NotificationManager(bot)
admin_panel = AdminPanel(bot)
payment_handler = PaymentHandler(bot)
invite_manager = InviteManager(bot, db_manager, config)
security_filter = SecurityFilter(db_manager, bot)
download_system = SecureDownloadSystem(bot, db_manager)

# نظام تحديد معدل الطلبات
rate_limiter = RateLimiter(config.RATE_LIMIT_MESSAGES, 60)

# متغير لتتبع حالات المستخدمين
user_states = {}

# إعداد قاعدة البيانات
try:
    db_manager.initialize_database()
    # إضافة ملفات التحميل التجريبية
    db_manager.add_sample_download_files()
    logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize database: {e}")
    sys.exit(1)

# ===== Flask Routes =====

@app.route('/')
def home():
    """الصفحة الرئيسية للتحقق من حالة البوت"""
    status = HealthChecker.get_system_status()
    return jsonify({
        'status': 'running',
        'bot_name': 'Advanced Subscription Bot',
        'version': '2.0.0',
        'system_status': status
    })

@app.route('/health')
def health_check():
    """فحص صحة النظام"""
    status = HealthChecker.get_system_status()
    return jsonify(status)

@app.route(f"/{config.API_TOKEN}", methods=['POST'])
def receive_update():
    """استقبال التحديثات من تليجرام"""
    try:
        if request.headers.get('content-type') == 'application/json':
            update = telebot.types.Update.de_json(request.get_data().decode('utf-8'))
            logger.debug(f"Received update: {update.update_id}")
            bot.process_new_updates([update])
            return '', 200
        else:
            logger.warning("Received non-JSON update")
            return '', 403
    except Exception as e:
        logger.error(f"Error processing update: {e}")
        return '', 500

# ===== Bot Handlers =====

@bot.message_handler(commands=['admin'])
def handle_admin_command(message):
    """معالج أمر الإدارة"""
    user_id = message.from_user.id

    if not admin_panel.is_admin(user_id):
        bot.send_message(user_id, "❌ ليس لديك صلاحية للوصول لهذه الوظيفة")
        return

    admin_panel.show_admin_menu(user_id)

@bot.message_handler(commands=['get_chat_id'])
def handle_get_chat_id(message):
    """الحصول على معرف الدردشة - يعمل في الجروبات والمحادثات الخاصة"""
    try:
        chat_id = message.chat.id
        chat_type = message.chat.type

        if chat_type == 'private':
            # محادثة خاصة
            bot.send_message(chat_id,
                f"🆔 **معرف المحادثة الخاصة:**\n`{chat_id}`\n\n"
                f"💡 هذا معرف المحادثة الخاصة معك، وليس معرف الجروب",
                parse_mode='Markdown')
        else:
            # جروب أو سوبر جروب
            chat_title = message.chat.title or "غير محدد"

            response = f"🆔 **معرف الجروب:**\n`{chat_id}`\n\n"
            response += f"📛 **اسم الجروب:** {chat_title}\n"
            response += f"📊 **نوع الدردشة:** {chat_type}\n\n"

            if chat_id == config.GROUP_ID:
                response += "✅ **هذا هو الجروب المُعرَّف في النظام**"
            else:
                response += f"⚠️ **الجروب المُعرَّف في النظام:** `{config.GROUP_ID}`\n"
                response += "💡 **لتحديث GROUP_ID:** حدث ملف .env وأعد تشغيل البوت"

            bot.send_message(chat_id, response, parse_mode='Markdown')

            # إرسال نسخة للإدارة إذا لم يكن الأدمن في الجروب
            if message.from_user.id != config.ADMIN_ID:
                admin_message = f"🆔 **تم طلب معرف الجروب:**\n\n"
                admin_message += f"👤 **بواسطة:** {message.from_user.first_name} (@{message.from_user.username or 'غير محدد'})\n"
                admin_message += f"📛 **الجروب:** {chat_title}\n"
                admin_message += f"🆔 **المعرف:** `{chat_id}`\n"

                try:
                    bot.send_message(config.ADMIN_ID, admin_message, parse_mode='Markdown')
                except:
                    pass

    except Exception as e:
        logger.error(f"Error in get_chat_id command: {e}")
        bot.send_message(message.chat.id, "❌ حدث خطأ في الحصول على معرف الدردشة")

@bot.message_handler(commands=['check_bot_status'])
def handle_check_bot_status(message):
    """فحص حالة البوت في الجروب المحدد"""
    user_id = message.from_user.id

    # التحقق من أن المستخدم هو الأدمن
    if user_id != config.ADMIN_ID:
        bot.send_message(user_id, "❌ هذا الأمر متاح للإدارة فقط")
        return

    try:
        # فحص حالة البوت في الجروب المحدد
        try:
            bot_member = bot.get_chat_member(config.GROUP_ID, bot.get_me().id)
            chat_info = bot.get_chat(config.GROUP_ID)

            status_message = f"🤖 **حالة البوت في الجروب:**\n\n"
            status_message += f"📛 **اسم الجروب:** {chat_info.title}\n"
            status_message += f"🆔 **معرف الجروب:** `{config.GROUP_ID}`\n"
            status_message += f"👥 **عدد الأعضاء:** {chat_info.member_count if hasattr(chat_info, 'member_count') else 'غير محدد'}\n\n"

            status_message += f"🔍 **حالة البوت:** {bot_member.status}\n"

            if bot_member.status == 'administrator':
                status_message += "✅ **البوت مشرف في الجروب**\n\n"
                status_message += "🔧 **الصلاحيات:**\n"

                # فحص الصلاحيات المحددة
                permissions = [
                    ('can_invite_users', 'دعوة المستخدمين'),
                    ('can_manage_chat', 'إدارة الدردشة'),
                    ('can_delete_messages', 'حذف الرسائل'),
                    ('can_restrict_members', 'تقييد الأعضاء'),
                    ('can_promote_members', 'ترقية الأعضاء'),
                    ('can_change_info', 'تغيير معلومات الجروب'),
                    ('can_pin_messages', 'تثبيت الرسائل')
                ]

                for perm, desc in permissions:
                    if hasattr(bot_member, perm):
                        value = getattr(bot_member, perm)
                        icon = "✅" if value else "❌"
                        status_message += f"{icon} {desc}: {'مُفعل' if value else 'معطل'}\n"

                # اختبار إنشاء رابط دعوة
                status_message += "\n🧪 **اختبار إنشاء رابط الدعوة:**\n"
                try:
                    from datetime import datetime, timedelta
                    test_link = bot.create_chat_invite_link(
                        config.GROUP_ID,
                        member_limit=1,
                        expire_date=int((datetime.now() + timedelta(minutes=1)).timestamp())
                    )
                    status_message += "✅ **نجح إنشاء رابط الدعوة**\n"
                    status_message += f"🔗 **رابط تجريبي:** `{test_link.invite_link}`\n"

                    # حذف الرابط التجريبي
                    try:
                        bot.revoke_chat_invite_link(config.GROUP_ID, test_link.invite_link)
                        status_message += "✅ **تم حذف الرابط التجريبي**\n"
                    except:
                        status_message += "⚠️ **لم يتم حذف الرابط التجريبي**\n"

                except Exception as link_error:
                    status_message += f"❌ **فشل إنشاء رابط الدعوة:** {str(link_error)}\n"

            elif bot_member.status == 'member':
                status_message += "⚠️ **البوت عضو عادي (ليس مشرف)**\n"
                status_message += "🔧 **يجب ترقية البوت لمشرف لإنشاء روابط الدعوة**\n"

            elif bot_member.status == 'left':
                status_message += "❌ **البوت غير موجود في الجروب**\n"
                status_message += "🔧 **يجب إضافة البوت للجروب أولاً**\n"

            elif bot_member.status == 'kicked':
                status_message += "🚫 **البوت محظور من الجروب**\n"
                status_message += "🔧 **يجب إلغاء حظر البوت وإعادة إضافته**\n"

        except Exception as group_error:
            status_message = f"❌ **خطأ في الوصول للجروب:**\n\n"
            status_message += f"🆔 **معرف الجروب:** `{config.GROUP_ID}`\n"
            status_message += f"📝 **الخطأ:** {str(group_error)}\n\n"
            status_message += f"🔧 **الحلول المحتملة:**\n"
            status_message += f"• تأكد من صحة معرف الجروب\n"
            status_message += f"• تأكد من إضافة البوت للجروب\n"
            status_message += f"• تأكد من أن الجروب ليس محذوف\n"

        bot.send_message(user_id, status_message, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"Error checking bot status: {e}")
        bot.send_message(user_id, f"❌ حدث خطأ في فحص حالة البوت: {str(e)}")

@bot.message_handler(commands=['start'])
@log_user_activity("start_command")
def handle_start(message):
    """معالج أمر البدء مع واجهة محسنة وفلترة أمنية"""
    user_id = message.from_user.id
    username = message.from_user.username
    first_name = message.from_user.first_name

    logger.info(f"User {user_id} ({username}) started the bot")

    # فحص أمني أولي
    is_allowed, block_info = security_filter.check_user_access(user_id, "start_command")

    if not is_allowed:
        security_filter.send_blocked_message(user_id, block_info)
        return

    # التحقق من تحديد معدل الطلبات
    if not rate_limiter.is_allowed(user_id):
        bot.send_message(user_id, "⚠️ تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً.")
        return

    # التحقق من حالة المستخدم لتخصيص القائمة
    subscriber = db_manager.get_subscriber(user_id)
    is_active = subscriber and subscriber.get('is_active', False)

    # إنشاء لوحة التحكم الرئيسية المخصصة
    markup = types.InlineKeyboardMarkup(row_width=2)

    if is_active:
        # للمشتركين النشطين
        days_left = 0
        if subscriber and subscriber.get('expire_time'):
            days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))

        if days_left <= 7:
            # إذا كان الاشتراك قارب على الانتهاء
            markup.add(
                types.InlineKeyboardButton("🔄 تجديد الاشتراك", callback_data="payment_methods"),
                types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status")
            )
        else:
            # اشتراك نشط عادي
            markup.add(
                types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status"),
                types.InlineKeyboardButton("📋 تاريخ المدفوعات", callback_data="payment_history")
            )

        markup.add(
            types.InlineKeyboardButton("🔗 رابط الجروب", callback_data="group_link"),
            types.InlineKeyboardButton("⚙️ إعدادات الحساب", callback_data="account_settings")
        )

        # إضافة أزرار التحميل والدعم للمشتركين النشطين فقط
        markup.add(
            types.InlineKeyboardButton("🎮 تحميل PUBG Mobile", callback_data="download_pubg"),
            types.InlineKeyboardButton("📁 ملفاتي", callback_data="my_downloads")
        )
        markup.add(
            types.InlineKeyboardButton("🎧 طلب دعم", callback_data="request_support"),
            types.InlineKeyboardButton("📞 التواصل", callback_data="contact_support")
        )
    else:
        # للمستخدمين غير المشتركين
        markup.add(
            types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
            types.InlineKeyboardButton("📊 معلومات الاشتراك", callback_data="subscription_info")
        )

        markup.add(
            types.InlineKeyboardButton("📄 حالة طلباتي", callback_data="my_status"),
            types.InlineKeyboardButton("� استفسار سريع", callback_data="quick_inquiry")
        )

    # الصف الأخير - المساعدة
    markup.add(
        types.InlineKeyboardButton("❓ مساعدة", callback_data="help")
    )

    # لوحة الإدارة (للمسؤول فقط)
    if user_id == config.ADMIN_ID:
        markup.add(types.InlineKeyboardButton("🎛️ لوحة الإدارة", callback_data="admin_menu"))

    # إنشاء رسالة ترحيب مخصصة
    if is_active:
        # رسالة للمشتركين النشطين
        days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400)) if subscriber.get('expire_time') else 0

        if days_left <= 3:
            welcome_message = f"⚠️ مرحباً {first_name}!\n\n" \
                            f"🔔 تنبيه: سينتهي اشتراكك خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}\n" \
                            f"🔄 جدد اشتراكك الآن لتجنب انقطاع الخدمة\n\n" \
                            f"💎 استمتع بالمحتوى الحصري!"
        else:
            welcome_message = f"🌟 أهلاً وسهلاً {first_name}!\n\n" \
                            f"✅ اشتراكك نشط ({days_left} يوم متبقي)\n" \
                            f"🎯 استمتع بجميع المميزات الحصرية\n" \
                            f"🔗 يمكنك الوصول للجروب من الأزرار أدناه"
    else:
        # رسالة للمستخدمين غير المشتركين
        welcome_message = f"👋 مرحباً {first_name}!\n\n" \
                        f"🌟 مرحبًا بك في نظام الاشتراكات المتقدم\n\n" \
                        f"💎 احصل على اشتراك شهري للوصول إلى:\n" \
                        f"• المحتوى الحصري والمميز\n" \
                        f"• دعم فني متميز 24/7\n" \
                        f"• تنبيهات تلقائية\n" \
                        f"• ضمان الجودة والأمان\n\n" \
                        f"⚡ نظام دفع سريع وآمن\n" \
                        f"🔔 تنبيهات تلقائية قبل انتهاء الاشتراك"

    try:
        bot.send_message(user_id, welcome_message, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "bot_started", f"Username: {username}, Name: {first_name}, Active: {is_active}")

    except Exception as e:
        logger.error(f"Failed to send welcome message to user {user_id}: {e}")

@bot.callback_query_handler(func=lambda call: call.data == "payment_methods")
def handle_payment_methods(call):
    """معالج عرض طرق الدفع مع فلترة أمنية"""
    user_id = call.from_user.id

    try:
        # فحص أمني أولي
        is_allowed, block_info = security_filter.check_user_access(user_id, "payment_methods")

        if not is_allowed:
            bot.answer_callback_query(call.id, "🚫 تم حظرك من استخدام البوت", show_alert=True)
            security_filter.send_blocked_message(user_id, block_info)
            return

        bot.answer_callback_query(call.id)
        payment_handler.show_payment_methods(user_id)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "payment_methods_viewed")

    except Exception as e:
        logger.error(f"Failed to show payment methods to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض طرق الدفع")

@bot.callback_query_handler(func=lambda call: call.data == "my_status")
def handle_my_status(call):
    """معالج عرض حالة الاشتراك"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # الحصول على بيانات المشترك
        subscriber = db_manager.get_subscriber(user_id)

        if subscriber:
            # إصلاح تلقائي لتاريخ الانضمام إذا كان خاطئ
            join_time = subscriber.get('join_time')
            if not join_time or join_time <= 86400:  # إذا كان فارغ أو قريب من 1970
                # تقدير تاريخ الانضمام بناءً على تاريخ الانتهاء
                if subscriber.get('expire_time'):
                    estimated_join_time = subscriber['expire_time'] - (30 * 24 * 60 * 60)  # 30 يوم قبل الانتهاء
                else:
                    estimated_join_time = int(datetime.now().timestamp())

                # تحديث قاعدة البيانات تلقائ<|im_start|>
                try:
                    with db_manager.get_db_cursor() as cursor:
                        cursor.execute(f"""
                            UPDATE {db_manager.subscribers_table}
                            SET join_time = %s
                            WHERE user_id = %s
                        """, (estimated_join_time, user_id))

                    # تحديث البيانات المحلية
                    subscriber['join_time'] = estimated_join_time
                    logger.info(f"Auto-fixed join_time for user {user_id}: {estimated_join_time}")
                except Exception as e:
                    logger.error(f"Failed to auto-fix join_time for user {user_id}: {e}")

            status_message = MessageFormatter.format_subscription_status(subscriber)

            # إضافة تفاصيل إضافية
            join_date = DateTimeHelper.format_datetime(
                DateTimeHelper.timestamp_to_datetime(subscriber['join_time'])
            )
            expire_date = DateTimeHelper.format_datetime(
                DateTimeHelper.timestamp_to_datetime(subscriber['expire_time'])
            )

            detailed_message = f"📊 **حالة اشتراكك:**\n\n" \
                             f"{status_message}\n\n" \
                             f"📅 تاريخ الاشتراك: {join_date}\n" \
                             f"⏰ تاريخ الانتهاء: {expire_date}\n" \
                             f"🔢 عدد مرات التجديد: {subscriber['subscription_count']}\n" \
                             f"💰 إجمالي المدفوعات: {MessageFormatter.format_currency(float(subscriber['total_paid']))}"

            # إضافة أزرار التفاعل
            markup = types.InlineKeyboardMarkup()

            # إذا كان الاشتراك قارب على الانتهاء
            now = datetime.now().timestamp()
            days_left = int((subscriber['expire_time'] - now) / 86400)

            if days_left <= 7:
                markup.add(types.InlineKeyboardButton("🔄 تجديد الاشتراك", callback_data="payment_methods"))

            markup.add(
                types.InlineKeyboardButton("📄 تاريخ المدفوعات", callback_data="payment_history"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

        else:
            detailed_message = "❌ **أنت غير مشترك حالياً**\n\n" \
                             "💡 اشترك الآن للحصول على:\n" \
                             "• وصول كامل للمحتوى الحصري\n" \
                             "• دعم فني متميز\n" \
                             "• تنبيهات تلقائية\n" \
                             "• ضمان الجودة"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

        bot.send_message(user_id, detailed_message, reply_markup=markup, parse_mode='Markdown')

        # تسجيل النشاط
        db_manager.log_activity(user_id, "status_checked")

    except Exception as e:
        logger.error(f"Failed to show status to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في جلب حالة الاشتراك")

@bot.callback_query_handler(func=lambda call: call.data == "help")
def handle_help(call):
    """معالج المساعدة"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        help_message = config.MESSAGES['help_text']

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("💳 طرق الدفع", callback_data="payment_methods"),
            types.InlineKeyboardButton("📞 تواصل مع الإدارة", url=f"tg://user?id={config.ADMIN_ID}")
        )
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        bot.send_message(user_id, help_message, reply_markup=markup, parse_mode='Markdown')

        # تسجيل النشاط
        db_manager.log_activity(user_id, "help_viewed")

    except Exception as e:
        logger.error(f"Failed to show help to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض المساعدة")

@bot.callback_query_handler(func=lambda call: call.data == "subscription_info")
def handle_subscription_info(call):
    """معالج معلومات الاشتراك"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        info_message = config.MESSAGES['subscription_info']

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
            types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
        )

        bot.send_message(user_id, info_message, reply_markup=markup, parse_mode='Markdown')

        # تسجيل النشاط
        db_manager.log_activity(user_id, "subscription_info_viewed")

    except Exception as e:
        logger.error(f"Failed to show subscription info to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض معلومات الاشتراك")

# ===== معالجات الوظائف الجديدة =====

@bot.callback_query_handler(func=lambda call: call.data == "payment_history")
def handle_payment_history(call):
    """معالج تاريخ المدفوعات"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # الحصول على تاريخ المدفوعات
        payment_status = payment_handler.get_payment_status(user_id)

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("💳 دفعة جديدة", callback_data="payment_methods"),
            types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
        )

        bot.send_message(user_id, payment_status, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "payment_history_viewed")

    except Exception as e:
        logger.error(f"Failed to show payment history to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في جلب تاريخ المدفوعات")

@bot.callback_query_handler(func=lambda call: call.data == "group_link")
def handle_group_link(call):
    """معالج رابط الجروب المخفي - أمان مطلق"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id, "🔒 جاري التحقق من الصلاحية...")

        # فحص أمني أولي
        is_allowed, block_info = security_filter.check_user_access(user_id, "group_link")

        if not is_allowed:
            security_filter.send_blocked_message(user_id, block_info)
            return

        # التحقق من صحة وصول المستخدم
        is_valid, message_text = invite_manager.validate_user_access(user_id)

        if not is_valid:
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            bot.send_message(
                user_id,
                f"🚫 **لا يمكن الوصول للجروب**\n\n"
                f"📝 السبب: {message_text}\n\n"
                f"💡 للحصول على الوصول للجروب، يجب أن تكون مشتركاً نشطاً",
                reply_markup=markup,
                parse_mode='Markdown'
            )
            return

        # إنشاء رابط دائم مخفي
        link_data = invite_manager.create_secure_invite_link(user_id)

        if link_data:
            # رسالة بدون عرض الرابط - أمان مطلق
            message = f"🔒 **رابطك الدائم جاهز!**\n\n" \
                     f"✅ **تم التحقق من اشتراكك بنجاح**\n" \
                     f"♾️ **رابط دائم:** يعمل دائماً - لا ينتهي أبداً\n" \
                     f"🔐 **أمان مطلق:** رابط مخفي بالكامل\n\n" \
                     f"🎯 **للدخول للجروب:**\n" \
                     f"اضغط على زر \"🚀 دخول الجروب\" أدناه\n\n" \
                     f"💡 **ملاحظة مهمة:**\n" \
                     f"• هذا نفس الرابط في كل مرة\n" \
                     f"• يعمل طوال فترة اشتراكك\n" \
                     f"• محمي ومخفي بالكامل\n" \
                     f"• لا يمكن نسخه أو مشاركته\n\n" \
                     f"🎉 **استمتع بالمحتوى الحصري!**"

            markup = types.InlineKeyboardMarkup()
            # زر الدخول المباشر - بدون عرض الرابط
            markup.add(types.InlineKeyboardButton("🚀 دخول الجروب", url=link_data['link']))
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

            bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            db_manager.log_activity(user_id, "permanent_hidden_link_created", "Permanent link, hidden for security")

        else:
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔄 حاول مرة أخرى", callback_data="group_link"),
                types.InlineKeyboardButton("📞 التواصل مع الدعم", callback_data="contact_support")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

            bot.send_message(
                user_id,
                "❌ **حدث خطأ في إنشاء الرابط الدائم**\n\n"
                "🔧 يرجى المحاولة مرة أخرى أو التواصل مع الدعم\n"
                "🛡️ نعمل على ضمان أقصى درجات الأمان",
                reply_markup=markup,
                parse_mode='Markdown'
            )

    except Exception as e:
        logger.error(f"Failed to handle hidden group link request for user {user_id}: {e}")

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("🔄 حاول مرة أخرى", callback_data="group_link"),
            types.InlineKeyboardButton("📞 التواصل مع الدعم", callback_data="contact_support")
        )
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        bot.send_message(
            user_id,
            "❌ **خطأ في النظام الأمني**\n\n"
            "🔧 يرجى المحاولة مرة أخرى أو التواصل مع الدعم\n"
            "🛡️ نحافظ على أمان بياناتك",
            reply_markup=markup,
            parse_mode='Markdown'
        )

@bot.callback_query_handler(func=lambda call: call.data == "create_new_link")
def handle_create_new_link(call):
    """معالج تجديد الوصول المخفي"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id, "🔒 جاري تجديد الوصول الآمن...")

        # فحص أمني أولي
        is_allowed, block_info = security_filter.check_user_access(user_id, "group_link")

        if not is_allowed:
            security_filter.send_blocked_message(user_id, block_info)
            return

        # التحقق من أن المستخدم مشترك نشط
        subscriber = db_manager.get_subscriber(user_id)
        is_active = subscriber and subscriber.get('is_active', False)

        if not is_active:
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            bot.send_message(
                user_id,
                "❌ **انتهت صلاحية اشتراكك**\n\n"
                "🔄 جدد اشتراكك للحصول على الوصول\n"
                "💎 استمتع بالمحتوى الحصري مرة أخرى",
                reply_markup=markup,
                parse_mode='Markdown'
            )
            return

        # إنشاء وصول دائم مخفي (إلغاء القديم تلقائياً)
        link_data = invite_manager.create_secure_invite_link(user_id)

        if link_data:
            # رسالة بدون عرض الرابط - أمان مطلق
            message = f"🔄 **تم تحديث الوصول بنجاح**\n\n" \
                     f"✅ **تم إنشاء رابط دائم جديد**\n" \
                     f"♾️ **رابط دائم:** لا ينتهي أبداً\n" \
                     f"🔐 **أمان مطلق:** مخفي بالكامل\n\n" \
                     f"🎯 **للدخول للجروب:**\n" \
                     f"اضغط على زر \"🚀 دخول الجروب\" أدناه\n\n" \
                     f"🛡️ **مميزات الأمان المحدثة:**\n" \
                     f"• رابط مخفي تماماً - لا يظهر أبداً\n" \
                     f"• مستحيل نسخه أو مشاركته\n" \
                     f"• دائم طوال فترة اشتراكك\n" \
                     f"• حماية قصوى من التسريب\n\n" \
                     f"🎉 **استمتع بالمحتوى الحصري!**"

            markup = types.InlineKeyboardMarkup()
            # زر الدخول المباشر - بدون عرض الرابط
            markup.add(types.InlineKeyboardButton("🚀 دخول الجروب", url=link_data['link']))
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

            bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            db_manager.log_activity(user_id, "permanent_access_updated", "Permanent link updated, hidden for security")

        else:
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🔄 حاول مرة أخرى", callback_data="group_link"),
                types.InlineKeyboardButton("📞 التواصل مع الدعم", callback_data="contact_support")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

            bot.send_message(
                user_id,
                "❌ **حدث خطأ في إنشاء الرابط الدائم**\n\n"
                "🔧 يرجى المحاولة مرة أخرى أو التواصل مع الدعم\n"
                "🛡️ نعمل على ضمان أقصى درجات الأمان",
                reply_markup=markup,
                parse_mode='Markdown'
            )

    except Exception as e:
        logger.error(f"Failed to renew hidden access for user {user_id}: {e}")

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("🔄 حاول مرة أخرى", callback_data="group_link"),
            types.InlineKeyboardButton("📞 التواصل مع الدعم", callback_data="contact_support")
        )
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        bot.send_message(
            user_id,
            "❌ **خطأ في النظام الأمني**\n\n"
            "🔧 فشل في إنشاء الرابط الدائم\n"
            "🛡️ يرجى المحاولة مرة أخرى",
            reply_markup=markup,
            parse_mode='Markdown'
        )



@bot.callback_query_handler(func=lambda call: call.data == "account_settings")
def handle_account_settings(call):
    """معالج إعدادات الحساب"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        subscriber = db_manager.get_subscriber(user_id)

        if not subscriber:
            bot.send_message(user_id, "❌ لم يتم العثور على بيانات حسابك")
            return

        # إصلاح تلقائي لتاريخ الانضمام إذا كان خاطئ
        join_time = subscriber.get('join_time')
        if not join_time or join_time <= 86400:  # إذا كان فارغ أو قريب من 1970
            # تقدير تاريخ الانضمام بناءً على تاريخ الانتهاء
            if subscriber.get('expire_time'):
                estimated_join_time = subscriber['expire_time'] - (30 * 24 * 60 * 60)  # 30 يوم قبل الانتهاء
            else:
                estimated_join_time = int(datetime.now().timestamp())

            # تحديث قاعدة البيانات تلقائ<|im_start|>
            try:
                with db_manager.get_db_cursor() as cursor:
                    cursor.execute(f"""
                        UPDATE {db_manager.subscribers_table}
                        SET join_time = %s
                        WHERE user_id = %s
                    """, (estimated_join_time, user_id))

                # تحديث البيانات المحلية
                subscriber['join_time'] = estimated_join_time
                logger.info(f"Auto-fixed join_time in account settings for user {user_id}: {estimated_join_time}")
            except Exception as e:
                logger.error(f"Failed to auto-fix join_time in account settings for user {user_id}: {e}")

        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("📊 إحصائياتي", callback_data="my_statistics")
        )
        markup.add(
            types.InlineKeyboardButton("🔄 تحديث البيانات", callback_data="update_profile")
        )
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        join_date = datetime.fromtimestamp(subscriber['join_time']).strftime('%Y-%m-%d') if subscriber.get('join_time') else 'غير محدد'

        message = f"⚙️ إعدادات الحساب\n\n" \
                 f"👤 الاسم: {subscriber.get('first_name', 'غير محدد')}\n" \
                 f"📝 اسم المستخدم: @{subscriber.get('username', 'غير محدد')}\n" \
                 f"📅 تاريخ الانضمام: {join_date}\n" \
                 f"🔄 عدد التجديدات: {subscriber.get('subscription_count', 0)}\n" \
                 f"💰 إجمالي المدفوعات: {float(subscriber.get('total_paid', 0)):.2f} جنيه\n\n" \
                 f"اختر الإعداد الذي تريد تعديله:"

        bot.send_message(user_id, message, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "account_settings_viewed")

    except Exception as e:
        logger.error(f"Failed to show account settings to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض إعدادات الحساب")

# تم حذف معالج العروض الخاصة - لم تعد مطلوبة

@bot.callback_query_handler(func=lambda call: call.data == "contact_support")
def handle_contact_support(call):
    """معالج التواصل مع الدعم - للمشتركين النشطين فقط"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # التحقق من أن المستخدم مشترك نشط
        subscriber = db_manager.get_subscriber(user_id)
        is_active = subscriber and subscriber.get('is_active', False)

        if not is_active:
            # المستخدم غير مشترك - رفض الوصول
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            message = f"🚫 **خدمة الدعم للمشتركين فقط**\n\n" \
                     f"📞 للحصول على الدعم الفني المتميز، يجب أن تكون مشتركاً نشطاً\n\n" \
                     f"💎 **مميزات الدعم للمشتركين:**\n" \
                     f"• دعم فني متخصص 24/7\n" \
                     f"• أولوية في الرد (أقل من 30 دقيقة)\n" \
                     f"• حل سريع للمشاكل التقنية\n" \
                     f"• استشارات مجانية\n\n" \
                     f"🚀 اشترك الآن للحصول على الدعم المتميز!"

            bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل محاولة الوصول غير المصرح بها
            db_manager.log_activity(user_id, "contact_support_denied_non_subscriber")
            return

        # المستخدم مشترك نشط - السماح بالوصول
        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("📞 راسل الإدارة", url=f"tg://user?id={config.ADMIN_ID}"),
            types.InlineKeyboardButton("❓ الأسئلة الشائعة", callback_data="help")
        )
        markup.add(
            types.InlineKeyboardButton("📧 إرسال رسالة", callback_data="send_message_to_admin"),
            types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
        )

        message = f"📞 التواصل مع الدعم\n\n" \
                 f"🕐 ساعات العمل: 24/7\n" \
                 f"⚡ متوسط وقت الرد: أقل من 30 دقيقة\n" \
                 f"🎯 نحن هنا لمساعدتك!\n\n" \
                 f"📋 يمكنك التواصل معنا لـ:\n" \
                 f"• مشاكل في الدفع\n" \
                 f"• مشاكل في الوصول للجروب\n" \
                 f"• استفسارات عامة\n" \
                 f"• اقتراحات وتحسينات\n\n" \
                 f"اختر طريقة التواصل المناسبة:"

        bot.send_message(user_id, message, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "contact_support_viewed")

    except Exception as e:
        logger.error(f"Failed to show contact support to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض معلومات الدعم")

# ===== معالجات إضافية للوظائف الجديدة =====

# تم حذف معالج إعدادات التنبيهات - التنبيهات مُفعلة تلقائياً للجميع
# @bot.callback_query_handler(func=lambda call: call.data == "notification_settings")
def handle_notification_settings(call):
    """معالج إعدادات التنبيهات"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 إعدادات الحساب", callback_data="account_settings"))

        message = f"🔔 إعدادات التنبيهات\n\n" \
                 f"✅ **التنبيهات مُفعلة تلقائ<|im_start|> للجميع**\n\n" \
                 f"📋 التنبيهات التي تصلك:\n" \
                 f"• 🔔 تنبيه قبل انتهاء الاشتراك بـ 3 أيام\n" \
                 f"• ⚠️ تنبيه قبل انتهاء الاشتراك بـ 1 يوم\n" \
                 f"• 🚨 تنبيه عند انتهاء الاشتراك\n" \
                 f"• 💳 تأكيدات المدفوعات\n" \
                 f"• 📢 إعلانات مهمة من الإدارة\n\n" \
                 f"💡 **ملاحظة:** التنبيهات مُفعلة دائماً لضمان عدم فوات أي معلومة مهمة"

        bot.send_message(user_id, message, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "notification_settings_viewed")

    except Exception as e:
        logger.error(f"Failed to show notification settings to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض إعدادات التنبيهات")

@bot.callback_query_handler(func=lambda call: call.data == "my_statistics")
def handle_my_statistics(call):
    """معالج إحصائياتي"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        subscriber = db_manager.get_subscriber(user_id)

        if not subscriber:
            bot.send_message(user_id, "❌ لم يتم العثور على بيانات حسابك")
            return

        # إصلاح تلقائي لتاريخ الانضمام إذا كان خاطئ
        join_time = subscriber.get('join_time')
        if not join_time or join_time <= 86400:  # إذا كان فارغ أو قريب من 1970
            # تقدير تاريخ الانضمام بناءً على تاريخ الانتهاء
            if subscriber.get('expire_time'):
                estimated_join_time = subscriber['expire_time'] - (30 * 24 * 60 * 60)  # 30 يوم قبل الانتهاء
            else:
                estimated_join_time = int(datetime.now().timestamp())

            # تحديث قاعدة البيانات تلقائ<|im_start|>
            try:
                with db_manager.get_db_cursor() as cursor:
                    cursor.execute(f"""
                        UPDATE {db_manager.subscribers_table}
                        SET join_time = %s
                        WHERE user_id = %s
                    """, (estimated_join_time, user_id))

                # تحديث البيانات المحلية
                subscriber['join_time'] = estimated_join_time
                logger.info(f"Auto-fixed join_time in statistics for user {user_id}: {estimated_join_time}")
            except Exception as e:
                logger.error(f"Failed to auto-fix join_time in statistics for user {user_id}: {e}")

        # حساب الإحصائيات
        join_date = datetime.fromtimestamp(subscriber['join_time']) if subscriber.get('join_time') else datetime.now()
        days_since_join = (datetime.now() - join_date).days

        # حساب متوسط الاستخدام الشهري
        monthly_avg = subscriber.get('subscription_count', 0) / max(1, days_since_join / 30)

        # حساب إجمالي المدفوعات
        total_paid = float(subscriber.get('total_paid', 0))

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("📊 تفاصيل أكثر", callback_data="detailed_statistics"),
            types.InlineKeyboardButton("🔙 إعدادات الحساب", callback_data="account_settings")
        )

        message = f"📊 إحصائياتك الشخصية\n\n" \
                 f"📅 عضو منذ: {days_since_join} يوم\n" \
                 f"🔄 عدد التجديدات: {subscriber.get('subscription_count', 0)}\n" \
                 f"💰 إجمالي المدفوعات: {total_paid:.2f} جنيه\n" \
                 f"📈 متوسط الاستخدام الشهري: {monthly_avg:.1f}\n\n" \
                 f"🏆 مستوى العضوية: {'VIP' if subscriber.get('subscription_count', 0) >= 5 else 'عادي'}"

        bot.send_message(user_id, message, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "my_statistics_viewed")

    except Exception as e:
        logger.error(f"Failed to show user statistics to user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض الإحصائيات")

@bot.callback_query_handler(func=lambda call: call.data == "send_message_to_admin")
def handle_send_message_to_admin(call):
    """معالج إرسال رسالة للإدارة - للمشتركين النشطين فقط"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # التحقق من أن المستخدم مشترك نشط
        subscriber = db_manager.get_subscriber(user_id)
        is_active = subscriber and subscriber.get('is_active', False)

        if not is_active:
            # المستخدم غير مشترك - رفض الوصول
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            message = f"🚫 **إرسال الرسائل للمشتركين فقط**\n\n" \
                     f"📧 لإرسال رسائل للإدارة، يجب أن تكون مشتركاً نشطاً\n\n" \
                     f"💎 **مميزات التواصل للمشتركين:**\n" \
                     f"• إرسال رسائل مباشرة للإدارة\n" \
                     f"• رد سريع ومتخصص\n" \
                     f"• أولوية في حل المشاكل\n" \
                     f"• دعم فني متقدم\n\n" \
                     f"🚀 اشترك الآن للحصول على الدعم المتميز!"

            bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل محاولة الوصول غير المصرح بها
            db_manager.log_activity(user_id, "send_message_to_admin_denied_non_subscriber")
            return

        # المستخدم مشترك نشط - السماح بإرسال الرسالة
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 التواصل مع الدعم", callback_data="contact_support"))

        message = f"📧 إرسال رسالة للإدارة\n\n" \
                 f"اكتب رسالتك وسيتم إرسالها مباشرة للإدارة\n" \
                 f"سيتم الرد عليك في أقرب وقت ممكن\n\n" \
                 f"💡 نصائح لرسالة أفضل:\n" \
                 f"• اذكر المشكلة بوضوح\n" \
                 f"• أرفق لقطات شاشة إذا أمكن\n" \
                 f"• اذكر رقم الدفعة إذا كانت المشكلة متعلقة بالدفع"

        bot.send_message(user_id, message, reply_markup=markup)

        # تعيين حالة انتظار رسالة للإدارة
        user_states[user_id] = {'waiting_for_admin_message': True}

        # تسجيل النشاط
        db_manager.log_activity(user_id, "send_message_to_admin_initiated")

    except Exception as e:
        logger.error(f"Failed to initiate send message to admin for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في إعداد إرسال الرسالة")

# ===== معالجات نظام الدعم =====

@bot.callback_query_handler(func=lambda call: call.data == "request_support")
def handle_request_support(call):
    """معالج طلب الدعم للمشتركين فقط"""
    user_id = call.from_user.id
    username = call.from_user.username
    first_name = call.from_user.first_name

    try:
        bot.answer_callback_query(call.id)

        # فحص أمني أولي
        is_allowed, block_info = security_filter.check_user_access(user_id, "request_support")

        if not is_allowed:
            security_filter.send_blocked_message(user_id, block_info)
            return

        # التحقق من أن المستخدم مشترك نشط
        subscriber = db_manager.get_subscriber(user_id)
        is_active = subscriber and subscriber.get('is_active', False)

        if not is_active:
            # المستخدم غير مشترك - رفض الوصول
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            message = f"🚫 **الدعم للمشتركين فقط**\n\n" \
                     f"🎧 للحصول على الدعم المتميز، يجب أن تكون مشتركاً نشطاً\n\n" \
                     f"💎 **مميزات الدعم للمشتركين:**\n" \
                     f"• دعم مباشر مع فريق متخصص\n" \
                     f"• نظام طوابير ذكي\n" \
                     f"• إشعارات فورية عند حلول الدور\n" \
                     f"• أولوية في حل المشاكل\n" \
                     f"• دعم فني متقدم\n\n" \
                     f"🚀 اشترك الآن للحصول على الدعم المتميز!"

            bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')
            return

        # استخدام نظام الدعم
        from support_system import SupportSystem
        support_system = SupportSystem(bot)

        # طلب الدعم
        result = support_system.request_support(user_id, username, first_name)

        if result['success']:
            # نجح الطلب
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📊 حالة الطابور", callback_data="check_support_status"),
                types.InlineKeyboardButton("❌ إلغاء الطلب", callback_data="cancel_support_request")
            )
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

            bot.send_message(user_id, result['message'], reply_markup=markup)

            # تسجيل النشاط
            db_manager.log_activity(user_id, "support_requested", f"Queue position: {result.get('position', 'unknown')}")
        else:
            # فشل الطلب
            markup = types.InlineKeyboardMarkup()
            if "لديك طلب دعم نشط" in result['message']:
                markup.add(types.InlineKeyboardButton("📊 حالة الطابور", callback_data="check_support_status"))
            markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

            bot.send_message(user_id, result['message'], reply_markup=markup)

    except Exception as e:
        logger.error(f"Failed to handle support request for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في طلب الدعم")

@bot.callback_query_handler(func=lambda call: call.data == "check_support_status")
def handle_check_support_status(call):
    """معالج التحقق من حالة طلب الدعم"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        from support_system import SupportSystem
        support_system = SupportSystem(bot)

        result = support_system.check_queue_status(user_id)

        markup = types.InlineKeyboardMarkup()
        if result['success'] and result.get('status') == 'waiting':
            markup.add(types.InlineKeyboardButton("❌ إلغاء الطلب", callback_data="cancel_support_request"))
        markup.add(
            types.InlineKeyboardButton("🔄 تحديث", callback_data="check_support_status"),
            types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
        )

        bot.send_message(user_id, result['message'], reply_markup=markup)

    except Exception as e:
        logger.error(f"Failed to check support status for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في التحقق من حالة الدعم")

@bot.callback_query_handler(func=lambda call: call.data == "cancel_support_request")
def handle_cancel_support_request(call):
    """معالج إلغاء طلب الدعم"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        from support_system import SupportSystem
        support_system = SupportSystem(bot)

        result = support_system.cancel_support_request(user_id)

        markup = types.InlineKeyboardMarkup()
        if result['success']:
            markup.add(types.InlineKeyboardButton("🎧 طلب دعم جديد", callback_data="request_support"))
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        bot.send_message(user_id, result['message'], reply_markup=markup)

        # تسجيل النشاط
        if result['success']:
            db_manager.log_activity(user_id, "support_request_cancelled")

    except Exception as e:
        logger.error(f"Failed to cancel support request for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في إلغاء طلب الدعم")

@bot.callback_query_handler(func=lambda call: call.data == "contact_support")
def handle_contact_support(call):
    """معالج التواصل مع الدعم للمشتركين فقط"""
    user_id = call.from_user.id
    first_name = call.from_user.first_name

    try:
        bot.answer_callback_query(call.id)

        # فحص أمني أولي
        is_allowed, block_info = security_filter.check_user_access(user_id, "contact_support")

        if not is_allowed:
            security_filter.send_blocked_message(user_id, block_info)
            return

        # التحقق من أن المستخدم مشترك نشط
        subscriber = db_manager.get_subscriber(user_id)
        is_active = subscriber and subscriber.get('is_active', False)

        if not is_active:
            # المستخدم غير مشترك - رفض الوصول
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            message = f"🚫 **إرسال الرسائل للمشتركين فقط**\n\n" \
                     f"📧 لإرسال رسائل للإدارة، يجب أن تكون مشتركاً نشطاً\n\n" \
                     f"💎 **مميزات التواصل للمشتركين:**\n" \
                     f"• إرسال رسائل مباشرة للإدارة\n" \
                     f"• رد سريع ومتخصص\n" \
                     f"• أولوية في حل المشاكل\n" \
                     f"• دعم فني متقدم\n\n" \
                     f"🚀 اشترك الآن للحصول على الدعم المتميز!"

            bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')
            return

        # المستخدم مشترك نشط - السماح بالتواصل
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        message = f"📞 **التواصل مع الدعم**\n\n" \
                 f"👋 مرحباً {first_name}!\n\n" \
                 f"📝 اكتب رسالتك وسيتم توصيلها للإدارة فوراً\n" \
                 f"⚡ ستحصل على رد سريع ومتخصص\n\n" \
                 f"💡 **نصائح لرسالة أفضل:**\n" \
                 f"• اذكر المشكلة بوضوح\n" \
                 f"• أرفق لقطات شاشة إن أمكن\n" \
                 f"• اذكر معرف المستخدم إذا كان مطلوباً\n\n" \
                 f"📨 اكتب رسالتك الآن:"

        bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

        # تعيين حالة انتظار الرسالة
        user_states[user_id] = {'waiting_for_admin_message': True}

        # تسجيل النشاط
        db_manager.log_activity(user_id, "contact_support_initiated")

    except Exception as e:
        logger.error(f"Failed to handle contact support for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في التواصل مع الدعم")

# ===== معالجات إضافية =====

# تم حذف معالجات تفعيل/إيقاف التنبيهات - التنبيهات مُفعلة تلقائ<|im_start|> للجميع

# ===== معالجات طرق الدفع =====

@bot.callback_query_handler(func=lambda call: call.data.startswith("payment_"))
def handle_payment_callbacks(call):
    """معالج callbacks طرق الدفع"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        if call.data == "payment_vodafone":
            payment_handler.show_vodafone_payment(user_id)
        elif call.data == "payment_instapay":
            payment_handler.show_instapay_payment(user_id)
        elif call.data == "payment_card":
            bot.send_message(user_id, "💳 دفع بالبطاقة الائتمانية سيتوفر قريباً")

    except Exception as e:
        logger.error(f"Failed to handle payment callback {call.data} for user {user_id}: {e}")

@bot.callback_query_handler(func=lambda call: call.data.startswith("upload_proof_"))
def handle_upload_proof_callbacks(call):
    """معالج callbacks رفع إثبات الدفع"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # استخراج طريقة الدفع من callback data
        _ = call.data.replace("upload_proof_", "")

        message = "📸 **أرسل إثبات الدفع الآن**\n\n" \
                 "يمكنك إرسال:\n" \
                 "• صورة لرسالة التأكيد\n" \
                 "• صورة للإيصال\n" \
                 "• رقم العملية كنص\n\n" \
                 "⚠️ تأكد من وضوح الصورة وظهور جميع التفاصيل"

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 طرق دفع أخرى", callback_data="payment_methods"))

        bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

        # حفظ حالة انتظار إثبات الدفع
        user_states[user_id] = {'waiting_for_payment_proof': True}

    except Exception as e:
        logger.error(f"Failed to handle upload proof callback for user {user_id}: {e}")

# معالج استقبال إثبات الدفع
@bot.message_handler(content_types=['photo', 'document', 'text'])
def handle_payment_proof(message):
    """معالج استقبال إثبات الدفع المحسن"""
    user_id = message.from_user.id

    # تجاهل الرسائل من الإدارة
    if user_id == config.ADMIN_ID:
        return

    # التحقق من تحديد معدل الطلبات
    if not rate_limiter.is_allowed(user_id):
        bot.send_message(user_id, "⚠️ تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً.")
        return

    # التحقق من صحة المحتوى
    if message.content_type == 'text':
        if not SecurityManager.validate_user_input(message.text):
            bot.send_message(user_id, "❌ النص المرسل غير صالح. يرجى إرسال إثبات دفع صحيح.")
            return

    try:
        # معالجة إثبات الدفع
        success = payment_handler.handle_payment_proof(message)

        if success:
            logger.info(f"Payment proof received from user {user_id}")
        else:
            logger.warning(f"Failed to process payment proof from user {user_id}")

    except Exception as e:
        logger.error(f"Error handling payment proof from user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في معالجة إثبات الدفع. يرجى المحاولة مرة أخرى.")

# ===== معالجات لوحة الإدارة =====



@bot.callback_query_handler(func=lambda call: call.data.startswith("approve_payment_"))
def handle_approve_payment(call):
    """معالج الموافقة على المدفوعات"""
    user_id = call.from_user.id

    if not admin_panel.is_admin(user_id):
        bot.answer_callback_query(call.id, "❌ ليس لديك صلاحية", show_alert=True)
        return

    try:
        payment_id = int(call.data.replace("approve_payment_", ""))
        bot.answer_callback_query(call.id, "✅ جاري معالجة الموافقة...")

        admin_panel.approve_payment(user_id, payment_id)

    except Exception as e:
        logger.error(f"Failed to approve payment: {e}")
        bot.answer_callback_query(call.id, "❌ حدث خطأ في الموافقة", show_alert=True)

@bot.callback_query_handler(func=lambda call: call.data.startswith("reject_payment_"))
def handle_reject_payment(call):
    """معالج رفض المدفوعات"""
    user_id = call.from_user.id

    if not admin_panel.is_admin(user_id):
        bot.answer_callback_query(call.id, "❌ ليس لديك صلاحية", show_alert=True)
        return

    try:
        payment_id = int(call.data.replace("reject_payment_", ""))
        bot.answer_callback_query(call.id, "❌ جاري معالجة الرفض...")

        admin_panel.reject_payment(user_id, payment_id, "تم الرفض من قبل الإدارة")

    except Exception as e:
        logger.error(f"Failed to reject payment: {e}")
        bot.answer_callback_query(call.id, "❌ حدث خطأ في الرفض", show_alert=True)

# ===== معالجات لوحة الإدارة =====

@bot.callback_query_handler(func=lambda call: call.data.startswith("admin_"))
def handle_admin_callbacks(call):
    user_id = call.from_user.id

    # التحقق من صلاحيات الإدارة
    if not admin_panel.is_admin(user_id):
        bot.answer_callback_query(call.id, "❌ ليس لديك صلاحية للوصول لهذه الوظيفة", show_alert=True)
        return

    try:
        bot.answer_callback_query(call.id)

        if call.data == "admin_menu":
            admin_panel.show_admin_menu(user_id)
        elif call.data == "admin_stats":
            admin_panel.show_statistics(user_id)
        elif call.data == "admin_payments":
            admin_panel.show_payments_management(user_id)
        elif call.data == "admin_pending_payments":
            admin_panel.show_pending_payments(user_id)
        elif call.data.startswith("admin_pending_payments_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_pending_payments(user_id, page)
        elif call.data == "admin_reports":
            admin_panel.show_payments_report(user_id)
        elif call.data == "admin_settings":
            admin_panel.show_settings_menu(user_id)
        elif call.data == "admin_tools":
            admin_panel.show_tools_menu(user_id)
        elif call.data == "admin_broadcast":
            admin_panel.show_broadcast_menu(user_id)
        elif call.data == "admin_refresh":
            admin_panel.show_admin_menu(user_id)
        elif call.data == "admin_approved_payments":
            admin_panel.show_approved_payments(user_id)
        elif call.data.startswith("admin_approved_payments_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_approved_payments(user_id, page)
        elif call.data == "admin_rejected_payments":
            admin_panel.show_rejected_payments(user_id)
        elif call.data.startswith("admin_rejected_payments_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_rejected_payments(user_id, page)
        elif call.data == "admin_payments_report":
            admin_panel.show_payments_report(user_id)

        # معالجات إدارة المشتركين
        elif call.data == "admin_subscribers":
            logger.info(f"Admin {user_id} accessing subscribers management")
            try:
                admin_panel.show_subscribers_management(user_id)
            except Exception as e:
                logger.error(f"Error in admin_subscribers: {e}")
                bot.send_message(user_id, f"❌ خطأ في إدارة المشتركين: {str(e)}")
        elif call.data == "admin_list_subscribers":
            try:
                admin_panel.show_subscribers_list(user_id)
            except Exception as e:
                logger.error(f"Error in admin_list_subscribers: {e}")
                bot.send_message(user_id, f"❌ خطأ في عرض قائمة المشتركين: {str(e)}")
        elif call.data.startswith("admin_list_subscribers_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_subscribers_list(user_id, page)

        elif call.data == "admin_list_subscribers":
            admin_panel.show_subscribers_list(user_id)

        # معالجات أزرار المشتركين الجديدة
        elif call.data.startswith("view_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            admin_panel.show_subscriber_details(user_id, target_user_id)

        elif call.data.startswith("confirm_delete_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            admin_panel.confirm_delete_subscriber(user_id, target_user_id)

        elif call.data.startswith("delete_subscriber_confirmed_"):
            target_user_id = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, "🗑️ جاري حذف المشترك...")
            admin_panel.delete_subscriber(user_id, target_user_id)

        elif call.data.startswith("ban_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            bot.send_message(user_id, f"🚫 حظر المشترك {target_user_id}\n\nأرسل سبب الحظر:")
            user_states[user_id] = {'waiting_for_subscriber_ban_reason': True, 'target_user_id': target_user_id}

        elif call.data.startswith("extend_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            bot.send_message(user_id, f"⏰ تمديد مخصص لاشتراك المستخدم {target_user_id}\n\nأرسل عدد الأيام للتمديد:")
            user_states[user_id] = {'waiting_for_extension_days': True, 'target_user_id': target_user_id}

        elif call.data.startswith("add_days_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            bot.send_message(user_id, f"➕ زيادة أيام لاشتراك المستخدم {target_user_id}\n\nأرسل عدد الأيام المراد إضافتها:")
            user_states[user_id] = {'waiting_for_add_days': True, 'target_user_id': target_user_id}

        elif call.data.startswith("reduce_days_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            bot.send_message(user_id, f"➖ تقليل أيام من اشتراك المستخدم {target_user_id}\n\nأرسل عدد الأيام المراد تقليلها:")
            user_states[user_id] = {'waiting_for_reduce_days': True, 'target_user_id': target_user_id}

        elif call.data.startswith("quick_extend_subscriber_"):
            target_user_id = int(call.data.split("_")[-1])
            admin_panel.quick_extend_subscription(user_id, target_user_id)

        elif call.data.startswith("quick_extend_"):
            # تمديد سريع بعدد أيام محدد
            parts = call.data.split("_")
            target_user_id = int(parts[2])
            days = int(parts[3])
            bot.answer_callback_query(call.id, f"⏰ جاري تمديد الاشتراك بـ {days} أيام...")
            admin_panel.extend_subscription(user_id, target_user_id, days)

        # معالجات نظام التحميل
        elif call.data == "download_pubg":
            bot.answer_callback_query(call.id, "🎮 جاري تحميل قائمة الملفات...")
            download_system.show_download_menu(user_id)

        elif call.data == "my_downloads":
            bot.answer_callback_query(call.id, "📊 جاري عرض سجل التحميلات...")
            download_system.show_download_history(user_id)

        elif call.data == "download_history":
            bot.answer_callback_query(call.id, "📊 جاري عرض سجل التحميلات...")
            download_system.show_download_history(user_id)

        elif call.data.startswith("download_file_"):
            file_id = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, "🔐 جاري إنشاء رابط التحميل الآمن...")
            download_system.handle_file_download_request(user_id, file_id)

        elif call.data.startswith("file_downloaded_"):
            file_id = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, "✅ تم تحميل هذا الملف من قبل", show_alert=True)

        # معالجات إدارة الملفات للأدمن
        elif call.data == "admin_files":
            bot.answer_callback_query(call.id, "📁 جاري تحميل إدارة الملفات...")
            admin_panel.show_files_management(user_id)

        elif call.data == "admin_files_list":
            bot.answer_callback_query(call.id, "📋 جاري تحميل قائمة الملفات...")
            admin_panel.show_files_list(user_id)

        elif call.data.startswith("admin_files_list_"):
            page = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, f"📄 الصفحة {page}")
            admin_panel.show_files_list(user_id, page)

        elif call.data.startswith("admin_manage_file_"):
            file_id = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, "⚙️ جاري تحميل إدارة الملف...")
            admin_panel.show_file_management(user_id, file_id)

        elif call.data == "admin_add_file":
            bot.answer_callback_query(call.id, "➕ إضافة ملف جديد")
            bot.send_message(user_id,
                "➕ **إضافة ملف جديد**\n\n"
                "📝 أرسل اسم الملف الذي تريد إضافته:\n"
                "مثال: PUBG Mobile v3.1 Portable",
                parse_mode='Markdown'
            )
            user_states[user_id] = {'waiting_for_file_name': True}

        elif call.data == "admin_download_stats":
            bot.answer_callback_query(call.id, "📊 جاري تحميل إحصائيات التحميل...")
            admin_panel.show_download_statistics(user_id)

        # معالجات اختيار فئة الملف
        elif call.data.startswith("file_category_"):
            category = call.data.split("_")[-1]
            category_names = {
                'game': 'لعبة',
                'tools': 'أدوات',
                'utility': 'مساعدة',
                'general': 'عام'
            }

            if user_id in user_states and user_states[user_id].get('waiting_for_file_category'):
                user_states[user_id].update({
                    'waiting_for_file_description': True,
                    'file_category': category,
                    'waiting_for_file_category': False
                })

                bot.answer_callback_query(call.id, f"✅ تم اختيار فئة: {category_names.get(category, category)}")
                bot.send_message(user_id,
                    f"✅ **فئة الملف:** {category_names.get(category, category)}\n\n"
                    "📝 **أخيراً، أرسل وصف الملف:**\n"
                    "مثال: نسخة محمولة من PUBG Mobile الإصدار 3.1 - لا تحتاج تثبيت",
                    parse_mode='Markdown'
                )
            else:
                bot.answer_callback_query(call.id, "❌ انتهت جلسة إضافة الملف", show_alert=True)
        elif call.data == "admin_search_subscriber":
            bot.send_message(user_id, "🔍 البحث عن مشترك\n\nأرسل معرف المستخدم أو اسم المستخدم أو الاسم للبحث:")
            user_states[user_id] = {'waiting_for_search_query': True}
        elif call.data == "admin_expiring_soon":
            admin_panel.show_expiring_soon(user_id)
        elif call.data == "admin_expired":
            admin_panel.show_expired_subscribers(user_id)
        elif call.data == "admin_add_test_subscriber":
            admin_panel.add_test_subscriber(user_id)
        elif call.data == "admin_add_subscriber":
            bot.send_message(user_id, "➕ إضافة مشترك\n\nأرسل معرف المستخدم (User ID) لإضافته:")
            user_states[user_id] = {'waiting_for_add_subscriber': True}
        elif call.data == "admin_remove_subscriber":
            bot.send_message(user_id, "🗑️ حذف مشترك\n\nأرسل معرف المستخدم (User ID) لحذفه:")
            user_states[user_id] = {'waiting_for_remove_subscriber': True}

        elif call.data == "admin_cleanup_expired":
            admin_panel.cleanup_expired_subscribers(user_id)
        elif call.data == "admin_send_reminder_all":
            admin_panel.send_reminder_to_all_expiring(user_id)

        # معالجات التنقل للمشتركين
        elif call.data.startswith("admin_expiring_soon_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_expiring_soon(user_id, page)
        elif call.data.startswith("admin_expired_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_expired_subscribers(user_id, page)

        # معالجات الأدوات
        elif call.data == "admin_cleanup_db":
            admin_panel.cleanup_database(user_id)
        elif call.data == "admin_backup_db":
            admin_panel.create_backup(user_id)

        # معالجات الأمان وقائمة الحظر
        elif call.data == "admin_security":
            admin_panel.show_security_menu(user_id)

        # معالجات قائمة الأمان الجديدة
        elif call.data == "security_users_list":
            admin_panel.show_security_users_list(user_id)

        elif call.data.startswith("security_users_page_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_security_users_list(user_id, page)

        elif call.data.startswith("security_user_details_"):
            target_user_id = int(call.data.split("_")[-1])
            admin_panel.show_user_security_details(user_id, target_user_id)

        elif call.data.startswith("security_ban_user_"):
            target_user_id = int(call.data.split("_")[-1])
            bot.send_message(user_id, f"🚫 حظر المستخدم {target_user_id}\n\nأرسل سبب الحظر:")
            user_states[user_id] = {'waiting_for_ban_reason': True, 'target_user_id': target_user_id}

        elif call.data.startswith("security_unban_user_"):
            target_user_id = int(call.data.split("_")[-1])
            try:
                result = admin_panel.unban_user(user_id, target_user_id)
                if result:
                    bot.answer_callback_query(call.id, "✅ تم إلغاء حظر المستخدم")
                    admin_panel.show_user_security_details(user_id, target_user_id)
                else:
                    bot.answer_callback_query(call.id, "❌ فشل في إلغاء الحظر")
            except Exception as e:
                logger.error(f"Failed to unban user {target_user_id}: {e}")
                bot.answer_callback_query(call.id, "❌ حدث خطأ في إلغاء الحظر")

        elif call.data == "security_search_user":
            bot.send_message(user_id, "🔍 البحث عن مستخدم\n\nأرسل معرف المستخدم (User ID) للبحث عنه:")
            user_states[user_id] = {'waiting_for_user_search': True}

        elif call.data == "security_quick_ban":
            bot.send_message(user_id, "🚫 حظر سريع\n\nأرسل معرف المستخدم (User ID) المراد حظره:")
            user_states[user_id] = {'waiting_for_quick_ban_id': True}

        elif call.data == "security_filter_high_risk":
            bot.send_message(user_id, "🔴 عرض المستخدمين عالي الخطر\n\n🚧 هذه الوظيفة قيد التطوير")

        elif call.data == "security_filter_blocked":
            admin_panel.show_blacklist_menu(user_id)

        elif call.data == "security_filter_subscribers":
            bot.send_message(user_id, "👥 عرض المشتركين فقط\n\n🚧 هذه الوظيفة قيد التطوير")

        elif call.data == "security_filter_visitors":
            bot.send_message(user_id, "👻 عرض الزوار فقط\n\n🚧 هذه الوظيفة قيد التطوير")

        elif call.data == "admin_blacklist":
            admin_panel.show_blacklist_menu(user_id)

        elif call.data.startswith("blacklist_page_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_blacklist_menu(user_id, page)

        elif call.data.startswith("view_banned_user_"):
            target_user_id = int(call.data.split("_")[-1])
            admin_panel.show_banned_user_details(user_id, target_user_id)

        elif call.data.startswith("unban_user_"):
            target_user_id = int(call.data.split("_")[-1])
            try:
                result = admin_panel.unban_user(user_id, target_user_id)
                if result:
                    bot.answer_callback_query(call.id, "✅ تم رفع الحظر بنجاح")
                    # إعادة عرض قائمة الحظر
                    admin_panel.show_blacklist_menu(user_id)
                else:
                    bot.answer_callback_query(call.id, "❌ فشل في رفع الحظر")
            except Exception as e:
                logger.error(f"Failed to unban user {target_user_id}: {e}")
                bot.answer_callback_query(call.id, "❌ حدث خطأ في رفع الحظر")

        elif call.data == "ban_statistics":
            admin_panel.show_ban_statistics(user_id)

        elif call.data == "clear_all_bans":
            admin_panel.confirm_clear_all_bans(user_id)

        elif call.data == "confirm_clear_all_bans":
            bot.answer_callback_query(call.id, "🗑️ جاري مسح جميع المحظورين...")
            admin_panel.clear_all_bans(user_id)

        elif call.data == "admin_security_report":
            # تقرير الأمان
            try:
                blacklist_stats = db_manager.get_blacklist_stats()

                message = "📊 **تقرير الأمان الشامل**\n\n"

                if blacklist_stats:
                    message += f"🚫 **إحصائيات قائمة الحظر:**\n"
                    message += f"• إجمالي المحظورين: {blacklist_stats['total_banned']}\n"
                    message += f"• حظر دائم: {blacklist_stats['permanent_bans']}\n"
                    message += f"• حظر مؤقت: {blacklist_stats['temporary_bans']}\n"
                    message += f"• محظورين اليوم: {blacklist_stats['banned_today']}\n\n"

                # إحصائيات إضافية
                with db_manager.get_db_cursor() as cursor:
                    # محاولات الوصول المحظورة اليوم
                    cursor.execute(f"""
                        SELECT COUNT(*) as blocked_attempts
                        FROM {db_manager.activity_logs_table}
                        WHERE action = 'blocked_access_attempt'
                        AND DATE(created_at) = CURRENT_DATE
                    """)
                    blocked_today = cursor.fetchone()['blocked_attempts']

                    message += f"🛡️ **نشاط الحماية اليوم:**\n"
                    message += f"• محاولات وصول محظورة: {blocked_today}\n"
                    message += f"• حالة النظام: {'🟢 آمن' if blocked_today < 10 else '🟡 مراقبة' if blocked_today < 50 else '🔴 تحذير'}\n\n"

                message += f"📅 **تاريخ التقرير:** {datetime.now().strftime('%Y-%m-%d %H:%M')}"

                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("🔙 الأمان", callback_data="admin_security"))

                bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            except Exception as e:
                logger.error(f"Failed to generate security report: {e}")
                bot.send_message(user_id, "❌ حدث خطأ في إنشاء تقرير الأمان")

        elif call.data == "admin_search_banned":
            # البحث في قائمة الحظر
            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 الأمان", callback_data="admin_security"))

            bot.send_message(user_id,
                "🔍 **البحث في قائمة الحظر**\n\n"
                "📝 أرسل معرف المستخدم للبحث عنه\n"
                "💡 مثال: 123456789",
                reply_markup=markup, parse_mode='Markdown')

            user_states[user_id] = {'waiting_for_search_banned_id': True}

        elif call.data.startswith("admin_blacklist_"):
            page = int(call.data.split("_")[-1])
            admin_panel.show_blacklist_menu(user_id, page)

        elif call.data == "admin_ban_user":
            # طلب معرف المستخدم للحظر
            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 الأمان", callback_data="admin_security"))

            bot.send_message(user_id,
                "➕ **حظر مستخدم جديد**\n\n"
                "📝 أرسل معرف المستخدم (User ID) الذي تريد حظره\n"
                "💡 مثال: 123456789\n\n"
                "⚠️ تأكد من صحة المعرف قبل الإرسال",
                reply_markup=markup, parse_mode='Markdown')

            # تعيين حالة انتظار معرف المستخدم
            user_states[user_id] = {'waiting_for_ban_user_id': True}

        elif call.data.startswith("admin_unban_"):
            target_user_id = int(call.data.split("_")[-1])
            admin_panel.unban_user(user_id, target_user_id)

        # معالجات أسباب الحظر
        elif call.data.startswith("ban_reason_"):
            parts = call.data.split("_")
            reason_type = parts[2]
            target_user_id = int(parts[3])

            if reason_type == "violation":
                admin_panel.ban_user(user_id, target_user_id, "مخالفة قوانين البوت والجروب", "permanent")
            elif reason_type == "fake":
                admin_panel.ban_user(user_id, target_user_id, "إرسال اشتراكات وهمية أو محاولة خداع النظام", "permanent")
            elif reason_type == "suspicious":
                admin_panel.ban_user(user_id, target_user_id, "سلوك مشبوه أو نشاط غير طبيعي", "permanent")
            elif reason_type == "custom":
                # طلب سبب مخصص
                markup = types.InlineKeyboardMarkup()
                markup.add(types.InlineKeyboardButton("❌ إلغاء", callback_data="admin_security"))

                bot.send_message(user_id,
                    f"📝 **سبب حظر مخصص للمستخدم {target_user_id}**\n\n"
                    f"اكتب السبب المخصص للحظر:",
                    reply_markup=markup, parse_mode='Markdown')

                user_states[user_id] = {'waiting_for_custom_ban_reason': True, 'target_user_id': target_user_id}

        # معالجات نظام الدعم
        elif call.data == "admin_support":
            admin_panel.show_support_menu(user_id)

        elif call.data == "admin_support_queue":
            admin_panel.show_support_queue(user_id)

        elif call.data.startswith("start_support_"):
            queue_id = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, "🔄 جاري بدء جلسة الدعم...")
            admin_panel.start_support_session_admin(user_id, queue_id)

        elif call.data.startswith("end_support_"):
            session_id = int(call.data.split("_")[-1])
            admin_panel.end_support_session_admin(user_id, session_id)

        elif call.data.startswith("skip_support_"):
            queue_id = int(call.data.split("_")[-1])
            bot.answer_callback_query(call.id, "⏭️ جاري تخطي المستخدم...")
            admin_panel.skip_support_user(user_id, queue_id)

        elif call.data == "admin_set_available":
            bot.answer_callback_query(call.id, "🟢 جاري تغيير الحالة لمتصل...")
            admin_panel.set_admin_support_status(user_id, 'available')

        elif call.data == "admin_set_offline":
            bot.answer_callback_query(call.id, "⚫ جاري تغيير الحالة لغير متصل...")
            admin_panel.set_admin_support_status(user_id, 'offline')

        elif call.data == "admin_support_stats":
            bot.answer_callback_query(call.id, "📊 جاري تحميل إحصائيات الدعم...")
            admin_panel.show_support_statistics(user_id)

        # معالجات أزرار إشعارات الدعم الجديدة
        elif call.data == "wait_for_reconnect":
            bot.answer_callback_query(call.id, "⏳ سيتم إشعارك عند عودة فريق الدعم")
            bot.send_message(user_id,
                "⏳ **تم تسجيل انتظارك**\n\n"
                "🔔 ستصلك رسالة فور عودة فريق الدعم\n"
                "💡 يمكنك استخدام البوت بشكل طبيعي أثناء الانتظار")

        elif call.data == "end_session_user":
            # إنهاء الجلسة من قبل المستخدم
            try:
                from support_system import SupportSystem
                support_system = SupportSystem(bot)

                # إنهاء الجلسة
                result = support_system.cancel_support_request(user_id)

                if result['success']:
                    bot.answer_callback_query(call.id, "✅ تم إنهاء الجلسة")
                    bot.send_message(user_id,
                        "✅ **تم إنهاء جلسة الدعم**\n\n"
                        "🔄 يمكنك طلب الدعم مرة أخرى في أي وقت\n"
                        "📞 نحن هنا لمساعدتك دائماً")
                else:
                    bot.answer_callback_query(call.id, "❌ حدث خطأ في إنهاء الجلسة")

            except Exception as e:
                logger.error(f"Failed to end session for user {user_id}: {e}")
                bot.answer_callback_query(call.id, "❌ حدث خطأ في إنهاء الجلسة")

        elif call.data == "admin_recalc_stats":
            admin_panel.recalculate_statistics(user_id)
        elif call.data == "admin_restart_system":
            bot.send_message(user_id, "🔄 إعادة تشغيل النظام\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_test_notifications":
            admin_panel.test_notifications(user_id)
        elif call.data == "admin_system_check":
            admin_panel.system_health_check(user_id)
        elif call.data == "admin_check_group":
            admin_panel.check_group_settings(user_id)
        elif call.data == "admin_fix_group":
            admin_panel.fix_group_settings(user_id)
        elif call.data == "admin_group_info":
            admin_panel.show_group_info(user_id)
        elif call.data == "admin_get_group_id":
            admin_panel.get_group_id_helper(user_id)
        elif call.data == "admin_update_group_link":
            admin_panel.update_group_link_helper(user_id)
        elif call.data == "admin_fix_join_dates":
            admin_panel.fix_join_dates(user_id)
        elif call.data == "admin_update_data":
            admin_panel.update_subscriber_data(user_id)
        # معالجات الإعدادات
        elif call.data == "admin_subscription_settings":
            bot.send_message(user_id, "💰 إعدادات الاشتراك\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_payment_settings":
            bot.send_message(user_id, "💳 إعدادات الدفع\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_notification_settings":
            bot.send_message(user_id, "🔔 إعدادات التنبيهات\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_group_settings":
            bot.send_message(user_id, "👥 إعدادات الجروب\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_report_settings":
            bot.send_message(user_id, "📊 إعدادات التقارير\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_security_settings":
            bot.send_message(user_id, "🛡️ إعدادات الأمان\n\n🚧 هذه الوظيفة قيد التطوير")
        # معالجات الرسائل الجماعية
        elif call.data == "admin_broadcast_all":
            bot.send_message(user_id, "📢 رسالة لجميع المشتركين\n\nأرسل الرسالة التي تريد إرسالها لجميع المشتركين:")
            user_states[user_id] = {'waiting_for_broadcast_all': True}
        elif call.data == "admin_broadcast_active":
            bot.send_message(user_id, "✅ رسالة للمشتركين النشطين\n\nأرسل الرسالة التي تريد إرسالها للمشتركين النشطين فقط:")
            user_states[user_id] = {'waiting_for_broadcast_active': True}
        elif call.data == "admin_broadcast_expiring":
            bot.send_message(user_id, "⚠️ رسالة للمنتهية قريباً\n\nأرسل الرسالة التي تريد إرسالها للمشتركين منتهية الصلاحية قريباً:")
            user_states[user_id] = {'waiting_for_broadcast_expiring': True}
        elif call.data == "admin_broadcast_expired":
            bot.send_message(user_id, "❌ رسالة للمنتهية الصلاحية\n\n🚧 هذه الوظيفة قيد التطوير")

        # معالجات التقارير
        elif call.data == "admin_reports_menu":
            admin_panel.show_reports_menu(user_id)
        elif call.data == "admin_payments_report":
            admin_panel.show_payments_report(user_id)
        elif call.data == "admin_subscribers_report":
            admin_panel.show_subscribers_report(user_id)
        elif call.data == "admin_revenue_report":
            bot.send_message(user_id, "📊 تقرير الإيرادات\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_growth_report":
            bot.send_message(user_id, "📈 تقرير النمو\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_daily_report":
            bot.send_message(user_id, "📅 التقرير اليومي\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_monthly_report":
            bot.send_message(user_id, "📆 التقرير الشهري\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_issues_report":
            bot.send_message(user_id, "⚠️ تقرير المشاكل\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_activity_report":
            bot.send_message(user_id, "🔄 تقرير النشاط\n\n🚧 هذه الوظيفة قيد التطوير")
        elif call.data == "admin_broadcast_stats":
            admin_panel.get_broadcast_statistics(user_id)
        elif call.data == "admin_test_broadcast":
            admin_panel.test_broadcast_system(user_id)
        elif call.data == "admin_send_test_message":
            admin_panel.send_test_broadcast_message(user_id)
        elif call.data == "admin_refresh":
            admin_panel.show_admin_menu(user_id)
        elif call.data == "current_page":
            bot.answer_callback_query(call.id, "📄 الصفحة الحالية")
        else:
            bot.send_message(user_id, "🚧 هذه الوظيفة قيد التطوير")

    except Exception as e:
        logger.error(f"Failed to handle admin callback {call.data}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في تنفيذ العملية")

# ===== معالجات عامة =====

@bot.callback_query_handler(func=lambda call: call.data == "auto_start")
def handle_auto_start(call):
    """معالج زر ابدأ الآن للمستخدمين الجدد"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # حذف الرسالة السابقة
        try:
            bot.delete_message(user_id, call.message.message_id)
        except:
            pass

        # إرسال القائمة الرئيسية مباشرة
        class TempMessage:
            def __init__(self, user):
                self.from_user = user

        temp_message = TempMessage(call.from_user)
        handle_start(temp_message)

    except Exception as e:
        logger.error(f"Failed to handle auto_start for user {user_id}: {e}")

@bot.callback_query_handler(func=lambda call: call.data == "main_menu")
def handle_main_menu(call):
    """العودة للقائمة الرئيسية"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # حذف الرسالة الحالية
        try:
            bot.delete_message(user_id, call.message.message_id)
        except:
            pass

        # إرسال القائمة الرئيسية
        # إنشاء message object مؤقت للتوافق مع handle_start
        class TempMessage:
            def __init__(self, user):
                self.from_user = user

        temp_message = TempMessage(call.from_user)
        handle_start(temp_message)

    except Exception as e:
        logger.error(f"Failed to show main menu for user {user_id}: {e}")

@bot.callback_query_handler(func=lambda call: call.data == "back_to_main")
def handle_back_to_main(call):
    """العودة للقائمة الرئيسية - معالج إضافي"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # حذف الرسالة الحالية
        try:
            bot.delete_message(user_id, call.message.message_id)
        except:
            pass

        # إرسال القائمة الرئيسية مباشرة
        subscriber = db_manager.get_subscriber(user_id)
        is_active = subscriber and subscriber.get('is_active', False)

        # إنشاء لوحة التحكم الرئيسية المخصصة
        markup = types.InlineKeyboardMarkup(row_width=2)

        if is_active:
            # قائمة للمشتركين النشطين
            markup.add(
                types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status"),
                types.InlineKeyboardButton("💳 تجديد الاشتراك", callback_data="payment_methods")
            )
            markup.add(
                types.InlineKeyboardButton("🔔 إعدادات التنبيهات", callback_data="notification_settings"),
                types.InlineKeyboardButton("📞 التواصل مع الدعم", callback_data="contact_support")
            )
            markup.add(
                types.InlineKeyboardButton("📋 تاريخ المدفوعات", callback_data="payment_history"),
                types.InlineKeyboardButton("❓ مساعدة", callback_data="help")
            )

            welcome_message = f"🎉 مرحباً بك في بوت الاشتراكات!\n\n" \
                            f"✅ اشتراكك نشط ومُفعل\n" \
                            f"🎯 اختر ما تريد فعله:"
        else:
            # قائمة للمشتركين غير النشطين أو الجدد
            markup.add(
                types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"),
                types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status")
            )
            markup.add(
                types.InlineKeyboardButton("❓ مساعدة", callback_data="help")
            )

            welcome_message = f"👋 مرحباً بك في بوت الاشتراكات!\n\n" \
                            f"🚀 للحصول على العضوية المميزة\n" \
                            f"💎 اختر خطة الاشتراك المناسبة لك:"

        bot.send_message(user_id, welcome_message, reply_markup=markup)

    except Exception as e:
        logger.error(f"Failed to show main menu for user {user_id}: {e}")
        # في حالة الخطأ، إرسال قائمة بسيطة
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("💳 اشترك الآن", callback_data="payment_methods"))
        bot.send_message(user_id, "👋 مرحباً! اختر ما تريد فعله:", reply_markup=markup)

# ===== معالجات الوظائف المفقودة =====

@bot.callback_query_handler(func=lambda call: call.data == "update_profile")
def handle_update_profile(call):
    """معالج تحديث البيانات"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        # الحصول على معلومات المستخدم الحالية من تليجرام
        try:
            user_info = bot.get_chat(user_id)
            new_username = user_info.username
            new_first_name = user_info.first_name

            # تحديث البيانات في قاعدة البيانات
            with db_manager.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {db_manager.subscribers_table}
                    SET username = %s, first_name = %s
                    WHERE user_id = %s
                """, (new_username, new_first_name, user_id))

            message = f"✅ تم تحديث بياناتك بنجاح!\n\n" \
                     f"👤 الاسم الجديد: {new_first_name}\n" \
                     f"📝 اسم المستخدم: @{new_username or 'غير محدد'}"

        except Exception as e:
            logger.error(f"Failed to update profile for user {user_id}: {e}")
            message = "❌ حدث خطأ في تحديث البيانات"

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 إعدادات الحساب", callback_data="account_settings"))

        bot.send_message(user_id, message, reply_markup=markup)

        # تسجيل النشاط
        db_manager.log_activity(user_id, "profile_updated")

    except Exception as e:
        logger.error(f"Failed to handle update profile for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في تحديث البيانات")



@bot.callback_query_handler(func=lambda call: call.data == "detailed_statistics")
def handle_detailed_statistics(call):
    """معالج تفاصيل الإحصائيات"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        subscriber = db_manager.get_subscriber(user_id)

        if not subscriber:
            bot.send_message(user_id, "❌ لم يتم العثور على بيانات حسابك")
            return

        # حساب إحصائيات مفصلة
        join_date = datetime.fromtimestamp(subscriber['join_time']) if subscriber.get('join_time') else datetime.now()
        days_since_join = (datetime.now() - join_date).days

        # حساب معدل التجديد
        renewal_rate = (subscriber.get('subscription_count', 0) / max(1, days_since_join / 30)) * 100

        # حساب متوسط الدفع الشهري
        total_paid = float(subscriber.get('total_paid', 0))
        monthly_avg = total_paid / max(1, days_since_join / 30)

        # حساب إجمالي المدفوعات
        # تم حذف نظام الوفورات والمكافآت

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("📊 إحصائياتي", callback_data="my_statistics"),
            types.InlineKeyboardButton("🔙 إعدادات الحساب", callback_data="account_settings")
        )

        message = f"📊 إحصائياتك التفصيلية\n\n" \
                 f"📅 **تاريخ الانضمام:** {join_date.strftime('%Y-%m-%d')}\n" \
                 f"⏰ **أيام العضوية:** {days_since_join} يوم\n" \
                 f"🔄 **عدد التجديدات:** {subscriber.get('subscription_count', 0)}\n" \
                 f"📈 **معدل التجديد:** {renewal_rate:.1f}%\n\n" \
                 f"💰 **الإحصائيات المالية:**\n" \
                 f"• إجمالي المدفوعات: {total_paid:.2f} جنيه\n" \
                 f"• متوسط الدفع الشهري: {monthly_avg:.2f} جنيه\n\n" \
                 f"🏆 **مستوى العضوية:**\n"

        # تحديد مستوى العضوية
        subscription_count = subscriber.get('subscription_count', 0)
        if subscription_count >= 12:
            message += "👑 عضو ذهبي (12+ تجديد)\n"
        elif subscription_count >= 6:
            message += "💎 عضو فضي (6+ تجديد)\n"
        elif subscription_count >= 3:
            message += "🥉 عضو برونزي (3+ تجديد)\n"
        else:
            message += "🆕 عضو جديد\n"

        message += f"\n⭐ **نقاط الولاء:** {subscription_count * 10} نقطة"

        bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

        # تسجيل النشاط
        db_manager.log_activity(user_id, "detailed_statistics_viewed")

    except Exception as e:
        logger.error(f"Failed to show detailed statistics for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض الإحصائيات التفصيلية")


@bot.callback_query_handler(func=lambda call: call.data == "payment_history")
def handle_payment_history(call):
    """عرض تاريخ المدفوعات"""
    user_id = call.from_user.id

    try:
        bot.answer_callback_query(call.id)

        history = payment_handler.get_payment_status(user_id)

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))

        bot.send_message(user_id, history, reply_markup=markup, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"Failed to show payment history for user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في جلب تاريخ المدفوعات")

# معالج الرسائل النصية للإدارة
@bot.message_handler(func=lambda message: message.from_user.id == config.ADMIN_ID and message.text and not message.text.startswith('/'))
def handle_admin_text_message(message):
    """معالج الرسائل النصية للإدارة"""
    user_id = message.from_user.id
    text = message.text.strip()

    try:
        # التحقق من حالة الإدارة
        admin_state = user_states.get(user_id, {})

        if admin_state.get('waiting_for_search_query'):
            # البحث عن مشترك
            admin_panel.search_subscriber(user_id, text)
            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_add_subscriber'):
            # إضافة مشترك
            try:
                target_user_id = int(text)
                admin_panel.add_subscriber_manually(user_id, target_user_id)
            except ValueError:
                bot.send_message(user_id, "❌ يجب أن يكون معرف المستخدم رقماً صحيحاً")
            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_remove_subscriber'):
            # حذف مشترك
            try:
                target_user_id = int(text)
                admin_panel.remove_subscriber_manually(user_id, target_user_id)
            except ValueError:
                bot.send_message(user_id, "❌ يجب أن يكون معرف المستخدم رقماً صحيحاً")
            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_broadcast_all'):
            # رسالة جماعية لجميع المشتركين
            admin_panel.broadcast_to_all_subscribers(user_id, text)
            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_broadcast_active'):
            # رسالة جماعية للمشتركين النشطين
            admin_panel.broadcast_to_active_subscribers(user_id, text)
            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_broadcast_expiring'):
            # رسالة جماعية للمنتهية قريباً
            admin_panel.broadcast_to_expiring_subscribers(user_id, text)
            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_ban_user_id'):
            # حظر مستخدم
            try:
                target_user_id = int(text.strip())

                # طلب سبب الحظر
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("🚫 مخالفة القوانين", callback_data=f"ban_reason_violation_{target_user_id}"),
                    types.InlineKeyboardButton("🔄 اشتراكات وهمية", callback_data=f"ban_reason_fake_{target_user_id}")
                )
                markup.add(
                    types.InlineKeyboardButton("🤖 سلوك مشبوه", callback_data=f"ban_reason_suspicious_{target_user_id}"),
                    types.InlineKeyboardButton("📝 سبب مخصص", callback_data=f"ban_reason_custom_{target_user_id}")
                )
                markup.add(types.InlineKeyboardButton("❌ إلغاء", callback_data="admin_security"))

                bot.send_message(user_id,
                    f"🚫 **حظر المستخدم {target_user_id}**\n\n"
                    f"📝 اختر سبب الحظر:",
                    reply_markup=markup, parse_mode='Markdown')

                # تحديث الحالة
                user_states[user_id] = {'waiting_for_ban_reason': True, 'target_user_id': target_user_id}

            except ValueError:
                bot.send_message(user_id, "❌ معرف المستخدم غير صحيح\n\n"
                                        "💡 يجب أن يكون رقماً صحيحاً\n"
                                        "مثال: 123456789")
            return
        elif admin_state.get('waiting_for_custom_ban_reason'):
            # سبب حظر مخصص
            target_user_id = admin_state.get('target_user_id')
            if target_user_id:
                admin_panel.ban_user(user_id, target_user_id, reason=text, ban_type="permanent")
                user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_user_search'):
            # البحث عن مستخدم في قائمة الأمان
            try:
                search_user_id = int(text.strip())
                admin_panel.show_user_security_details(user_id, search_user_id)
                user_states.pop(user_id, None)
                return
            except ValueError:
                bot.send_message(user_id, "❌ يرجى إدخال معرف مستخدم صحيح (أرقام فقط)")
                return
        elif admin_state.get('waiting_for_quick_ban_id'):
            # حظر سريع
            try:
                ban_user_id = int(text.strip())
                bot.send_message(user_id, f"🚫 حظر المستخدم {ban_user_id}\n\nأرسل سبب الحظر:")
                user_states[user_id] = {'waiting_for_ban_reason': True, 'target_user_id': ban_user_id}
                return
            except ValueError:
                bot.send_message(user_id, "❌ يرجى إدخال معرف مستخدم صحيح (أرقام فقط)")
                return
        elif admin_state.get('waiting_for_ban_reason'):
            # سبب الحظر للحظر السريع
            target_user_id = admin_state.get('target_user_id')
            if target_user_id:
                admin_panel.ban_user(user_id, target_user_id, reason=text, ban_type="permanent")
                user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_subscriber_ban_reason'):
            # حظر مشترك مع سبب
            target_user_id = admin_state.get('target_user_id')
            if target_user_id:
                # حظر المستخدم أولاً
                admin_panel.ban_user(user_id, target_user_id, reason=text, ban_type="permanent")
                # ثم حذفه من المشتركين
                admin_panel.delete_subscriber(user_id, target_user_id)
                user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_extension_days'):
            # تمديد اشتراك مشترك
            target_user_id = admin_state.get('target_user_id')
            try:
                days = int(text.strip())
                if days <= 0:
                    bot.send_message(user_id, "❌ يرجى إدخال عدد أيام صحيح (أكبر من صفر)")
                    return

                # تمديد الاشتراك
                admin_panel.extend_subscription(user_id, target_user_id, days)
                user_states.pop(user_id, None)
                return
            except ValueError:
                bot.send_message(user_id, "❌ يرجى إدخال عدد صحيح للأيام")
                return
        elif admin_state.get('waiting_for_add_days'):
            # زيادة أيام لاشتراك مشترك
            target_user_id = admin_state.get('target_user_id')
            try:
                days = int(text.strip())
                if days <= 0:
                    bot.send_message(user_id, "❌ يرجى إدخال عدد أيام صحيح (أكبر من صفر)")
                    return

                # زيادة الأيام (نفس وظيفة التمديد)
                admin_panel.extend_subscription(user_id, target_user_id, days)
                user_states.pop(user_id, None)
                return
            except ValueError:
                bot.send_message(user_id, "❌ يرجى إدخال عدد صحيح للأيام")
                return
        elif admin_state.get('waiting_for_reduce_days'):
            # تقليل أيام من اشتراك مشترك
            target_user_id = admin_state.get('target_user_id')
            try:
                days = int(text.strip())
                if days <= 0:
                    bot.send_message(user_id, "❌ يرجى إدخال عدد أيام صحيح (أكبر من صفر)")
                    return

                # تقليل الأيام
                admin_panel.reduce_subscription_days(user_id, target_user_id, days)
                user_states.pop(user_id, None)
                return
            except ValueError:
                bot.send_message(user_id, "❌ يرجى إدخال عدد صحيح للأيام")
                return
        elif admin_state.get('waiting_for_file_name'):
            # إضافة ملف جديد - الخطوة 1: اسم الملف
            file_name = text.strip()
            if len(file_name) < 3:
                bot.send_message(user_id, "❌ اسم الملف قصير جداً. يرجى إدخال اسم أطول (3 أحرف على الأقل)")
                return

            user_states[user_id] = {
                'waiting_for_file_path': True,
                'file_name': file_name
            }

            bot.send_message(user_id,
                f"✅ **اسم الملف:** {file_name}\n\n"
                "📂 **الآن أرسل مسار الملف:**\n"
                "مثال: /downloads/pubg_mobile_v31_portable.zip\n"
                "أو: https://example.com/files/pubg_mobile.zip",
                parse_mode='Markdown'
            )
            return
        elif admin_state.get('waiting_for_file_path'):
            # إضافة ملف جديد - الخطوة 2: مسار الملف
            file_path = text.strip()
            if len(file_path) < 5:
                bot.send_message(user_id, "❌ مسار الملف قصير جداً. يرجى إدخال مسار صحيح")
                return

            user_states[user_id].update({
                'waiting_for_file_size': True,
                'file_path': file_path
            })

            bot.send_message(user_id,
                f"✅ **مسار الملف:** {file_path}\n\n"
                "📊 **الآن أرسل حجم الملف بالميجابايت:**\n"
                "مثال: 2048 (للملفات 2 جيجا)\n"
                "مثال: 500 (للملفات 500 ميجا)",
                parse_mode='Markdown'
            )
            return
        elif admin_state.get('waiting_for_file_size'):
            # إضافة ملف جديد - الخطوة 3: حجم الملف
            try:
                size_mb = float(text.strip())
                if size_mb <= 0:
                    bot.send_message(user_id, "❌ يرجى إدخال حجم صحيح (أكبر من صفر)")
                    return

                size_bytes = int(size_mb * 1024 * 1024)  # تحويل من ميجا إلى بايت

                user_states[user_id].update({
                    'waiting_for_file_category': True,
                    'file_size': size_bytes
                })

                # عرض الفئات المتاحة
                markup = types.InlineKeyboardMarkup(row_width=2)
                markup.add(
                    types.InlineKeyboardButton("🎮 لعبة", callback_data="file_category_game"),
                    types.InlineKeyboardButton("🔧 أدوات", callback_data="file_category_tools")
                )
                markup.add(
                    types.InlineKeyboardButton("⚙️ مساعدة", callback_data="file_category_utility"),
                    types.InlineKeyboardButton("📦 عام", callback_data="file_category_general")
                )

                bot.send_message(user_id,
                    f"✅ **حجم الملف:** {size_mb} ميجا\n\n"
                    "📂 **اختر فئة الملف:**",
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                return
            except ValueError:
                bot.send_message(user_id, "❌ يرجى إدخال رقم صحيح لحجم الملف")
                return
        elif admin_state.get('waiting_for_file_description'):
            # إضافة ملف جديد - الخطوة 5: وصف الملف
            description = text.strip()

            # إضافة الملف إلى قاعدة البيانات
            file_data = user_states[user_id]
            success = admin_panel.add_new_file(
                user_id,
                file_data['file_name'],
                file_data['file_path'],
                file_data['file_size'],
                file_data['file_category'],
                description
            )

            user_states.pop(user_id, None)
            return
        elif admin_state.get('waiting_for_search_banned_id'):
            # البحث في قائمة الحظر
            try:
                search_user_id = int(text.strip())
                ban_info = db_manager.is_user_blacklisted(search_user_id)

                if ban_info:
                    ban_date = ban_info['banned_at'].strftime('%Y-%m-%d %H:%M') if ban_info.get('banned_at') else 'غير محدد'

                    message = f"🔍 **نتيجة البحث**\n\n"
                    message += f"🆔 **المعرف:** {search_user_id}\n"
                    message += f"🚫 **الحالة:** محظور\n"
                    message += f"📝 **السبب:** {ban_info['reason']}\n"
                    message += f"📅 **تاريخ الحظر:** {ban_date}\n"
                    message += f"🔒 **نوع الحظر:** {ban_info['ban_type']}\n"

                    if ban_info.get('notes'):
                        message += f"📋 **ملاحظات:** {ban_info['notes']}\n"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(
                        types.InlineKeyboardButton(f"🔓 إلغاء الحظر", callback_data=f"admin_unban_{search_user_id}"),
                        types.InlineKeyboardButton("🔙 الأمان", callback_data="admin_security")
                    )

                    bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')
                else:
                    message = f"✅ **نتيجة البحث**\n\n"
                    message += f"🆔 **المعرف:** {search_user_id}\n"
                    message += f"🟢 **الحالة:** غير محظور\n\n"
                    message += f"💡 هذا المستخدم غير موجود في قائمة الحظر"

                    markup = types.InlineKeyboardMarkup()
                    markup.add(
                        types.InlineKeyboardButton("🚫 حظر هذا المستخدم", callback_data="admin_ban_user"),
                        types.InlineKeyboardButton("🔙 الأمان", callback_data="admin_security")
                    )

                    bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

                user_states.pop(user_id, None)

            except ValueError:
                bot.send_message(user_id, "❌ معرف المستخدم غير صحيح\n\n"
                                        "💡 يجب أن يكون رقماً صحيحاً\n"
                                        "مثال: 123456789")
            return

        else:
            # رسالة عادية للإدارة
            bot.send_message(user_id, "👋 مرحباً بك في لوحة الإدارة!\n\nاستخدم /admin للوصول للوحة التحكم")

    except Exception as e:
        logger.error(f"Error handling admin text message from user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في معالجة رسالتك")

# معالج الرسائل النصية للمستخدمين العاديين
@bot.message_handler(func=lambda message: message.from_user.id != config.ADMIN_ID and message.text and not message.text.startswith('/'))
def handle_user_text_message(message):
    """معالج الرسائل النصية للمستخدمين العاديين مع فلترة أمنية"""
    user_id = message.from_user.id
    text = message.text.strip()

    try:
        # فحص أمني أولي
        is_allowed, block_info = security_filter.check_user_access(user_id, "text_message")

        if not is_allowed:
            security_filter.send_blocked_message(user_id, block_info)
            return
        # التحقق من حالة المستخدم
        user_state = user_states.get(user_id, {})

        if user_state.get('waiting_for_payment_proof'):
            # المستخدم يرسل إثبات دفع
            payment_handler.handle_payment_proof(message)
            user_states.pop(user_id, None)  # إزالة الحالة
            return
        elif user_state.get('waiting_for_admin_message'):
            # المستخدم يرسل رسالة للإدارة
            try:
                username = message.from_user.username or 'غير محدد'
                first_name = message.from_user.first_name or 'غير محدد'

                # إرسال الرسالة للإدارة
                admin_message = f"📧 رسالة من مستخدم\n\n" \
                              f"👤 من: {first_name} (@{username})\n" \
                              f"🆔 معرف المستخدم: {user_id}\n" \
                              f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n" \
                              f"📝 الرسالة:\n{text}"

                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("💬 رد على المستخدم", callback_data=f"reply_to_user_{user_id}"),
                    types.InlineKeyboardButton("👤 معلومات المستخدم", callback_data=f"user_info_{user_id}")
                )

                bot.send_message(config.ADMIN_ID, admin_message, reply_markup=markup)

                # تأكيد للمستخدم
                bot.send_message(user_id,
                    "✅ تم إرسال رسالتك للإدارة بنجاح!\n\n"
                    "📞 سيتم الرد عليك في أقرب وقت ممكن\n"
                    "⏰ متوسط وقت الرد: أقل من 30 دقيقة")

                user_states.pop(user_id, None)

                # تسجيل النشاط
                db_manager.log_activity(user_id, "message_sent_to_admin", f"Message: {text[:50]}...")

            except Exception as e:
                logger.error(f"Failed to send user message to admin: {e}")
                bot.send_message(user_id, "❌ حدث خطأ في إرسال الرسالة. حاول مرة أخرى.")
            return


        # رسائل عادية
        if text.lower() in ['مرحبا', 'السلام عليكم', 'هلا', 'اهلا']:
            bot.send_message(user_id, "👋 أهلاً وسهلاً بك!\n\nاستخدم /start للبدء")
        else:
            # استدعاء معالج الرسائل الأولى للمستخدمين الجدد
            handle_first_message(message)

    except Exception as e:
        logger.error(f"Error handling user text message from user {user_id}: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في معالجة رسالتك")

# معالج الرسائل الأولى للمستخدمين الجدد
# تم تعطيل هذا المعالج لأنه سيتم استدعاؤه من معالج المستخدمين
def handle_first_message(message):
    """معالج الرسائل للمستخدمين الجدد - ترحيب تلقائي"""
    user_id = message.from_user.id

    if not rate_limiter.is_allowed(user_id):
        return

    # التحقق إذا كان المستخدم جديد (لم يتفاعل مع البوت من قبل)
    try:
        # فحص إذا كان المستخدم موجود في سجلات النشاط
        with db_manager.get_db_cursor() as cursor:
            cursor.execute(f"""
                SELECT COUNT(*) as count FROM {db_manager.activity_logs_table}
                WHERE user_id = %s
            """, (user_id,))

            result = cursor.fetchone()
            is_new_user = result['count'] == 0

        if is_new_user:
            # مستخدم جديد - إرسال ترحيب تلقائي
            welcome_message = "👋 أهلاً وسهلاً بك!\n\n" \
                            "🤖 أنا بوت الاشتراكات المتقدم\n" \
                            "✨ سأساعدك في إدارة اشتراكك بسهولة\n\n" \
                            "🚀 لنبدأ معاً!"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🚀 ابدأ الآن", callback_data="auto_start"))

            bot.send_message(user_id, welcome_message, reply_markup=markup)

            # تسجيل النشاط
            db_manager.log_activity(user_id, "first_interaction", f"First message: {message.text[:50]}")
        else:
            # مستخدم قديم - رسالة مساعدة
            help_message = "❓ لم أفهم طلبك. استخدم الأزرار أدناه للتنقل:\n\n" \
                          "💡 يمكنك استخدام الأوامر التالية:\n" \
                          "• /start - القائمة الرئيسية"

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="main_menu"),
                types.InlineKeyboardButton("💳 دفع الاشتراك", callback_data="payment_methods")
            )
            markup.add(
                types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status"),
                types.InlineKeyboardButton("❓ مساعدة", callback_data="help")
            )

            bot.send_message(user_id, help_message, reply_markup=markup)

    except Exception as e:
        logger.error(f"Error in handle_first_message for user {user_id}: {e}")
        # في حالة الخطأ، إرسال رسالة بسيطة
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🚀 ابدأ الآن", callback_data="auto_start"))
        bot.send_message(user_id, "👋 مرحباً! اضغط الزر أدناه للبدء:", reply_markup=markup)

# ===== إعداد إيقاف النظام بأمان =====

def signal_handler(signum, frame):
    """معالج إيقاف النظام بأمان"""
    # تجاهل المتغيرات غير المستخدمة
    _ = signum, frame
    logger.info("Received shutdown signal, cleaning up...")

    try:
        # إيقاف نظام التنبيهات
        notification_manager.shutdown()

        # إنشاء نسخة احتياطية أخيرة
        from utils import BackupManager
        BackupManager.create_database_backup()

        logger.info("Cleanup completed, shutting down...")
        sys.exit(0)

    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
        sys.exit(1)

# تسجيل معالج الإيقاف
import signal
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# ===== معالجات أزرار الدعم للإدارة =====
# تم حذف المعالج المكرر - يتم استخدام المعالج في admin_panel.py

# تم حذف المعالج المكرر لبدء الدعم

@bot.callback_query_handler(func=lambda call: call.data == "admin_support_queue")
def handle_admin_support_queue(call):
    """معالج عرض طابور الدعم للإدارة"""
    user_id = call.from_user.id

    # التحقق من أن المستخدم هو الأدمن
    if user_id != config.ADMIN_ID:
        bot.answer_callback_query(call.id, "❌ ليس لديك صلاحية لهذا الإجراء", show_alert=True)
        return

    try:
        bot.answer_callback_query(call.id)

        from support_system import SupportSystem
        support_system = SupportSystem(bot)

        # الحصول على إحصائيات الطابور
        stats = support_system.get_support_stats()

        if stats['success']:
            message = f"📊 **حالة طابور الدعم**\n\n" \
                     f"⏳ **في الانتظار:** {stats['waiting_count']}\n" \
                     f"💬 **جلسات نشطة:** {stats['in_progress_count']}\n" \
                     f"✅ **مكتملة اليوم:** {stats['completed_today']}\n" \
                     f"🎯 **الحد الأقصى:** {stats['max_concurrent']}\n" \
                     f"🆓 **مقاعد متاحة:** {stats['available_slots']}\n\n"

            if stats['waiting_count'] > 0:
                message += "👤 **المستخدم التالي:**\n"
                next_user_result = support_system.get_next_user_for_support()
                if next_user_result['success']:
                    user_data = next_user_result['user_data']
                    message += f"• {user_data['first_name']} (@{user_data.get('username', 'غير محدد')})\n"
                    message += f"• المعرف: `{user_data['user_id']}`\n"
                    message += f"• الموضوع: {user_data.get('subject', 'طلب دعم')}"
            else:
                message += "📭 **لا يوجد مستخدمين في الانتظار**"
        else:
            message = "❌ فشل في الحصول على إحصائيات الطابور"

        markup = types.InlineKeyboardMarkup()
        if stats.get('success') and stats.get('waiting_count', 0) > 0:
            markup.add(types.InlineKeyboardButton("👤 بدء الدعم التالي", callback_data="admin_start_next_support"))
        markup.add(
            types.InlineKeyboardButton("🔄 تحديث", callback_data="admin_support_queue"),
            types.InlineKeyboardButton("🎛️ لوحة الإدارة", callback_data="admin_menu")
        )

        bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"Failed to show admin support queue: {e}")
        bot.send_message(user_id, "❌ حدث خطأ في عرض طابور الدعم")

# ===== بدء تشغيل النظام =====

if __name__ == "__main__":
    try:
        logger.info("Starting Advanced Subscription Bot System...")

        # التحقق من الإعدادات المطلوبة
        if not config.WEBHOOK_URL:
            raise Exception("WEBHOOK_URL must be set in environment variables!")

        # إعداد الـ webhook
        try:
            bot.remove_webhook()
            webhook_url = f"{config.WEBHOOK_URL}/{config.API_TOKEN}"
            bot.set_webhook(url=webhook_url)
            logger.info(f"Webhook set to: {webhook_url}")
        except Exception as e:
            logger.error(f"Failed to set webhook: {e}")
            raise

        # بدء تشغيل نظام التنبيهات
        try:
            notification_manager.start()
            logger.info("Notification system started successfully")
        except Exception as e:
            logger.error(f"Failed to start notification system: {e}")

        # إضافة مهمة دورية لتنظيف روابط الدعوة المنتهية الصلاحية
        def cleanup_expired_invite_links():
            """تنظيف روابط الدعوة المنتهية الصلاحية كل ساعة"""
            try:
                cleaned_count = invite_manager.cleanup_expired_links()
                if cleaned_count > 0:
                    logger.info(f"Cleaned up {cleaned_count} expired invite links")
            except Exception as e:
                logger.error(f"Failed to cleanup expired invite links: {e}")

        # إضافة مهمة دورية لتنظيف روابط الدعوة المنتهية الصلاحية
        try:
            import threading
            import time

            def cleanup_task():
                """مهمة تنظيف دورية تعمل كل ساعة"""
                while True:
                    try:
                        time.sleep(3600)  # انتظار ساعة واحدة
                        cleaned_count = invite_manager.cleanup_expired_links()
                        if cleaned_count > 0:
                            logger.info(f"Cleaned up {cleaned_count} expired invite links")
                    except Exception as e:
                        logger.error(f"Error in cleanup task: {e}")

            # بدء المهمة في خيط منفصل
            cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
            cleanup_thread.start()
            logger.info("Invite links cleanup task started")
        except Exception as e:
            logger.error(f"Failed to start invite links cleanup task: {e}")

        # بدء تشغيل الخادم
        port = config.PORT
        logger.info(f"Starting Flask server on port {port}")

        # رسالة بدء التشغيل للإدارة
        try:
            startup_message = f"🚀 تم بدء تشغيل النظام بنجاح!\n\n" \
                            f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" \
                            f"🌐 المنفذ: {port}\n" \
                            f"🔗 Webhook: {webhook_url}\n" \
                            f"📊 حالة قاعدة البيانات: متصلة\n" \
                            f"🔔 نظام التنبيهات: نشط\n" \
                            f"🔒 نظام الأمان المحسن: نشط\n" \
                            f"🔗 مدير روابط الدعوة الآمن: نشط\n" \
                            f"🧹 تنظيف تلقائي للروابط: كل ساعة\n\n" \
                            f"✅ النظام جاهز لاستقبال المستخدمين مع أمان محسن!"

            bot.send_message(config.ADMIN_ID, startup_message)
        except Exception as e:
            logger.warning(f"Failed to send startup message to admin: {e}")

        # تشغيل الخادم
        app.run(
            host="0.0.0.0",
            port=port,
            debug=config.DEBUG,
            use_reloader=False  # تجنب إعادة التحميل التلقائي في الإنتاج
        )

    except Exception as e:
        logger.error(f"Failed to start bot: {e}")

        # إرسال رسالة خطأ للإدارة
        try:
            error_message = f"❌ فشل في بدء تشغيل النظام!\n\n" \
                          f"🔴 الخطأ: {str(e)}\n" \
                          f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n" \
                          f"يرجى مراجعة السجلات وإعادة المحاولة."

            bot.send_message(config.ADMIN_ID, error_message)
        except:
            pass

        sys.exit(1)
