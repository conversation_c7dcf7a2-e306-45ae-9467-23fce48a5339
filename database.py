"""
إدارة قاعدة البيانات المتقدمة
Advanced Database Management
"""
import psycopg2  # type: ignore
import psycopg2.extras  # type: ignore
from contextlib import contextmanager
import logging
from datetime import datetime, timedelta
from config import Config

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.config = Config()
        self.prefix = self.config.TABLE_PREFIX

        # أسماء الجداول مع البادئة
        self.subscribers_table = f"{self.prefix}subscribers"
        self.payments_table = f"{self.prefix}payments"
        self.notifications_table = f"{self.prefix}notifications"
        self.statistics_table = f"{self.prefix}statistics"
        self.activity_logs_table = f"{self.prefix}activity_logs"
        self.invite_links_table = f"{self.prefix}invite_links"
        self.blacklist_table = f"{self.prefix}blacklist"
        self.support_queue_table = f"{self.prefix}support_queue"
        self.support_sessions_table = f"{self.prefix}support_sessions"
        self.support_status_table = f"{self.prefix}support_status"

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        return psycopg2.connect(
            host=self.config.DB_HOST,
            database=self.config.DB_NAME,
            user=self.config.DB_USER,
            password=self.config.DB_PASS,
            port=self.config.DB_PORT,
            cursor_factory=psycopg2.extras.RealDictCursor
        )

    @contextmanager
    def get_db_cursor(self):
        """Context manager لإدارة الاتصال والمؤشر"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            yield cursor
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def initialize_database(self):
        """إنشاء الجداول المطلوبة مع البادئة الجديدة"""
        try:
            with self.get_db_cursor() as cursor:
                # جدول المشتركين المحسن
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.subscribers_table} (
                    user_id BIGINT PRIMARY KEY,
                    username VARCHAR(255),
                    first_name VARCHAR(255),
                    last_name VARCHAR(255),
                    join_time BIGINT NOT NULL,
                    expire_time BIGINT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    subscription_count INTEGER DEFAULT 1,
                    total_paid DECIMAL(10,2) DEFAULT 0,
                    last_payment_date BIGINT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # جدول المدفوعات
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.payments_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_method VARCHAR(50),
                    transaction_id VARCHAR(255),
                    status VARCHAR(20) DEFAULT 'pending',
                    admin_id BIGINT,
                    payment_proof_message_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    approved_at TIMESTAMP,
                    notes TEXT,
                    FOREIGN KEY (user_id) REFERENCES {self.subscribers_table}(user_id) ON DELETE CASCADE
                )
                """)

                # جدول التنبيهات
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.notifications_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    notification_type VARCHAR(50) NOT NULL,
                    message TEXT NOT NULL,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_read BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (user_id) REFERENCES {self.subscribers_table}(user_id) ON DELETE CASCADE
                )
                """)

                # جدول الإحصائيات
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.statistics_table} (
                    id SERIAL PRIMARY KEY,
                    date DATE DEFAULT CURRENT_DATE,
                    total_subscribers INTEGER DEFAULT 0,
                    active_subscribers INTEGER DEFAULT 0,
                    new_subscribers INTEGER DEFAULT 0,
                    expired_subscribers INTEGER DEFAULT 0,
                    total_revenue DECIMAL(10,2) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # جدول السجلات
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.activity_logs_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT,
                    action VARCHAR(100) NOT NULL,
                    details TEXT,
                    ip_address INET,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # جدول روابط الدعوة البسيط
                self.invite_links_table = f"{self.prefix}invite_links"
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.invite_links_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    invite_link TEXT NOT NULL,
                    expire_time BIGINT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    revoked_at TIMESTAMP NULL,
                    FOREIGN KEY (user_id) REFERENCES {self.subscribers_table}(user_id) ON DELETE CASCADE
                )
                """)

                # جدول قائمة الحظر (القائمة السوداء)
                self.blacklist_table = f"{self.prefix}blacklist"
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.blacklist_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL UNIQUE,
                    username VARCHAR(255),
                    first_name VARCHAR(255),
                    reason TEXT NOT NULL,
                    banned_by BIGINT NOT NULL,
                    banned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    notes TEXT,
                    ban_type VARCHAR(50) DEFAULT 'permanent'
                )
                """)

                # جدول طابور الدعم المبسط
                self.support_queue_table = f"{self.prefix}support_queue"
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.support_queue_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    username VARCHAR(255),
                    first_name VARCHAR(255),
                    subject TEXT,
                    description TEXT,
                    status VARCHAR(20) DEFAULT 'waiting',
                    queue_position INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP NULL,
                    completed_at TIMESTAMP NULL,
                    FOREIGN KEY (user_id) REFERENCES {self.subscribers_table}(user_id) ON DELETE CASCADE
                )
                """)

                # جدول جلسات الدعم المبسط
                self.support_sessions_table = f"{self.prefix}support_sessions"
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.support_sessions_table} (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    admin_id BIGINT NOT NULL,
                    session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_end TIMESTAMP NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    messages_count INTEGER DEFAULT 0
                )
                """)

                # جدول حالة الدعم البسيط
                self.support_status_table = f"{self.prefix}support_status"
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.support_status_table} (
                    id SERIAL PRIMARY KEY,
                    admin_id BIGINT NOT NULL UNIQUE,
                    status VARCHAR(20) DEFAULT 'available',
                    current_user_id BIGINT NULL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # إنشاء الفهارس لتحسين الأداء
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}subscribers_expire_time ON {self.subscribers_table}(expire_time)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}subscribers_is_active ON {self.subscribers_table}(is_active)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}payments_user_id ON {self.payments_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}payments_status ON {self.payments_table}(status)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}notifications_user_id ON {self.notifications_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}activity_logs_user_id ON {self.activity_logs_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}invite_links_user_id ON {self.invite_links_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}invite_links_active ON {self.invite_links_table}(is_active)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}invite_links_expire ON {self.invite_links_table}(expire_time)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}blacklist_user_id ON {self.blacklist_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}blacklist_active ON {self.blacklist_table}(is_active)")

                # فهارس جداول الدعم
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}support_queue_user_id ON {self.support_queue_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}support_queue_status ON {self.support_queue_table}(status)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}support_sessions_user_id ON {self.support_sessions_table}(user_id)")
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{self.prefix}support_sessions_admin ON {self.support_sessions_table}(admin_id)")



                logger.info(f"Database initialized successfully with prefix: {self.prefix}")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    def add_subscriber(self, user_id, username=None, first_name=None, last_name=None, duration_days=None):
        """إضافة مشترك جديد أو تجديد اشتراك موجود"""
        if duration_days is None:
            duration_days = self.config.SUBSCRIPTION_DURATION_DAYS

        try:
            with self.get_db_cursor() as cursor:
                now = int(datetime.now().timestamp())
                expire_time = now + (duration_days * 24 * 3600)

                # التحقق من وجود المشترك
                cursor.execute(f"SELECT user_id, expire_time, subscription_count FROM {self.subscribers_table} WHERE user_id = %s", (user_id,))
                existing = cursor.fetchone()

                if existing:
                    # تجديد الاشتراك
                    new_expire_time = max(existing['expire_time'], now) + (duration_days * 24 * 3600)
                    cursor.execute(f"""
                        UPDATE {self.subscribers_table}
                        SET expire_time = %s, is_active = TRUE, subscription_count = subscription_count + 1,
                            last_payment_date = %s, updated_at = CURRENT_TIMESTAMP
                        WHERE user_id = %s
                    """, (new_expire_time, now, user_id))
                    logger.info(f"Renewed subscription for user {user_id}")
                else:
                    # مشترك جديد
                    cursor.execute(f"""
                        INSERT INTO {self.subscribers_table}
                        (user_id, username, first_name, last_name, join_time, expire_time, last_payment_date)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (user_id, username, first_name, last_name, now, expire_time, now))
                    logger.info(f"Added new subscriber {user_id}")

                return True

        except Exception as e:
            logger.error(f"Failed to add/update subscriber {user_id}: {e}")
            return False

    def get_subscriber(self, user_id):
        """الحصول على بيانات مشترك"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"SELECT * FROM {self.subscribers_table} WHERE user_id = %s", (user_id,))
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get subscriber {user_id}: {e}")
            return None

    def get_expiring_subscribers(self, days_before=3):
        """الحصول على المشتركين المنتهية صلاحيتهم قريباً"""
        try:
            with self.get_db_cursor() as cursor:
                warning_time = int((datetime.now() + timedelta(days=days_before)).timestamp())
                cursor.execute(f"""
                    SELECT * FROM {self.subscribers_table}
                    WHERE is_active = TRUE AND expire_time <= %s AND expire_time > %s
                """, (warning_time, int(datetime.now().timestamp())))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get expiring subscribers: {e}")
            return []

    def get_expired_subscribers(self):
        """الحصول على المشتركين المنتهية صلاحيتهم"""
        try:
            with self.get_db_cursor() as cursor:
                now = int(datetime.now().timestamp())
                cursor.execute(f"""
                    SELECT * FROM {self.subscribers_table}
                    WHERE is_active = TRUE AND expire_time <= %s
                """, (now,))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get expired subscribers: {e}")
            return []

    def deactivate_subscriber(self, user_id):
        """إلغاء تفعيل مشترك"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {self.subscribers_table}
                    SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = %s
                """, (user_id,))
                logger.info(f"Deactivated subscriber {user_id}")
                return True
        except Exception as e:
            logger.error(f"Failed to deactivate subscriber {user_id}: {e}")
            return False

    def add_payment(self, user_id, amount, payment_method=None, transaction_id=None, message_id=None):
        """إضافة دفعة جديدة"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.payments_table}
                    (user_id, amount, payment_method, transaction_id, payment_proof_message_id)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING id
                """, (user_id, amount, payment_method, transaction_id, message_id))
                payment_id = cursor.fetchone()['id']
                logger.info(f"Added payment {payment_id} for user {user_id}")
                return payment_id
        except Exception as e:
            logger.error(f"Failed to add payment for user {user_id}: {e}")
            return None

    def approve_payment(self, payment_id, admin_id, notes=None):
        """الموافقة على دفعة"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {self.payments_table}
                    SET status = 'approved', admin_id = %s, approved_at = CURRENT_TIMESTAMP, notes = %s
                    WHERE id = %s
                    RETURNING user_id, amount
                """, (admin_id, notes, payment_id))
                result = cursor.fetchone()
                if result:
                    # تحديث إجمالي المدفوعات للمشترك
                    cursor.execute(f"""
                        UPDATE {self.subscribers_table}
                        SET total_paid = total_paid + %s
                        WHERE user_id = %s
                    """, (result['amount'], result['user_id']))
                    logger.info(f"Approved payment {payment_id}")
                    return result['user_id']
                return None
        except Exception as e:
            logger.error(f"Failed to approve payment {payment_id}: {e}")
            return None

    def reject_payment(self, payment_id, admin_id, notes=None):
        """رفض دفعة"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {self.payments_table}
                    SET status = 'rejected', admin_id = %s, notes = %s
                    WHERE id = %s
                    RETURNING user_id
                """, (admin_id, notes, payment_id))
                result = cursor.fetchone()
                if result:
                    logger.info(f"Rejected payment {payment_id}")
                    return result['user_id']
                return None
        except Exception as e:
            logger.error(f"Failed to reject payment {payment_id}: {e}")
            return None

    def add_notification(self, user_id, notification_type, message):
        """إضافة تنبيه"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.notifications_table} (user_id, notification_type, message)
                    VALUES (%s, %s, %s)
                """, (user_id, notification_type, message))
                logger.info(f"Added notification for user {user_id}")
                return True
        except Exception as e:
            logger.error(f"Failed to add notification for user {user_id}: {e}")
            return False

    def log_activity(self, user_id, action, details=None, ip_address=None):
        """تسجيل نشاط"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.activity_logs_table} (user_id, action, details, ip_address)
                    VALUES (%s, %s, %s, %s)
                """, (user_id, action, details, ip_address))
                return True
        except Exception as e:
            logger.error(f"Failed to log activity: {e}")
            return False

    def get_statistics(self):
        """الحصول على إحصائيات النظام"""
        try:
            with self.get_db_cursor() as cursor:
                # إحصائيات عامة
                cursor.execute(f"SELECT COUNT(*) as total FROM {self.subscribers_table}")
                total_subscribers = cursor.fetchone()['total']

                cursor.execute(f"SELECT COUNT(*) as active FROM {self.subscribers_table} WHERE is_active = TRUE")
                active_subscribers = cursor.fetchone()['active']

                cursor.execute(f"SELECT SUM(total_paid) as revenue FROM {self.subscribers_table}")
                total_revenue = cursor.fetchone()['revenue'] or 0

                # إحصائيات اليوم
                cursor.execute(f"""
                    SELECT COUNT(*) as today_new
                    FROM {self.subscribers_table}
                    WHERE DATE(created_at) = CURRENT_DATE
                """)
                today_new = cursor.fetchone()['today_new']

                cursor.execute(f"""
                    SELECT COUNT(*) as today_payments
                    FROM {self.payments_table}
                    WHERE DATE(created_at) = CURRENT_DATE AND status = 'approved'
                """)
                today_payments = cursor.fetchone()['today_payments']

                return {
                    'total_subscribers': total_subscribers,
                    'active_subscribers': active_subscribers,
                    'total_revenue': float(total_revenue),
                    'today_new_subscribers': today_new,
                    'today_payments': today_payments
                }
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return None

    def save_invite_link(self, user_id, invite_link, expire_time):
        """حفظ رابط دعوة جديد"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.invite_links_table} (user_id, invite_link, expire_time)
                    VALUES (%s, %s, %s)
                    RETURNING id
                """, (user_id, invite_link, expire_time))
                link_id = cursor.fetchone()['id']
                logger.info(f"Saved invite link {link_id} for user {user_id}")
                return link_id
        except Exception as e:
            logger.error(f"Failed to save invite link for user {user_id}: {e}")
            return None

    def get_active_invite_links(self, user_id):
        """الحصول على روابط الدعوة النشطة للمستخدم"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM {self.invite_links_table}
                    WHERE user_id = %s AND is_active = TRUE AND expire_time > %s
                """, (user_id, int(datetime.now().timestamp())))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get active invite links for user {user_id}: {e}")
            return []

    def revoke_user_invite_links(self, user_id):
        """إلغاء جميع روابط الدعوة للمستخدم"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {self.invite_links_table}
                    SET is_active = FALSE, revoked_at = CURRENT_TIMESTAMP
                    WHERE user_id = %s AND is_active = TRUE
                """, (user_id,))
                revoked_count = cursor.rowcount
                logger.info(f"Revoked {revoked_count} invite links for user {user_id}")
                return revoked_count
        except Exception as e:
            logger.error(f"Failed to revoke invite links for user {user_id}: {e}")
            return 0

    def cleanup_expired_invite_links(self):
        """تنظيف روابط الدعوة المنتهية الصلاحية"""
        try:
            with self.get_db_cursor() as cursor:
                current_time = int(datetime.now().timestamp())
                cursor.execute(f"""
                    UPDATE {self.invite_links_table}
                    SET is_active = FALSE, revoked_at = CURRENT_TIMESTAMP
                    WHERE expire_time <= %s AND is_active = TRUE
                """, (current_time,))
                cleaned_count = cursor.rowcount
                logger.info(f"Cleaned up {cleaned_count} expired invite links")
                return cleaned_count
        except Exception as e:
            logger.error(f"Failed to cleanup expired invite links: {e}")
            return 0

    def add_to_blacklist(self, user_id, username=None, first_name=None, reason="", banned_by=None, notes=None, ban_type="permanent"):
        """إضافة مستخدم لقائمة الحظر"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.blacklist_table}
                    (user_id, username, first_name, reason, banned_by, notes, ban_type)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (user_id) DO UPDATE SET
                        username = EXCLUDED.username,
                        first_name = EXCLUDED.first_name,
                        reason = EXCLUDED.reason,
                        banned_by = EXCLUDED.banned_by,
                        banned_at = CURRENT_TIMESTAMP,
                        is_active = TRUE,
                        notes = EXCLUDED.notes,
                        ban_type = EXCLUDED.ban_type
                    RETURNING id
                """, (user_id, username, first_name, reason, banned_by, notes, ban_type))

                result = cursor.fetchone()
                ban_id = result['id'] if result else None
                logger.info(f"Added user {user_id} to blacklist with ID {ban_id}")
                return ban_id
        except Exception as e:
            logger.error(f"Failed to add user {user_id} to blacklist: {e}")
            return None

    def remove_from_blacklist(self, user_id):
        """إزالة مستخدم من قائمة الحظر"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {self.blacklist_table}
                    SET is_active = FALSE
                    WHERE user_id = %s AND is_active = TRUE
                """, (user_id,))

                removed_count = cursor.rowcount
                logger.info(f"Removed user {user_id} from blacklist")
                return removed_count > 0
        except Exception as e:
            logger.error(f"Failed to remove user {user_id} from blacklist: {e}")
            return False

    def is_user_blacklisted(self, user_id):
        """التحقق من أن المستخدم محظور"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, reason, banned_at, ban_type, notes
                    FROM {self.blacklist_table}
                    WHERE user_id = %s AND is_active = TRUE
                """, (user_id,))

                result = cursor.fetchone()
                return result
        except Exception as e:
            logger.error(f"Failed to check blacklist status for user {user_id}: {e}")
            return None

    def get_blacklisted_users(self, limit=50, offset=0):
        """الحصول على قائمة المستخدمين المحظورين"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT user_id, username, first_name, reason, banned_by, banned_at, ban_type, notes
                    FROM {self.blacklist_table}
                    WHERE is_active = TRUE
                    ORDER BY banned_at DESC
                    LIMIT %s OFFSET %s
                """, (limit, offset))

                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get blacklisted users: {e}")
            return []

    def get_blacklist_stats(self):
        """الحصول على إحصائيات قائمة الحظر"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_banned,
                        COUNT(CASE WHEN ban_type = 'permanent' THEN 1 END) as permanent_bans,
                        COUNT(CASE WHEN ban_type = 'temporary' THEN 1 END) as temporary_bans,
                        COUNT(CASE WHEN banned_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as banned_today
                    FROM {self.blacklist_table}
                    WHERE is_active = TRUE
                """)

                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get blacklist stats: {e}")
            return None

    # ===== دوال نظام الدعم =====

    def add_to_support_queue(self, user_id, username=None, first_name=None, subject="", description=""):
        """إضافة مستخدم لطابور الدعم"""
        try:
            with self.get_db_cursor() as cursor:
                # التحقق من وجود طلب دعم نشط للمستخدم
                cursor.execute(f"""
                    SELECT id FROM {self.support_queue_table}
                    WHERE user_id = %s AND status IN ('waiting', 'in_progress')
                """, (user_id,))

                existing = cursor.fetchone()
                if existing:
                    return None  # المستخدم لديه طلب دعم نشط بالفعل

                # الحصول على الموضع في الطابور
                cursor.execute(f"""
                    SELECT COALESCE(MAX(queue_position), 0) + 1 as next_position
                    FROM {self.support_queue_table}
                    WHERE status = 'waiting'
                """)
                next_position = cursor.fetchone()['next_position']

                # إضافة للطابور
                cursor.execute(f"""
                    INSERT INTO {self.support_queue_table}
                    (user_id, username, first_name, subject, description, queue_position)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, (user_id, username, first_name, subject, description, next_position))

                queue_id = cursor.fetchone()['id']
                logger.info(f"Added user {user_id} to support queue at position {next_position}")
                return queue_id
        except Exception as e:
            logger.error(f"Failed to add user {user_id} to support queue: {e}")
            return None

    def get_queue_position(self, user_id):
        """الحصول على موضع المستخدم في الطابور"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT queue_position, status FROM {self.support_queue_table}
                    WHERE user_id = %s AND status IN ('waiting', 'in_progress')
                    ORDER BY created_at DESC LIMIT 1
                """, (user_id,))
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get queue position for user {user_id}: {e}")
            return None

    def get_next_in_queue(self):
        """الحصول على المستخدم التالي في الطابور"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM {self.support_queue_table}
                    WHERE status = 'waiting'
                    ORDER BY queue_position ASC
                    LIMIT 1
                """)
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get next user in queue: {e}")
            return None

    def start_support_session(self, queue_id, admin_id):
        """بدء جلسة دعم"""
        try:
            with self.get_db_cursor() as cursor:
                # تحديث حالة الطلب في الطابور
                cursor.execute(f"""
                    UPDATE {self.support_queue_table}
                    SET status = 'in_progress', started_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    RETURNING user_id
                """, (queue_id,))

                result = cursor.fetchone()
                if not result:
                    return None

                user_id = result['user_id']

                # إنشاء جلسة دعم
                cursor.execute(f"""
                    INSERT INTO {self.support_sessions_table}
                    (user_id, admin_id, status)
                    VALUES (%s, %s, 'active')
                    RETURNING id
                """, (user_id, admin_id))

                session_id = cursor.fetchone()['id']

                # تحديث حالة الإدارة
                cursor.execute(f"""
                    INSERT INTO {self.support_status_table} (admin_id, status, current_user_id)
                    VALUES (%s, 'busy', %s)
                    ON CONFLICT (admin_id) DO UPDATE SET
                        status = 'busy',
                        current_user_id = EXCLUDED.current_user_id,
                        last_updated = CURRENT_TIMESTAMP
                """, (admin_id, user_id))

                logger.info(f"Started support session {session_id} for user {user_id} with admin {admin_id}")
                return session_id
        except Exception as e:
            logger.error(f"Failed to start support session: {e}")
            return None

    def end_support_session(self, session_id, admin_id):
        """إنهاء جلسة دعم"""
        try:
            with self.get_db_cursor() as cursor:
                # إنهاء الجلسة
                cursor.execute(f"""
                    UPDATE {self.support_sessions_table}
                    SET status = 'completed', session_end = CURRENT_TIMESTAMP
                    WHERE id = %s AND admin_id = %s
                    RETURNING user_id
                """, (session_id, admin_id))

                result = cursor.fetchone()
                if not result:
                    return False

                user_id = result['user_id']

                # تحديث حالة الطلب في الطابور
                cursor.execute(f"""
                    UPDATE {self.support_queue_table}
                    SET status = 'completed', completed_at = CURRENT_TIMESTAMP
                    WHERE user_id = %s AND status = 'in_progress'
                """, (user_id,))

                # تحديث حالة الإدارة
                cursor.execute(f"""
                    UPDATE {self.support_status_table}
                    SET status = 'available', current_user_id = NULL, last_updated = CURRENT_TIMESTAMP
                    WHERE admin_id = %s
                """, (admin_id,))

                # إعادة ترتيب الطابور
                cursor.execute(f"""
                    UPDATE {self.support_queue_table}
                    SET queue_position = queue_position - 1
                    WHERE status = 'waiting' AND queue_position > (
                        SELECT queue_position FROM {self.support_queue_table}
                        WHERE user_id = %s AND status = 'completed'
                    )
                """, (user_id,))

                logger.info(f"Ended support session {session_id} for user {user_id}")
                return True
        except Exception as e:
            logger.error(f"Failed to end support session {session_id}: {e}")
            return False

    def get_admin_status(self, admin_id):
        """الحصول على حالة الإدارة"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM {self.support_status_table}
                    WHERE admin_id = %s
                """, (admin_id,))
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get admin status for {admin_id}: {e}")
            return None

    def set_admin_status(self, admin_id, status):
        """تحديث حالة الإدارة"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    INSERT INTO {self.support_status_table} (admin_id, status)
                    VALUES (%s, %s)
                    ON CONFLICT (admin_id) DO UPDATE SET
                        status = EXCLUDED.status,
                        last_updated = CURRENT_TIMESTAMP
                """, (admin_id, status))
                logger.info(f"Updated admin {admin_id} status to {status}")
                return True
        except Exception as e:
            logger.error(f"Failed to update admin status: {e}")
            return False

    def get_active_support_session(self, user_id):
        """الحصول على جلسة الدعم النشطة للمستخدم"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM {self.support_sessions_table}
                    WHERE user_id = %s AND status = 'active'
                    ORDER BY session_start DESC LIMIT 1
                """, (user_id,))
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get active support session for user {user_id}: {e}")
            return None

    def remove_from_support_queue(self, user_id):
        """إزالة مستخدم من طابور الدعم"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    UPDATE {self.support_queue_table}
                    SET status = 'cancelled'
                    WHERE user_id = %s AND status = 'waiting'
                """, (user_id,))

                removed_count = cursor.rowcount
                if removed_count > 0:
                    # إعادة ترتيب الطابور
                    cursor.execute(f"""
                        UPDATE {self.support_queue_table}
                        SET queue_position = queue_position - 1
                        WHERE status = 'waiting' AND queue_position > (
                            SELECT queue_position FROM {self.support_queue_table}
                            WHERE user_id = %s AND status = 'cancelled'
                        )
                    """, (user_id,))

                logger.info(f"Removed user {user_id} from support queue")
                return removed_count > 0
        except Exception as e:
            logger.error(f"Failed to remove user {user_id} from support queue: {e}")
            return False

    def get_queue_stats(self):
        """الحصول على إحصائيات طابور الدعم"""
        try:
            with self.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT
                        COUNT(CASE WHEN status = 'waiting' THEN 1 END) as waiting_count,
                        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                        COUNT(CASE WHEN status = 'completed' AND DATE(completed_at) = CURRENT_DATE THEN 1 END) as completed_today
                    FROM {self.support_queue_table}
                """)
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Failed to get queue stats: {e}")
            return None


