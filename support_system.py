"""
نظام الدعم المتقدم والمبسط
Advanced Support System
"""
import logging
from datetime import datetime
import telebot  # type: ignore
from telebot import types  # type: ignore
from database import DatabaseManager
from config import Config

logger = logging.getLogger(__name__)

class SupportSystem:
    """نظام الدعم الشامل مع إمكانية دعم جلستين متزامنتين"""

    def __init__(self, bot: telebot.TeleBot):
        self.bot = bot
        self.db = DatabaseManager()
        self.config = Config()

        # حد أقصى جلستين دعم متزامنتين
        self.max_concurrent_sessions = 2

    def request_support(self, user_id, username=None, first_name=None, subject="طلب دعم", description=""):
        """طلب دعم جديد"""
        try:
            # التحقق من أن المستخدم مشترك
            subscriber = self.db.get_subscriber(user_id)
            if not subscriber:
                return {
                    'success': False,
                    'message': "❌ يجب أن تكون مشتركاً للحصول على الدعم\n\n💡 اشترك أولاً ثم اطلب الدعم"
                }

            # التحقق من وجود طلب دعم نشط
            queue_position = self.db.get_queue_position(user_id)
            if queue_position:
                if queue_position['status'] == 'waiting':
                    return {
                        'success': False,
                        'message': f"⏳ لديك طلب دعم نشط بالفعل\n\n📍 موضعك في الطابور: {queue_position['queue_position']}\n\n💡 انتظر دورك أو ألغِ الطلب الحالي"
                    }
                elif queue_position['status'] == 'in_progress':
                    return {
                        'success': False,
                        'message': "💬 لديك جلسة دعم نشطة حالياً\n\n💡 أكمل الجلسة الحالية أولاً"
                    }

            # إضافة للطابور
            queue_id = self.db.add_to_support_queue(user_id, username, first_name, subject, description)

            if queue_id:
                # الحصول على الموضع الجديد
                position_info = self.db.get_queue_position(user_id)
                position = position_info['queue_position'] if position_info else 1

                # حساب الوقت المتوقع للانتظار (تقريبي)
                estimated_wait = self._calculate_estimated_wait_time(position)

                # إرسال إشعار للإدارة
                self._notify_admin_new_support_request(user_id, username, first_name, position)

                return {
                    'success': True,
                    'queue_id': queue_id,
                    'position': position,
                    'estimated_wait': estimated_wait,
                    'message': f"✅ تم إضافتك لطابور الدعم بنجاح!\n\n"
                              f"📍 موضعك في الطابور: {position}\n"
                              f"⏰ الوقت المتوقع: {estimated_wait} دقيقة\n\n"
                              f"🔔 سنرسل لك إشعار عند حلول دورك\n"
                              f"💡 يمكنك إلغاء الطلب في أي وقت"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ حدث خطأ في إضافتك للطابور\nيرجى المحاولة لاحقاً"
                }

        except Exception as e:
            logger.error(f"Failed to process support request for user {user_id}: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في معالجة طلبك\nيرجى المحاولة لاحقاً"
            }

    def cancel_support_request(self, user_id):
        """إلغاء طلب الدعم"""
        try:
            success = self.db.remove_from_support_queue(user_id)

            if success:
                # إشعار الإدارة بإلغاء الطلب
                self._notify_admin_support_cancelled(user_id)

                return {
                    'success': True,
                    'message': "✅ تم إلغاء طلب الدعم بنجاح\n\n💡 يمكنك طلب الدعم مرة أخرى في أي وقت"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ لا يوجد طلب دعم نشط لإلغائه"
                }

        except Exception as e:
            logger.error(f"Failed to cancel support request for user {user_id}: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في إلغاء الطلب"
            }

    def check_queue_status(self, user_id):
        """التحقق من حالة الطابور"""
        try:
            position_info = self.db.get_queue_position(user_id)

            if not position_info:
                return {
                    'success': False,
                    'message': "📋 لا يوجد لديك طلب دعم نشط\n\n💡 يمكنك طلب الدعم الآن"
                }

            if position_info['status'] == 'waiting':
                estimated_wait = self._calculate_estimated_wait_time(position_info['queue_position'])

                return {
                    'success': True,
                    'status': 'waiting',
                    'position': position_info['queue_position'],
                    'estimated_wait': estimated_wait,
                    'message': f"⏳ حالة طلب الدعم: في الانتظار\n\n"
                              f"📍 موضعك في الطابور: {position_info['queue_position']}\n"
                              f"⏰ الوقت المتوقع: {estimated_wait} دقيقة\n\n"
                              f"🔔 سنرسل لك إشعار عند حلول دورك"
                }
            elif position_info['status'] == 'in_progress':
                return {
                    'success': True,
                    'status': 'in_progress',
                    'message': "💬 جلسة الدعم نشطة الآن!\n\n"
                              "📞 يمكنك التحدث مع فريق الدعم\n"
                              "💡 اكتب رسالتك وسيتم توصيلها فوراً"
                }

        except Exception as e:
            logger.error(f"Failed to check queue status for user {user_id}: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في التحقق من الحالة"
            }

    def get_next_user_for_support(self):
        """الحصول على المستخدم التالي للدعم"""
        try:
            # التحقق من عدد الجلسات النشطة
            stats = self.db.get_queue_stats()
            if stats and stats['in_progress_count'] >= self.max_concurrent_sessions:
                return {
                    'success': False,
                    'message': f"⚠️ تم الوصول للحد الأقصى من الجلسات المتزامنة ({self.max_concurrent_sessions})"
                }

            # الحصول على المستخدم التالي
            next_user = self.db.get_next_in_queue()

            if next_user:
                return {
                    'success': True,
                    'user_data': next_user,
                    'message': f"👤 المستخدم التالي جاهز للدعم:\n\n"
                              f"🆔 المعرف: {next_user['user_id']}\n"
                              f"👤 الاسم: {next_user['first_name']}\n"
                              f"📝 الموضوع: {next_user['subject']}\n"
                              f"📋 الوصف: {next_user['description'][:100]}..."
                }
            else:
                return {
                    'success': False,
                    'message': "📭 لا يوجد مستخدمين في طابور الدعم حالياً"
                }

        except Exception as e:
            logger.error(f"Failed to get next user for support: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في الحصول على المستخدم التالي"
            }

    def start_support_session(self, queue_id, admin_id):
        """بدء جلسة دعم"""
        try:
            session_id = self.db.start_support_session(queue_id, admin_id)

            if session_id:
                # الحصول على بيانات المستخدم
                queue_data = self.db.get_next_in_queue()  # سنحتاج لتحسين هذا

                return {
                    'success': True,
                    'session_id': session_id,
                    'message': "✅ تم بدء جلسة الدعم بنجاح!"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في بدء جلسة الدعم"
                }

        except Exception as e:
            logger.error(f"Failed to start support session: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في بدء الجلسة"
            }

    def end_support_session(self, session_id, admin_id):
        """إنهاء جلسة دعم"""
        try:
            success = self.db.end_support_session(session_id, admin_id)

            if success:
                return {
                    'success': True,
                    'message': "✅ تم إنهاء جلسة الدعم بنجاح!"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في إنهاء جلسة الدعم"
                }

        except Exception as e:
            logger.error(f"Failed to end support session: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في إنهاء الجلسة"
            }

    def get_admin_status(self, admin_id):
        """الحصول على حالة الإدارة"""
        try:
            status = self.db.get_admin_status(admin_id)

            if status:
                return {
                    'success': True,
                    'status': status['status'],
                    'current_user': status.get('current_user_id'),
                    'last_updated': status.get('last_updated')
                }
            else:
                # إنشاء حالة جديدة
                self.db.set_admin_status(admin_id, 'available')
                return {
                    'success': True,
                    'status': 'available',
                    'current_user': None,
                    'last_updated': datetime.now()
                }

        except Exception as e:
            logger.error(f"Failed to get admin status: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في الحصول على الحالة"
            }

    def set_admin_status(self, admin_id, status):
        """تحديث حالة الإدارة"""
        try:
            success = self.db.set_admin_status(admin_id, status)

            if success:
                return {
                    'success': True,
                    'message': f"✅ تم تحديث حالتك إلى: {status}"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في تحديث الحالة"
                }

        except Exception as e:
            logger.error(f"Failed to set admin status: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في تحديث الحالة"
            }

    def _calculate_estimated_wait_time(self, position):
        """حساب الوقت المتوقع للانتظار"""
        # متوسط 10 دقائق لكل جلسة دعم
        average_session_time = 10
        return position * average_session_time

    def notify_user_turn(self, user_id):
        """إشعار المستخدم بحلول دوره"""
        try:
            message = "🔔 **حان دورك في الدعم!**\n\n" \
                     "👋 مرحباً! فريق الدعم جاهز للمساعدة\n" \
                     "💬 يمكنك الآن كتابة رسالتك وسيتم الرد عليك فوراً\n\n" \
                     "⚡ الجلسة نشطة الآن - ابدأ المحادثة!"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("💬 بدء المحادثة", callback_data="start_support_chat"))

            self.bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(user_id, "support_turn_notification", "User notified of support turn")

            return True

        except Exception as e:
            logger.error(f"Failed to notify user {user_id} of support turn: {e}")
            return False

    def get_support_stats(self):
        """الحصول على إحصائيات الدعم"""
        try:
            stats = self.db.get_queue_stats()

            if stats:
                return {
                    'success': True,
                    'waiting_count': stats['waiting_count'],
                    'in_progress_count': stats['in_progress_count'],
                    'completed_today': stats['completed_today'],
                    'max_concurrent': self.max_concurrent_sessions,
                    'available_slots': self.max_concurrent_sessions - stats['in_progress_count']
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في الحصول على الإحصائيات"
                }

        except Exception as e:
            logger.error(f"Failed to get support stats: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في الحصول على الإحصائيات"
            }

    def _notify_admin_new_support_request(self, user_id, username, first_name, position):
        """إرسال إشعار للإدارة عند طلب دعم جديد"""
        try:
            from config import Config
            config = Config()

            # الحصول على إحصائيات الطابور
            stats = self.get_support_stats()
            waiting_count = stats.get('waiting_count', 0) if stats.get('success') else 0

            # إنشاء رسالة الإشعار
            admin_message = f"🔔 **طلب دعم جديد!**\n\n" \
                          f"👤 **المستخدم:** {first_name or 'غير محدد'}\n" \
                          f"📝 **اسم المستخدم:** @{username or 'غير محدد'}\n" \
                          f"🆔 **معرف المستخدم:** `{user_id}`\n" \
                          f"📍 **موضعه في الطابور:** {position}\n\n" \
                          f"📊 **حالة الطابور:**\n" \
                          f"⏳ في الانتظار: {waiting_count}\n" \
                          f"💬 جلسات نشطة: {stats.get('in_progress_count', 0) if stats.get('success') else 0}\n" \
                          f"✅ مكتملة اليوم: {stats.get('completed_today', 0) if stats.get('success') else 0}\n\n" \
                          f"⚡ **إجراءات سريعة:**"

            # إنشاء أزرار للإدارة
            import telebot  # type: ignore
            markup = telebot.types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                telebot.types.InlineKeyboardButton("👤 بدء الدعم", callback_data=f"admin_start_support_{user_id}"),
                telebot.types.InlineKeyboardButton("📊 حالة الطابور", callback_data="admin_support_queue")
            )
            markup.add(
                telebot.types.InlineKeyboardButton("📞 راسل المستخدم", url=f"tg://user?id={user_id}"),
                telebot.types.InlineKeyboardButton("🎛️ لوحة الإدارة", callback_data="admin_menu")
            )

            # إرسال الإشعار للإدارة
            self.bot.send_message(
                config.ADMIN_ID,
                admin_message,
                reply_markup=markup,
                parse_mode='Markdown'
            )

            # تسجيل النشاط
            self.db.log_activity(
                config.ADMIN_ID,
                "admin_notified_new_support_request",
                f"User {user_id} requested support, position {position}"
            )

            logger.info(f"Admin notified of new support request from user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to notify admin of new support request from user {user_id}: {e}")
            return False

    def _notify_admin_support_cancelled(self, user_id):
        """إشعار الإدارة بإلغاء طلب الدعم"""
        try:
            from config import Config
            config = Config()

            # الحصول على بيانات المستخدم
            subscriber = self.db.get_subscriber(user_id)
            first_name = subscriber.get('first_name', 'غير محدد') if subscriber else 'غير محدد'
            username = subscriber.get('username', 'غير محدد') if subscriber else 'غير محدد'

            # إنشاء رسالة الإشعار
            admin_message = f"❌ **تم إلغاء طلب دعم**\n\n" \
                          f"👤 **المستخدم:** {first_name}\n" \
                          f"📝 **اسم المستخدم:** @{username}\n" \
                          f"🆔 **معرف المستخدم:** `{user_id}`\n" \
                          f"⏰ **وقت الإلغاء:** {datetime.now().strftime('%H:%M')}\n\n" \
                          f"📊 **تم تحديث الطابور تلقائياً**"

            # إرسال الإشعار للإدارة
            self.bot.send_message(
                config.ADMIN_ID,
                admin_message,
                parse_mode='Markdown'
            )

            # تسجيل النشاط
            self.db.log_activity(
                config.ADMIN_ID,
                "admin_notified_support_cancelled",
                f"User {user_id} cancelled support request"
            )

            logger.info(f"Admin notified of cancelled support request from user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to notify admin of cancelled support request from user {user_id}: {e}")
            return False
