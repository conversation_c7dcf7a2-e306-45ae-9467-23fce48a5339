"""
نظام الدعم المتقدم والمبسط
Advanced Support System
"""
import logging
from datetime import datetime
import telebot  # type: ignore
from telebot import types  # type: ignore
from database import DatabaseManager
from config import Config

logger = logging.getLogger(__name__)

class SupportSystem:
    """نظام الدعم الشامل مع إمكانية دعم جلستين متزامنتين"""

    def __init__(self, bot: telebot.TeleBot):
        self.bot = bot
        self.db = DatabaseManager()
        self.config = Config()

        # حد أقصى جلستين دعم متزامنتين
        self.max_concurrent_sessions = 2

    def request_support(self, user_id, username=None, first_name=None, subject="طلب دعم", description=""):
        """طلب دعم جديد"""
        try:
            # التحقق من أن المستخدم مشترك
            subscriber = self.db.get_subscriber(user_id)
            if not subscriber:
                return {
                    'success': False,
                    'message': "❌ يجب أن تكون مشتركاً للحصول على الدعم\n\n💡 اشترك أولاً ثم اطلب الدعم"
                }

            # التحقق من وجود طلب دعم نشط
            queue_position = self.db.get_queue_position(user_id)
            if queue_position:
                if queue_position['status'] == 'waiting':
                    return {
                        'success': False,
                        'message': f"⏳ لديك طلب دعم نشط بالفعل\n\n📍 موضعك في الطابور: {queue_position['queue_position']}\n\n💡 انتظر دورك أو ألغِ الطلب الحالي"
                    }
                elif queue_position['status'] == 'in_progress':
                    return {
                        'success': False,
                        'message': "💬 لديك جلسة دعم نشطة حالياً\n\n💡 أكمل الجلسة الحالية أولاً"
                    }

            # إضافة للطابور
            queue_id = self.db.add_to_support_queue(user_id, username, first_name, subject, description)

            if queue_id:
                # الحصول على الموضع الجديد
                position_info = self.db.get_queue_position(user_id)
                position = position_info['queue_position'] if position_info else 1

                # حساب الوقت المتوقع للانتظار (تقريبي)
                estimated_wait = self._calculate_estimated_wait_time(position)

                # إرسال إشعار للإدارة
                self._notify_admin_new_support_request(user_id, username, first_name, position)

                return {
                    'success': True,
                    'queue_id': queue_id,
                    'position': position,
                    'estimated_wait': estimated_wait,
                    'message': f"✅ **تم إضافتك لطابور الدعم بنجاح!**\n\n"
                              f"📍 **موضعك في الطابور:** {position}\n"
                              f"⏰ **الوقت المتوقع للانتظار:** {estimated_wait} دقيقة\n"
                              f"👥 **عدد المنتظرين أمامك:** {position - 1}\n\n"
                              f"🔔 **سنرسل لك إشعار فوري عند حلول دورك**\n"
                              f"📱 **ستصلك رسالة تنبيه عندما يكون فريق الدعم جاهز**\n\n"
                              f"💡 **يمكنك:**\n"
                              f"• التحقق من حالة الطابور في أي وقت\n"
                              f"• إلغاء الطلب إذا لم تعد تحتاج للدعم\n"
                              f"• الاستمرار في استخدام البوت بشكل طبيعي\n\n"
                              f"⚡ **تم إشعار الإدارة بطلبك - ستحصل على دعم سريع!**"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ حدث خطأ في إضافتك للطابور\nيرجى المحاولة لاحقاً"
                }

        except Exception as e:
            logger.error(f"Failed to process support request for user {user_id}: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في معالجة طلبك\nيرجى المحاولة لاحقاً"
            }

    def cancel_support_request(self, user_id):
        """إلغاء طلب الدعم"""
        try:
            success = self.db.remove_from_support_queue(user_id)

            if success:
                # إشعار الإدارة بإلغاء الطلب
                self._notify_admin_support_cancelled(user_id)

                return {
                    'success': True,
                    'message': "✅ **تم إلغاء طلب الدعم بنجاح**\n\n"
                              "📋 **تم إزالتك من طابور الانتظار**\n"
                              "🔄 **تم تحديث مواضع المنتظرين تلقائياً**\n\n"
                              "💡 **يمكنك:**\n"
                              "• طلب الدعم مرة أخرى في أي وقت\n"
                              "• استخدام البوت بشكل طبيعي\n"
                              "• التواصل مع الإدارة مباشرة إذا احتجت\n\n"
                              "🎯 **نحن هنا لمساعدتك متى احتجت!**"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ لا يوجد طلب دعم نشط لإلغائه"
                }

        except Exception as e:
            logger.error(f"Failed to cancel support request for user {user_id}: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في إلغاء الطلب"
            }

    def check_queue_status(self, user_id):
        """التحقق من حالة الطابور"""
        try:
            position_info = self.db.get_queue_position(user_id)

            if not position_info:
                return {
                    'success': False,
                    'message': "📋 **لا يوجد لديك طلب دعم نشط حالياً**\n\n"
                              "💡 **يمكنك:**\n"
                              "• طلب الدعم الآن إذا كنت تحتاج مساعدة\n"
                              "• التواصل مع الإدارة مباشرة\n"
                              "• استخدام البوت بشكل طبيعي\n\n"
                              "🎧 **اضغط 'طلب دعم' للحصول على المساعدة**\n"
                              "⚡ **فريق الدعم جاهز لخدمتك 24/7**"
                }

            if position_info['status'] == 'waiting':
                estimated_wait = self._calculate_estimated_wait_time(position_info['queue_position'])

                return {
                    'success': True,
                    'status': 'waiting',
                    'position': position_info['queue_position'],
                    'estimated_wait': estimated_wait,
                    'message': f"⏳ **حالة طلب الدعم: في الانتظار**\n\n"
                              f"📍 **موضعك الحالي:** {position_info['queue_position']}\n"
                              f"👥 **عدد المنتظرين أمامك:** {position_info['queue_position'] - 1}\n"
                              f"⏰ **الوقت المتوقع المتبقي:** {estimated_wait} دقيقة\n\n"
                              f"🔔 **ستصلك رسالة فورية عند حلول دورك**\n"
                              f"📱 **لا تحتاج لفعل أي شيء - فقط انتظر**\n\n"
                              f"💡 **نصائح أثناء الانتظار:**\n"
                              f"• يمكنك استخدام البوت بشكل طبيعي\n"
                              f"• تحقق من حالة الطابور متى شئت\n"
                              f"• ألغِ الطلب إذا حُلت مشكلتك\n\n"
                              f"⚡ **فريق الدعم يعمل على خدمتك!**"
                }
            elif position_info['status'] == 'in_progress':
                return {
                    'success': True,
                    'status': 'in_progress',
                    'message': "💬 جلسة الدعم نشطة الآن!\n\n"
                              "📞 يمكنك التحدث مع فريق الدعم\n"
                              "💡 اكتب رسالتك وسيتم توصيلها فوراً"
                }

        except Exception as e:
            logger.error(f"Failed to check queue status for user {user_id}: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في التحقق من الحالة"
            }

    def get_next_user_for_support(self):
        """الحصول على المستخدم التالي للدعم"""
        try:
            # التحقق من عدد الجلسات النشطة
            stats = self.db.get_queue_stats()
            if stats and stats['in_progress_count'] >= self.max_concurrent_sessions:
                return {
                    'success': False,
                    'message': f"⚠️ تم الوصول للحد الأقصى من الجلسات المتزامنة ({self.max_concurrent_sessions})"
                }

            # الحصول على المستخدم التالي
            next_user = self.db.get_next_in_queue()

            if next_user:
                return {
                    'success': True,
                    'user_data': next_user,
                    'message': f"👤 المستخدم التالي جاهز للدعم:\n\n"
                              f"🆔 المعرف: {next_user['user_id']}\n"
                              f"👤 الاسم: {next_user['first_name']}\n"
                              f"📝 الموضوع: {next_user['subject']}\n"
                              f"📋 الوصف: {next_user['description'][:100]}..."
                }
            else:
                return {
                    'success': False,
                    'message': "📭 لا يوجد مستخدمين في طابور الدعم حالياً"
                }

        except Exception as e:
            logger.error(f"Failed to get next user for support: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في الحصول على المستخدم التالي"
            }

    def start_support_session(self, queue_id, admin_id):
        """بدء جلسة دعم"""
        try:
            session_id = self.db.start_support_session(queue_id, admin_id)

            if session_id:
                # الحصول على بيانات المستخدم
                queue_data = self.db.get_next_in_queue()  # سنحتاج لتحسين هذا

                return {
                    'success': True,
                    'session_id': session_id,
                    'message': "✅ تم بدء جلسة الدعم بنجاح!"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في بدء جلسة الدعم"
                }

        except Exception as e:
            logger.error(f"Failed to start support session: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في بدء الجلسة"
            }

    def end_support_session(self, session_id, admin_id):
        """إنهاء جلسة دعم"""
        try:
            success = self.db.end_support_session(session_id, admin_id)

            if success:
                return {
                    'success': True,
                    'message': "✅ تم إنهاء جلسة الدعم بنجاح!"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في إنهاء جلسة الدعم"
                }

        except Exception as e:
            logger.error(f"Failed to end support session: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في إنهاء الجلسة"
            }

    def get_admin_status(self, admin_id):
        """الحصول على حالة الإدارة"""
        try:
            status = self.db.get_admin_status(admin_id)

            if status:
                return {
                    'success': True,
                    'status': status['status'],
                    'current_user': status.get('current_user_id'),
                    'last_updated': status.get('last_updated')
                }
            else:
                # إنشاء حالة جديدة
                self.db.set_admin_status(admin_id, 'available')
                return {
                    'success': True,
                    'status': 'available',
                    'current_user': None,
                    'last_updated': datetime.now()
                }

        except Exception as e:
            logger.error(f"Failed to get admin status: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في الحصول على الحالة"
            }

    def set_admin_status(self, admin_id, status):
        """تحديث حالة الإدارة"""
        try:
            # الحصول على الحالة السابقة
            old_status_result = self.get_admin_status(admin_id)
            old_status = old_status_result.get('status') if old_status_result.get('success') else None

            success = self.db.set_admin_status(admin_id, status)

            if success:
                # إشعار المشتركين بتغيير الحالة
                if old_status != status:
                    self._notify_subscribers_status_change(status, old_status)

                    # إذا تم تغيير الحالة لغير متصل، إشعار الجلسات النشطة
                    if status == 'offline':
                        self._notify_active_sessions_disconnect()

                return {
                    'success': True,
                    'message': f"✅ تم تحديث حالتك إلى: {status}"
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في تحديث الحالة"
                }

        except Exception as e:
            logger.error(f"Failed to set admin status: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في تحديث الحالة"
            }

    def _calculate_estimated_wait_time(self, position):
        """حساب الوقت المتوقع للانتظار"""
        # متوسط 10 دقائق لكل جلسة دعم
        average_session_time = 10
        return position * average_session_time

    def notify_user_turn(self, user_id):
        """إشعار المستخدم بحلول دوره"""
        try:
            message = "🔔 **حان دورك في الدعم!**\n\n" \
                     "🎉 **مرحباً! فريق الدعم جاهز الآن لمساعدتك**\n\n" \
                     "✅ **جلسة الدعم نشطة:**\n" \
                     "• اكتب رسالتك وسيتم الرد عليك فوراً\n" \
                     "• يمكنك إرسال صور أو ملفات إذا احتجت\n" \
                     "• فريق الدعم متاح الآن للإجابة على أسئلتك\n\n" \
                     "💬 **ابدأ بكتابة مشكلتك أو استفسارك:**\n" \
                     "📝 كن واضحاً ومفصلاً للحصول على أفضل مساعدة\n\n" \
                     "⚡ **الجلسة نشطة الآن - ابدأ المحادثة!**"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("💬 بدء المحادثة", callback_data="start_support_chat"))

            self.bot.send_message(user_id, message, reply_markup=markup, parse_mode='Markdown')

            # تسجيل النشاط
            self.db.log_activity(user_id, "support_turn_notification", "User notified of support turn")

            return True

        except Exception as e:
            logger.error(f"Failed to notify user {user_id} of support turn: {e}")
            return False

    def get_support_stats(self):
        """الحصول على إحصائيات الدعم"""
        try:
            stats = self.db.get_queue_stats()

            if stats:
                return {
                    'success': True,
                    'waiting_count': stats['waiting_count'],
                    'in_progress_count': stats['in_progress_count'],
                    'completed_today': stats['completed_today'],
                    'max_concurrent': self.max_concurrent_sessions,
                    'available_slots': self.max_concurrent_sessions - stats['in_progress_count']
                }
            else:
                return {
                    'success': False,
                    'message': "❌ فشل في الحصول على الإحصائيات"
                }

        except Exception as e:
            logger.error(f"Failed to get support stats: {e}")
            return {
                'success': False,
                'message': "❌ حدث خطأ في الحصول على الإحصائيات"
            }

    def _notify_admin_new_support_request(self, user_id, username, first_name, position):
        """إرسال إشعار للإدارة عند طلب دعم جديد"""
        try:
            from config import Config
            config = Config()

            # الحصول على queue_id للمستخدم
            queue_id = self.db.get_user_queue_id(user_id)
            if not queue_id:
                logger.error(f"Could not find queue_id for user {user_id}")
                return False

            # الحصول على إحصائيات الطابور
            stats = self.get_support_stats()
            waiting_count = stats.get('waiting_count', 0) if stats.get('success') else 0

            # إنشاء رسالة الإشعار
            admin_message = f"🔔 **طلب دعم جديد!**\n\n" \
                          f"👤 **المستخدم:** {first_name or 'غير محدد'}\n" \
                          f"📝 **اسم المستخدم:** @{username or 'غير محدد'}\n" \
                          f"🆔 **معرف المستخدم:** `{user_id}`\n" \
                          f"📍 **موضعه في الطابور:** {position}\n\n" \
                          f"📊 **حالة الطابور:**\n" \
                          f"⏳ في الانتظار: {waiting_count}\n" \
                          f"💬 جلسات نشطة: {stats.get('in_progress_count', 0) if stats.get('success') else 0}\n" \
                          f"✅ مكتملة اليوم: {stats.get('completed_today', 0) if stats.get('success') else 0}\n\n" \
                          f"⚡ **إجراءات سريعة:**"

            # إنشاء أزرار للإدارة
            import telebot  # type: ignore
            markup = telebot.types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                telebot.types.InlineKeyboardButton("👤 بدء الدعم", callback_data=f"start_support_{queue_id}"),
                telebot.types.InlineKeyboardButton("📊 حالة الطابور", callback_data="admin_support_queue")
            )
            markup.add(
                telebot.types.InlineKeyboardButton("📞 راسل المستخدم", url=f"tg://user?id={user_id}"),
                telebot.types.InlineKeyboardButton("🎛️ لوحة الإدارة", callback_data="admin_menu")
            )

            # إرسال الإشعار للإدارة
            self.bot.send_message(
                config.ADMIN_ID,
                admin_message,
                reply_markup=markup,
                parse_mode='Markdown'
            )

            # تسجيل النشاط
            self.db.log_activity(
                config.ADMIN_ID,
                "admin_notified_new_support_request",
                f"User {user_id} requested support, position {position}"
            )

            logger.info(f"Admin notified of new support request from user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to notify admin of new support request from user {user_id}: {e}")
            return False

    def _notify_admin_support_cancelled(self, user_id):
        """إشعار الإدارة بإلغاء طلب الدعم"""
        try:
            from config import Config
            config = Config()

            # الحصول على بيانات المستخدم
            subscriber = self.db.get_subscriber(user_id)
            first_name = subscriber.get('first_name', 'غير محدد') if subscriber else 'غير محدد'
            username = subscriber.get('username', 'غير محدد') if subscriber else 'غير محدد'

            # إنشاء رسالة الإشعار
            admin_message = f"❌ **تم إلغاء طلب دعم**\n\n" \
                          f"👤 **المستخدم:** {first_name}\n" \
                          f"📝 **اسم المستخدم:** @{username}\n" \
                          f"🆔 **معرف المستخدم:** `{user_id}`\n" \
                          f"⏰ **وقت الإلغاء:** {datetime.now().strftime('%H:%M')}\n\n" \
                          f"📊 **تم تحديث الطابور تلقائياً**"

            # إرسال الإشعار للإدارة
            self.bot.send_message(
                config.ADMIN_ID,
                admin_message,
                parse_mode='Markdown'
            )

            # تسجيل النشاط
            self.db.log_activity(
                config.ADMIN_ID,
                "admin_notified_support_cancelled",
                f"User {user_id} cancelled support request"
            )

            logger.info(f"Admin notified of cancelled support request from user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to notify admin of cancelled support request from user {user_id}: {e}")
            return False

    def _notify_subscribers_status_change(self, new_status, old_status):
        """إشعار المشتركين بتغيير حالة فريق الدعم"""
        try:
            # الحصول على المشتركين الذين لديهم طلبات دعم نشطة
            active_support_users = self.db.get_active_support_users()

            if not active_support_users:
                logger.info("No active support users to notify about status change")
                return

            # إنشاء رسالة الإشعار حسب الحالة الجديدة
            if new_status == 'offline':
                message = self._create_offline_notification_message()
            elif new_status == 'available' and old_status == 'offline':
                message = self._create_online_notification_message()
            else:
                # لا نرسل إشعار للحالات الأخرى
                return

            # إرسال الإشعار لجميع المستخدمين النشطين
            notification_count = 0
            for user_data in active_support_users:
                user_id = user_data['user_id']
                try:
                    markup = self._create_status_notification_markup(user_data)
                    self.bot.send_message(
                        user_id,
                        message,
                        reply_markup=markup,
                        parse_mode='Markdown'
                    )
                    notification_count += 1

                    # تسجيل النشاط
                    self.db.log_activity(
                        user_id,
                        "support_status_notification",
                        f"Notified of status change to {new_status}"
                    )

                except Exception as e:
                    logger.warning(f"Failed to notify user {user_id} of status change: {e}")

            logger.info(f"Notified {notification_count} users of support status change to {new_status}")

        except Exception as e:
            logger.error(f"Failed to notify subscribers of status change: {e}")

    def _create_offline_notification_message(self):
        """إنشاء رسالة إشعار عدم الاتصال"""
        return f"⚫ **تنبيه: فريق الدعم غير متصل حالياً**\n\n" \
               f"📋 **إذا كان لديك طلب دعم نشط:**\n" \
               f"• سيتم الاحتفاظ بموضعك في الطابور\n" \
               f"• ستتم خدمتك عند عودة فريق الدعم\n" \
               f"• يمكنك إلغاء الطلب إذا أردت\n\n" \
               f"⏰ **سنرسل لك إشعار عند عودة فريق الدعم**\n" \
               f"🙏 **نعتذر عن الإزعاج ونشكرك على صبرك**"

    def _create_online_notification_message(self):
        """إنشاء رسالة إشعار الاتصال"""
        return f"🟢 **فريق الدعم متصل الآن!**\n\n" \
               f"📋 **إذا كان لديك طلب دعم:**\n" \
               f"• سيتم التعامل مع طلبك قريباً\n" \
               f"• موضعك محفوظ في الطابور\n" \
               f"• ستصلك رسالة عند حلول دورك\n\n" \
               f"⚡ **نعتذر عن التأخير ونشكرك على صبرك**\n" \
               f"💪 **فريق الدعم جاهز لمساعدتك الآن!**"

    def _create_status_notification_markup(self, user_data):
        """إنشاء أزرار إشعار تغيير الحالة"""
        import telebot  # type: ignore
        markup = telebot.types.InlineKeyboardMarkup(row_width=2)

        # أزرار حسب حالة المستخدم
        if user_data.get('status') == 'waiting':
            markup.add(
                telebot.types.InlineKeyboardButton("📊 حالة الطابور", callback_data="check_support_status"),
                telebot.types.InlineKeyboardButton("❌ إلغاء الطلب", callback_data="cancel_support_request")
            )
        elif user_data.get('status') == 'in_progress':
            markup.add(
                telebot.types.InlineKeyboardButton("💬 متابعة المحادثة", callback_data="start_support_chat"),
                telebot.types.InlineKeyboardButton("📊 حالة الجلسة", callback_data="check_support_status")
            )

        markup.add(telebot.types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu"))
        return markup

    def _notify_active_sessions_disconnect(self):
        """إشعار الجلسات النشطة بانقطاع الاتصال"""
        try:
            # الحصول على الجلسات النشطة فقط
            active_sessions = self.db.get_active_support_sessions()

            if not active_sessions:
                logger.info("No active sessions to notify about disconnect")
                return

            disconnect_message = f"⚠️ **انقطع الاتصال مع فريق الدعم**\n\n" \
                               f"🔄 **لا تقلق:**\n" \
                               f"• جلستك محفوظة ولن تضيع\n" \
                               f"• سيتم استكمال الدعم عند العودة\n" \
                               f"• يمكنك الانتظار أو إنهاء الجلسة\n\n" \
                               f"📞 **سنعاود الاتصال بك قريباً**\n" \
                               f"🙏 **نعتذر عن الانقطاع المؤقت**"

            # إرسال إشعار لكل جلسة نشطة
            notification_count = 0
            for session_data in active_sessions:
                user_id = session_data['user_id']
                try:
                    markup = self._create_disconnect_notification_markup()
                    self.bot.send_message(
                        user_id,
                        disconnect_message,
                        reply_markup=markup,
                        parse_mode='Markdown'
                    )
                    notification_count += 1

                    # تسجيل النشاط
                    self.db.log_activity(
                        user_id,
                        "support_session_disconnect",
                        "Notified of support disconnect"
                    )

                except Exception as e:
                    logger.warning(f"Failed to notify user {user_id} of session disconnect: {e}")

            logger.info(f"Notified {notification_count} active sessions of disconnect")

        except Exception as e:
            logger.error(f"Failed to notify active sessions of disconnect: {e}")

    def _create_disconnect_notification_markup(self):
        """إنشاء أزرار إشعار انقطاع الجلسة"""
        import telebot  # type: ignore
        markup = telebot.types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            telebot.types.InlineKeyboardButton("⏳ انتظار العودة", callback_data="wait_for_reconnect"),
            telebot.types.InlineKeyboardButton("🔚 إنهاء الجلسة", callback_data="end_session_user")
        )
        markup.add(
            telebot.types.InlineKeyboardButton("📊 حالة الجلسة", callback_data="check_support_status"),
            telebot.types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
        )

        return markup

    def notify_support_system_started(self):
        """إشعار جماعي لجميع المشتركين ببدء نظام الدعم"""
        try:
            # الحصول على جميع المشتركين النشطين
            all_subscribers = self.db.get_all_active_subscribers()

            if not all_subscribers:
                logger.info("No active subscribers to notify about support system start")
                return {'success': True, 'notified_count': 0}

            # إنشاء رسالة الإشعار
            support_start_message = f"🎉 **فريق الدعم متاح الآن!**\n\n" \
                                  f"👋 **مرحباً بجميع المشتركين الأعزاء**\n\n" \
                                  f"✅ **نظام الدعم نشط ومتاح:**\n" \
                                  f"• فريق الدعم جاهز لمساعدتكم\n" \
                                  f"• يمكنكم طلب الدعم في أي وقت\n" \
                                  f"• استجابة سريعة ومتميزة\n" \
                                  f"• دعم فني متخصص\n\n" \
                                  f"🎯 **كيفية طلب الدعم:**\n" \
                                  f"1️⃣ اضغط على \"🎧 طلب دعم\"\n" \
                                  f"2️⃣ اكتب مشكلتك أو استفسارك\n" \
                                  f"3️⃣ انتظر دورك في الطابور\n" \
                                  f"4️⃣ احصل على المساعدة فوراً\n\n" \
                                  f"⚡ **نحن هنا لخدمتكم 24/7!**\n" \
                                  f"💪 **فريق دعم محترف ومتميز**"

            # إنشاء أزرار الإشعار
            markup = self._create_support_start_notification_markup()

            # إرسال الإشعار لجميع المشتركين
            notification_count = 0
            failed_count = 0

            for subscriber in all_subscribers:
                user_id = subscriber['user_id']
                try:
                    self.bot.send_message(
                        user_id,
                        support_start_message,
                        reply_markup=markup,
                        parse_mode='Markdown'
                    )
                    notification_count += 1

                    # تسجيل النشاط
                    self.db.log_activity(
                        user_id,
                        "support_system_start_notification",
                        "Notified of support system availability"
                    )

                except Exception as e:
                    failed_count += 1
                    logger.warning(f"Failed to notify subscriber {user_id} of support start: {e}")

            logger.info(f"Support system start notification: {notification_count} sent, {failed_count} failed")

            return {
                'success': True,
                'notified_count': notification_count,
                'failed_count': failed_count,
                'total_subscribers': len(all_subscribers)
            }

        except Exception as e:
            logger.error(f"Failed to notify subscribers of support system start: {e}")
            return {'success': False, 'error': str(e)}

    def _create_support_start_notification_markup(self):
        """إنشاء أزرار إشعار بدء نظام الدعم"""
        import telebot  # type: ignore
        markup = telebot.types.InlineKeyboardMarkup(row_width=2)

        markup.add(
            telebot.types.InlineKeyboardButton("🎧 طلب دعم", callback_data="contact_support"),
            telebot.types.InlineKeyboardButton("📊 حالة النظام", callback_data="check_support_status")
        )
        markup.add(
            telebot.types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status"),
            telebot.types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
        )

        return markup