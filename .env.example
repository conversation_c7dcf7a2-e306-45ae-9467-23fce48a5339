# إعدادات البوت الأساسية
API_TOKEN=7653972411:AAGU7XKhlc6cb_1YfOCBEvI_Sp3tvrZeX1A
ADMIN_ID=1449694969
# معرف الجروب (يج<PERSON> أن يبدأ بـ -100 للجروبات الكبيرة)
# للحصول على معرف الجروب: أضف البوت للجروب واجعله مشرف مع صلاحية دعوة المستخدمين
GROUP_ID=-1001234567890
WEBHOOK_URL=https://telegram-subscription-bot-97mn.onrender.com

# إعدادات قاعدة البيانات
DB_HOST=dpg-d0c70oh5pdvs73dasfug-a.oregon-postgres.render.com
DB_NAME=activationserver
DB_USER=activationserver_user
DB_PASS=CJPiZEU3QDQpK0ztajgevjHFzOU2V1GR
DB_PORT=5432

# بادئة الجداول لعزل النظام الجديد عن القديم
TABLE_PREFIX=advanced_

# إعدادات الاشتراك
SUBSCRIPTION_PRICE=100
SUBSCRIPTION_DURATION_DAYS=30
WARNING_DAYS_BEFORE_EXPIRY=3

# إعدادات الدفع
VODAFONE_CASH_NUMBER=01009046911
INSTAPAY_NUMBER=01009046911

# إعدادات النظام
CHECK_INTERVAL_MINUTES=60
NOTIFICATION_ENABLED=true
MAX_PAYMENT_ATTEMPTS=3
RATE_LIMIT_MESSAGES=10

# إعدادات السيرفر
PORT=5000
DEBUG=false

# إعدادات السجلات
LOG_LEVEL=INFO
LOG_FILE=bot.log
