"""
مدير روابط الدعوة الآمن
Secure Invite Links Manager
"""
import logging
import time
from datetime import datetime
import telebot  # type: ignore
from database import DatabaseManager
from config import Config

logger = logging.getLogger(__name__)

class InviteManager:
    """مدير روابط الدعوة مع الأمان المحسن"""

    def __init__(self, bot: telebot.TeleBot, db: DatabaseManager, config: Config):
        self.bot = bot
        self.db = db
        self.config = config

    def create_secure_invite_link(self, user_id):
        """إنشاء رابط دائم مخفي - أمان مطلق"""
        try:
            # التحقق من أن المستخدم مشترك نشط
            subscriber = self.db.get_subscriber(user_id)
            if not subscriber or not subscriber.get('is_active', False):
                logger.warning(f"Attempted to create permanent link for inactive user {user_id}")
                return None

            # فحص وجود رابط دائم نشط أولاً
            existing_link = self.get_active_permanent_link(user_id)
            if existing_link:
                # التحقق من صحة الرابط في تليجرام
                if self.validate_telegram_link(existing_link['link']):
                    logger.info(f"Returning existing valid permanent link for user {user_id}")
                    return existing_link
                else:
                    # الرابط غير صالح، إلغاؤه وإنشاء جديد
                    logger.warning(f"Existing link invalid for user {user_id}, creating new one")
                    self.revoke_user_links(user_id)

            # إنشاء رابط دعوة دائم (بدون expire_date)
            invite_link = self.bot.create_chat_invite_link(
                self.config.GROUP_ID,
                member_limit=1,  # رابط لشخص واحد فقط
                name=f"Permanent_{user_id}_{int(time.time())}"
                # بدون expire_date = رابط دائم
            )

            # حفظ الرابط الدائم في قاعدة البيانات (expire_time = 0 يعني دائم)
            link_id = self.db.save_invite_link(user_id, invite_link.invite_link, 0)

            if link_id:
                logger.info(f"Created new permanent hidden link {link_id} for user {user_id}")
                return {
                    'link': invite_link.invite_link,
                    'is_permanent': True,
                    'hidden': True,
                    'member_limit': 1
                }
            else:
                # إذا فشل حفظ الرابط، ألغِ الرابط من تليجرام
                try:
                    self.bot.revoke_chat_invite_link(self.config.GROUP_ID, invite_link.invite_link)
                except:
                    pass
                return None

        except Exception as e:
            logger.error(f"Failed to create permanent hidden link for user {user_id}: {e}")
            return None

    def get_active_permanent_link(self, user_id):
        """الحصول على الرابط الدائم النشط"""
        try:
            # البحث عن رابط دائم نشط (expire_time = 0)
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT invite_link FROM {self.db.invite_links_table}
                    WHERE user_id = %s AND is_active = TRUE AND expire_time = 0
                    ORDER BY created_at DESC LIMIT 1
                """, (user_id,))

                result = cursor.fetchone()

                if result:
                    return {
                        'link': result['invite_link'],
                        'is_permanent': True,
                        'hidden': True,
                        'member_limit': 1
                    }

                return None

        except Exception as e:
            logger.error(f"Failed to get active permanent link for user {user_id}: {e}")
            return None

    def revoke_user_links(self, user_id):
        """إلغاء جميع روابط الدعوة للمستخدم"""
        try:
            # الحصول على الروابط النشطة
            active_links = self.db.get_active_invite_links(user_id)

            revoked_count = 0
            for link_data in active_links:
                try:
                    # إلغاء الرابط من تليجرام
                    self.bot.revoke_chat_invite_link(self.config.GROUP_ID, link_data['invite_link'])
                    revoked_count += 1
                except Exception as e:
                    logger.warning(f"Failed to revoke invite link {link_data['invite_link']}: {e}")

            # تحديث قاعدة البيانات
            db_revoked = self.db.revoke_user_invite_links(user_id)

            logger.info(f"Revoked {revoked_count} telegram links and {db_revoked} database entries for user {user_id}")
            return revoked_count

        except Exception as e:
            logger.error(f"Failed to revoke user links for {user_id}: {e}")
            return 0

    def cleanup_expired_links(self):
        """تنظيف الروابط المنتهية الصلاحية"""
        try:
            # تنظيف قاعدة البيانات
            cleaned_count = self.db.cleanup_expired_invite_links()

            logger.info(f"Cleaned up {cleaned_count} expired invite links from database")
            return cleaned_count

        except Exception as e:
            logger.error(f"Failed to cleanup expired links: {e}")
            return 0

    def handle_user_removal(self, user_id, reason="subscription_ended"):
        """معالجة إزالة المستخدم وإلغاء روابطه"""
        try:
            # إلغاء جميع روابط الدعوة
            revoked_count = self.revoke_user_links(user_id)

            # محاولة إزالة المستخدم من الجروب
            try:
                self.bot.ban_chat_member(self.config.GROUP_ID, user_id)
                self.bot.unban_chat_member(self.config.GROUP_ID, user_id)
                group_removed = True
            except Exception as e:
                logger.warning(f"Failed to remove user {user_id} from group: {e}")
                group_removed = False

            # تسجيل النشاط
            self.db.log_activity(
                user_id,
                "user_removed",
                f"Reason: {reason}, Links revoked: {revoked_count}, Group removed: {group_removed}"
            )

            return {
                'links_revoked': revoked_count,
                'group_removed': group_removed
            }

        except Exception as e:
            logger.error(f"Failed to handle user removal for {user_id}: {e}")
            return {'links_revoked': 0, 'group_removed': False}

    def get_link_info(self, user_id):
        """الحصول على معلومات الرابط الدائم"""
        try:
            active_links = self.db.get_active_invite_links(user_id)

            if not active_links:
                return None

            # البحث عن رابط دائم أولاً (expire_time = 0)
            permanent_link = None
            for link in active_links:
                if link['expire_time'] == 0:
                    permanent_link = link
                    break

            if permanent_link:
                return {
                    'link': permanent_link['invite_link'],
                    'expire_time': 0,
                    'created_at': permanent_link['created_at'],
                    'is_expired': False,  # الرابط الدائم لا ينتهي أبداً
                    'is_permanent': True
                }

            # إذا لم يوجد رابط دائم، أحدث رابط نشط
            latest_link = max(active_links, key=lambda x: x['created_at'])

            return {
                'link': latest_link['invite_link'],
                'expire_time': latest_link['expire_time'],
                'created_at': latest_link['created_at'],
                'is_expired': latest_link['expire_time'] <= int(datetime.now().timestamp()),
                'is_permanent': False
            }

        except Exception as e:
            logger.error(f"Failed to get link info for user {user_id}: {e}")
            return None

    def validate_user_access(self, user_id):
        """التحقق من صحة وصول المستخدم"""
        try:
            subscriber = self.db.get_subscriber(user_id)

            if not subscriber:
                return False, "المستخدم غير موجود في النظام"

            if not subscriber.get('is_active', False):
                return False, "الاشتراك غير نشط"

            # التحقق من انتهاء الاشتراك
            if subscriber.get('expire_time'):
                if subscriber['expire_time'] <= int(datetime.now().timestamp()):
                    return False, "انتهت صلاحية الاشتراك"

            return True, "المستخدم مخول للوصول"

        except Exception as e:
            logger.error(f"Failed to validate user access for {user_id}: {e}")
            return False, "خطأ في التحقق من الصلاحية"

    def validate_telegram_link(self, invite_link):
        """التحقق من صحة رابط الدعوة في تليجرام"""
        try:
            # محاولة الحصول على معلومات الرابط
            link_info = self.bot.get_chat_invite_link(self.config.GROUP_ID, invite_link)

            # التحقق من أن الرابط نشط وصالح
            if link_info and not link_info.is_revoked:
                # التحقق من أن الرابط لم يستنفد استخداماته
                if link_info.member_limit is None or link_info.member_limit > 0:
                    return True

            return False

        except Exception as e:
            logger.warning(f"Failed to validate telegram link {invite_link}: {e}")
            return False
