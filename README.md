# 🤖 نظام البوت المتقدم للاشتراكات الشهرية

نظام متكامل ومتقدم لإدارة الاشتراكات الشهرية عبر تليجرام مع ميزات احترافية وأمان عالي.

## ✨ المميزات الرئيسية

### 🎯 للمستخدمين
- **واجهة تفاعلية حديثة** مع أزرار inline
- **طرق دفع متعددة** (فودافون كاش، انستاباي، تحويل بنكي)
- **تنبيهات تلقائية** قبل انتهاء الاشتراك بـ 3 أيام
- **تتبع حالة الاشتراك** في الوقت الفعلي
- **تاريخ المدفوعات** الكامل
- **دعم فني متميز**
- **إعلانات مهمة** من الإدارة

### 🛡️ للإدارة
- **لوحة تحكم شاملة** مع إحصائيات متقدمة
- **إدارة المدفوعات** بنظام موافقة/رفض
- **تقارير مالية** يومية وشهرية
- **إدارة المشتركين** المتقدمة
- **نظام تنبيهات** للإدارة
- **نسخ احتياطية** تلقائية

### 🔧 تقنية متقدمة
- **قاعدة بيانات محسنة** مع فهارس للأداء
- **نظام أمان متقدم** ضد الهجمات
- **معالجة أخطاء شاملة**
- **نظام سجلات متطور**
- **تحديد معدل الطلبات** لمنع الإساءة
- **صحة النظام** والمراقبة

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام
```bash
Python 3.8+
PostgreSQL 12+
```

### 2. تحميل المشروع
```bash
git clone https://github.com/your-username/telegram-subscription-bot.git
cd telegram-subscription-bot
```

### 3. تثبيت المكتبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```sql
CREATE DATABASE subscription_bot;
CREATE USER bot_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE subscription_bot TO bot_user;
```

### 5. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعديل الملف .env بالقيم الصحيحة

# مهم: تأكد من تعيين TABLE_PREFIX لعزل النظام الجديد
TABLE_PREFIX=advanced_
```

### 6. فحص النظام (اختياري)
```bash
# فحص شامل للنظام
python system_diagnostics.py

# إصلاح سريع للمشاكل الشائعة
python quick_fix.py

# فحص العزل
python check_isolation.py
```

### 7. تشغيل النظام
```bash
python server.py
```

## 🔧 الإعداد على Render

### 1. إنشاء خدمة جديدة
- اذهب إلى [Render.com](https://render.com)
- اختر "New Web Service"
- اربط مستودع GitHub الخاص بك

### 2. إعدادات البناء
```
Build Command: pip install -r requirements.txt
Start Command: python server.py
```

### 3. متغيرات البيئة
أضف جميع المتغيرات من ملف `.env.example` في إعدادات Render

### 4. قاعدة البيانات
- أنشئ PostgreSQL database في Render
- انسخ connection string إلى متغيرات البيئة

## 🔒 عزل النظام الجديد

### لماذا العزل؟
هذا النظام مصمم ليعمل بشكل منفصل تماماً عن أي نظام موجود مسبقاً في نفس قاعدة البيانات.

### كيفية العزل:
- **بادئة الجداول**: جميع الجداول تبدأ بـ `advanced_`
- **عدم التداخل**: لا يؤثر على الجداول الموجودة
- **أمان كامل**: يمكن تشغيل النظامين معاً

### الجداول الجديدة:
```sql
advanced_subscribers      -- بدلاً من subscribers
advanced_payments         -- جدول جديد للمدفوعات
advanced_notifications    -- جدول جديد للتنبيهات
advanced_statistics       -- جدول جديد للإحصائيات
advanced_activity_logs    -- جدول جديد للسجلات
```

### إعداد البادئة:
```env
# في ملف .env
TABLE_PREFIX=advanced_
```

راجع [دليل العزل التفصيلي](ISOLATION_GUIDE.md) للمزيد من المعلومات.

## 📊 هيكل قاعدة البيانات

### جدول المشتركين (advanced_subscribers)
```sql
- user_id: معرف المستخدم الفريد
- username: اسم المستخدم
- first_name, last_name: الاسم الكامل
- join_time: وقت الاشتراك
- expire_time: وقت انتهاء الاشتراك
- is_active: حالة النشاط
- subscription_count: عدد مرات التجديد
- total_paid: إجمالي المدفوعات
```

### جدول المدفوعات (advanced_payments)
```sql
- id: معرف الدفعة
- user_id: معرف المستخدم
- amount: المبلغ
- payment_method: طريقة الدفع
- status: حالة الدفعة
- created_at: وقت الإنشاء
```

### جدول التنبيهات (advanced_notifications)
```sql
- id: معرف التنبيه
- user_id: معرف المستخدم
- notification_type: نوع التنبيه
- message: نص الرسالة
- sent_at: وقت الإرسال
```

## 🎛️ استخدام لوحة الإدارة

### الوصول للوحة الإدارة
1. ابدأ محادثة مع البوت
2. اضغط `/start`
3. اضغط "🎛️ لوحة الإدارة" (تظهر للمسؤول فقط)

### الوظائف المتاحة
- **📊 الإحصائيات**: عرض إحصائيات شاملة
- **👥 المشتركين**: إدارة قائمة المشتركين
- **💰 المدفوعات**: مراجعة وموافقة المدفوعات
- **📈 التقارير**: تقارير مالية مفصلة
- **⚙️ الإعدادات**: إعدادات النظام
- **📢 رسالة جماعية**: إرسال رسائل لجميع المشتركين

## 🔔 نظام التنبيهات

### تنبيهات المستخدمين
- **تنبيه 3 أيام** قبل انتهاء الاشتراك
- **تنبيه يوم واحد** قبل الانتهاء
- **تنبيه انتهاء** الاشتراك
- **تأكيد الدفع** عند الموافقة

### تنبيهات الإدارة
- **تقرير يومي** في الساعة 9 صباحاً
- **إشعار دفعة جديدة** فوري
- **تنبيه مشكلة** في النظام
- **إحصائيات أسبوعية**

## 🛡️ الأمان والحماية

### حماية من الهجمات
- **تحديد معدل الطلبات** لمنع spam
- **تنظيف المدخلات** من الأكواد الضارة
- **تشفير البيانات** الحساسة
- **مراقبة النشاط** المشبوه

### النسخ الاحتياطية
- **نسخ تلقائية** أسبوعية
- **نسخ عند الإيقاف** الطارئ
- **استرداد سريع** للبيانات

## 📝 السجلات والمراقبة

### أنواع السجلات
- **سجلات النظام**: أخطاء وتحذيرات
- **سجلات المستخدمين**: نشاط المستخدمين
- **سجلات المدفوعات**: تتبع المعاملات
- **سجلات الأداء**: مراقبة الأداء

### مراقبة الصحة
- **فحص قاعدة البيانات**: كل دقيقة
- **فحص البوت**: كل 5 دقائق
- **تقرير الحالة**: `/health` endpoint

## 🔧 التخصيص والتطوير

### إضافة طرق دفع جديدة
1. عدّل `payment_handler.py`
2. أضف الطريقة الجديدة في `config.py`
3. حدّث واجهة المستخدم

### تخصيص الرسائل
عدّل قسم `MESSAGES` في `config.py`

### إضافة ميزات جديدة
1. أنشئ ملف جديد في المجلد الرئيسي
2. استورد الوحدة في `server.py`
3. أضف المعالجات المطلوبة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### 🚫 **"group is inaccessible"**
**السبب:** البوت غير قادر على الوصول للجروب
**الحل:**
```bash
# 1. تحقق من معرف الجروب
/get_chat_id

# 2. فحص حالة البوت
/check_bot_status

# 3. تأكد من أن البوت مشرف في الجروب
# 4. فعّل صلاحية "دعوة المستخدمين"
```

#### 🗄️ **"support_queue_table not found"**
**السبب:** جداول الدعم غير موجودة
**الحل:**
```bash
# تم إصلاح هذه المشكلة في آخر تحديث
# أعد تشغيل البوت لتطبيق التحديثات
python server.py
```

#### 🔗 **مشاكل روابط الدعوة**
**السبب:** البوت ليس له صلاحيات كافية
**الحل:**
1. تأكد من أن البوت مشرف
2. فعّل صلاحية "إدارة الدردشة"
3. فعّل صلاحية "دعوة المستخدمين"

#### 🔌 **مشاكل عامة**
1. **خطأ قاعدة البيانات**: تحقق من connection string
2. **webhook لا يعمل**: تأكد من HTTPS في الرابط
3. **البوت لا يرد**: تحقق من API token
4. **مشاكل الأذونات**: تأكد من أذونات البوت في الجروب

### ملفات السجلات
```bash
# عرض السجلات
tail -f logs/bot.log

# البحث عن أخطاء
grep ERROR logs/bot.log
```

## 📞 الدعم والمساهمة

### الإبلاغ عن مشاكل
افتح issue جديد في GitHub مع:
- وصف المشكلة
- خطوات إعادة الإنتاج
- رسائل الخطأ
- إعدادات النظام

### المساهمة
1. Fork المشروع
2. أنشئ branch جديد
3. اعمل التغييرات
4. اختبر التغييرات
5. أرسل Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- [pyTelegramBotAPI](https://github.com/eternnoir/pyTelegramBotAPI)
- [Flask](https://flask.palletsprojects.com/)
- [PostgreSQL](https://www.postgresql.org/)
- [APScheduler](https://apscheduler.readthedocs.io/)

---

**تم تطويره بـ ❤️ للمجتمع العربي**
