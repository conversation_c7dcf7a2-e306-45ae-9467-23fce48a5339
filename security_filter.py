"""
نظام الفلترة الأمنية المتقدم
Advanced Security Filter System
"""
import logging
from datetime import datetime
from functools import wraps
import telebot  # type: ignore
from database import DatabaseManager

logger = logging.getLogger(__name__)

class SecurityFilter:
    """نظام فلترة أمني شامل لحماية البوت"""
    
    def __init__(self, db: DatabaseManager, bot: telebot.TeleBot):
        self.db = db
        self.bot = bot
        
    def check_user_access(self, user_id, action="general"):
        """فحص شامل لصلاحية وصول المستخدم"""
        try:
            # التحقق من قائمة الحظر
            blacklist_info = self.db.is_user_blacklisted(user_id)
            
            if blacklist_info:
                logger.warning(f"Blocked access for blacklisted user {user_id}, action: {action}")
                return False, {
                    'blocked': True,
                    'reason': blacklist_info['reason'],
                    'banned_at': blacklist_info['banned_at'],
                    'ban_type': blacklist_info['ban_type'],
                    'notes': blacklist_info.get('notes', '')
                }
            
            # المستخدم مسموح له بالوصول
            return True, {'blocked': False}
            
        except Exception as e:
            logger.error(f"Error checking user access for {user_id}: {e}")
            # في حالة الخطأ، نسمح بالوصول لتجنب حظر المستخدمين بالخطأ
            return True, {'blocked': False, 'error': str(e)}
    
    def send_blocked_message(self, user_id, block_info):
        """إرسال رسالة للمستخدم المحظور"""
        try:
            ban_date = block_info['banned_at'].strftime('%Y-%m-%d %H:%M') if block_info.get('banned_at') else 'غير محدد'
            
            message = f"🚫 **تم حظرك من استخدام هذا البوت**\n\n" \
                     f"📝 **السبب:** {block_info['reason']}\n" \
                     f"📅 **تاريخ الحظر:** {ban_date}\n" \
                     f"🔒 **نوع الحظر:** {block_info['ban_type']}\n\n"
            
            if block_info.get('notes'):
                message += f"📋 **ملاحظات:** {block_info['notes']}\n\n"
            
            message += f"📞 **للاستفسار:** راسل الإدارة مباشرة\n" \
                      f"⚠️ **تنبيه:** محاولة تجاوز الحظر قد تؤدي لحظر دائم"
            
            self.bot.send_message(user_id, message, parse_mode='Markdown')
            
            # تسجيل محاولة الوصول
            self.db.log_activity(user_id, "blocked_access_attempt", f"Reason: {block_info['reason']}")
            
        except Exception as e:
            logger.error(f"Failed to send blocked message to user {user_id}: {e}")
    
    def security_check_decorator(self, action="general"):
        """ديكوريتر للفحص الأمني التلقائي"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # استخراج user_id من المعاملات
                user_id = None
                
                # البحث في المعاملات المختلفة
                for arg in args:
                    if hasattr(arg, 'from_user') and hasattr(arg.from_user, 'id'):
                        user_id = arg.from_user.id
                        break
                    elif hasattr(arg, 'message') and hasattr(arg.message, 'from_user'):
                        user_id = arg.message.from_user.id
                        break
                
                if not user_id:
                    # إذا لم نجد user_id، نسمح بالتنفيذ
                    return func(*args, **kwargs)
                
                # فحص الأمان
                is_allowed, block_info = self.check_user_access(user_id, action)
                
                if not is_allowed:
                    # المستخدم محظور
                    self.send_blocked_message(user_id, block_info)
                    return None
                
                # المستخدم مسموح له، تنفيذ الدالة
                return func(*args, **kwargs)
            
            return wrapper
        return decorator

def create_security_filter(db_manager, bot):
    """إنشاء مرشح أمني عام"""
    return SecurityFilter(db_manager, bot)

# ديكوريتر سريع للاستخدام
def security_check(action="general"):
    """ديكوريتر سريع للفحص الأمني"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # هذا سيتم تحديثه عند إنشاء المرشح الأمني
            return func(*args, **kwargs)
        return wrapper
    return decorator
