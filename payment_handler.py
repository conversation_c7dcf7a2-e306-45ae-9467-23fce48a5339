"""
معالج المدفوعات المتقدم
Advanced Payment Handler
"""
import logging
import re
from datetime import datetime
import telebot  # type: ignore
from telebot import types  # type: ignore
from database import DatabaseManager
from config import Config

logger = logging.getLogger(__name__)

class PaymentHandler:
    def __init__(self, bot: telebot.TeleBot):
        self.bot = bot
        self.db = DatabaseManager()
        self.config = Config()

        # أنماط التحقق من صحة البيانات
        self.vodafone_pattern = re.compile(r'^01[0-2]\d{8}$')
        self.transaction_pattern = re.compile(r'^[A-Z0-9]{6,20}$')

    def show_payment_methods(self, chat_id):
        """عرض طرق الدفع المتاحة"""
        markup = types.InlineKeyboardMarkup(row_width=1)

        # طرق الدفع المختلفة
        markup.add(
            types.InlineKeyboardButton("📱 فودافون كاش", callback_data="payment_vodafone"),
            types.InlineKeyboardButton("💸 انستاباي", callback_data="payment_instapay")
        )
        markup.add(
            types.InlineKeyboardButton("💳 بطاقة ائتمان", callback_data="payment_card")
        )

        markup.add(types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu"))

        # التحقق من حالة المستخدم لتخصيص الرسالة
        subscriber = self.db.get_subscriber(chat_id)
        is_renewal = subscriber and subscriber.get('is_active', False)

        if is_renewal:
            message = f"🔄 تجديد الاشتراك\n\n" \
                     f"💵 سعر التجديد: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                     f"⏰ مدة إضافية: {self.config.SUBSCRIPTION_DURATION_DAYS} يوم\n" \
                     f"🎁 مكافأة الولاء: خصم 5% على التجديد التالي\n\n" \
                     f"اختر طريقة الدفع المناسبة لك:"
        else:
            message = f"💰 طرق الدفع المتاحة\n\n" \
                     f"💵 سعر الاشتراك الشهري: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                     f"⏰ مدة الاشتراك: {self.config.SUBSCRIPTION_DURATION_DAYS} يوم\n" \
                     f"🎁 عرض المشتركين الجدد: دعم فني مجاني لمدة شهر\n\n" \
                     f"اختر طريقة الدفع المناسبة لك:"

        try:
            self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')
            return True
        except Exception as e:
            logger.error(f"Failed to show payment methods: {e}")
            return False

    def show_vodafone_payment(self, chat_id):
        """عرض تعليمات الدفع عبر فودافون كاش"""
        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("📸 أرسل إثبات الدفع", callback_data="upload_proof_vodafone"),
            types.InlineKeyboardButton("🔙 طرق دفع أخرى", callback_data="payment_methods")
        )

        message = f"📱 **الدفع عبر فودافون كاش**\n\n" \
                 f"💰 المبلغ المطلوب: **{self.config.SUBSCRIPTION_PRICE} جنيه**\n" \
                 f"📞 رقم فودافون كاش: **{self.config.VODAFONE_CASH_NUMBER}**\n\n" \
                 f"📋 **خطوات الدفع:**\n" \
                 f"1️⃣ افتح تطبيق فودافون كاش\n" \
                 f"2️⃣ اختر 'تحويل فلوس'\n" \
                 f"3️⃣ أدخل الرقم: {self.config.VODAFONE_CASH_NUMBER}\n" \
                 f"4️⃣ أدخل المبلغ: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                 f"5️⃣ أكمل العملية\n" \
                 f"6️⃣ التقط صورة لرسالة التأكيد\n\n" \
                 f"⚠️ **مهم:** احتفظ برقم العملية وأرسل صورة واضحة لإثبات الدفع"

        self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        # تسجيل اختيار طريقة الدفع
        self.db.log_activity(chat_id, "payment_method_selected", "Vodafone Cash")

    def show_instapay_payment(self, chat_id):
        """عرض تعليمات الدفع عبر انستاباي"""
        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("📸 أرسل إثبات الدفع", callback_data="upload_proof_instapay"),
            types.InlineKeyboardButton("🔙 طرق دفع أخرى", callback_data="payment_methods")
        )

        message = f"💸 **الدفع عبر انستاباي**\n\n" \
                 f"💰 المبلغ المطلوب: **{self.config.SUBSCRIPTION_PRICE} جنيه**\n" \
                 f"📞 رقم انستاباي: **{self.config.INSTAPAY_NUMBER}**\n\n" \
                 f"📋 **خطوات الدفع:**\n" \
                 f"1️⃣ افتح تطبيق البنك الخاص بك\n" \
                 f"2️⃣ اختر 'انستاباي'\n" \
                 f"3️⃣ أدخل الرقم: {self.config.INSTAPAY_NUMBER}\n" \
                 f"4️⃣ أدخل المبلغ: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                 f"5️⃣ أكمل العملية\n" \
                 f"6️⃣ التقط صورة لرسالة التأكيد\n\n" \
                 f"⚠️ **مهم:** احتفظ برقم العملية وأرسل صورة واضحة لإثبات الدفع"

        self.bot.send_message(chat_id, message, reply_markup=markup, parse_mode='Markdown')

        # تسجيل اختيار طريقة الدفع
        self.db.log_activity(chat_id, "payment_method_selected", "InstaPay")



    def handle_payment_proof(self, message, payment_method=None):
        """معالجة إثبات الدفع المرسل من المستخدم"""
        user_id = message.from_user.id
        username = message.from_user.username
        first_name = message.from_user.first_name

        try:
            # التحقق من وجود المستخدم في جدول المشتركين أولاً
            subscriber = self.db.get_subscriber(user_id)

            # إذا لم يكن موجود، إنشاء سجل مؤقت له
            if not subscriber:
                logger.info(f"Creating temporary subscriber record for user {user_id}")

                # إنشاء سجل مشترك مؤقت (غير نشط)
                with self.db.get_db_cursor() as cursor:
                    cursor.execute(f"""
                        INSERT INTO {self.db.subscribers_table}
                        (user_id, username, first_name, join_time, expire_time, is_active, subscription_count, total_paid)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (user_id) DO NOTHING
                    """, (
                        user_id,
                        username or 'غير محدد',
                        first_name or 'غير محدد',
                        0,  # join_time = 0 (مؤقت)
                        0,  # expire_time = 0 (مؤقت)
                        False,  # غير نشط
                        0,  # عدد الاشتراكات
                        0.0  # إجمالي المدفوعات
                    ))

            # الآن إنشاء سجل دفعة جديد
            payment_id = self.db.add_payment(
                user_id=user_id,
                amount=self.config.SUBSCRIPTION_PRICE,
                payment_method=payment_method,
                message_id=message.message_id
            )

            if not payment_id:
                self.bot.send_message(user_id, "❌ حدث خطأ في تسجيل الدفعة. حاول مرة أخرى.")
                return False

            # إرسال رسالة تأكيد محسنة للمستخدم
            is_renewal = subscriber and subscriber.get('is_active', False)

            if is_renewal:
                confirmation_msg = f"✅ تم استلام إثبات تجديد الاشتراك\n\n" \
                                 f"🆔 رقم الدفعة: #{payment_id}\n" \
                                 f"💰 المبلغ: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                                 f"💳 طريقة الدفع: {payment_method or 'غير محدد'}\n" \
                                 f"⏰ وقت الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n" \
                                 f"🔄 جاري مراجعة التجديد من قبل الإدارة...\n" \
                                 f"⚡ التجديد سيتم إضافته لاشتراكك الحالي\n" \
                                 f"⏱️ عادة ما تتم الموافقة خلال دقائق قليلة\n\n" \
                                 f"🎁 مكافأة الولاء: خصم 5% على التجديد التالي\n" \
                                 f"📞 للاستفسار: راسل الإدارة مباشرة"
            else:
                confirmation_msg = f"✅ تم استلام إثبات الدفع\n\n" \
                                 f"🆔 رقم الدفعة: #{payment_id}\n" \
                                 f"💰 المبلغ: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                                 f"💳 طريقة الدفع: {payment_method or 'غير محدد'}\n" \
                                 f"⏰ وقت الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n" \
                                 f"🔄 جاري مراجعة الدفعة من قبل الإدارة...\n" \
                                 f"🎯 بعد الموافقة ستحصل على رابط الجروب الحصري\n" \
                                 f"⏱️ عادة ما تتم الموافقة خلال دقائق قليلة\n\n" \
                                 f"🎁 مرحباً بك في عائلة المشتركين!\n" \
                                 f"📞 للاستفسار: راسل الإدارة مباشرة"

            # إضافة أزرار تفاعلية
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📄 حالة الدفعة", callback_data="payment_history"),
                types.InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="main_menu")
            )

            self.bot.send_message(user_id, confirmation_msg, reply_markup=markup)

            # إرسال إشعار للإدارة مع تفاصيل الدفعة
            admin_msg = f"💰 دفعة جديدة تحتاج مراجعة\n\n" \
                       f"🆔 رقم الدفعة: #{payment_id}\n" \
                       f"👤 المستخدم: {first_name} (@{username or 'غير محدد'})\n" \
                       f"🆔 معرف المستخدم: {user_id}\n" \
                       f"💰 المبلغ: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                       f"💳 طريقة الدفع: {payment_method or 'غير محدد'}\n" \
                       f"⏰ وقت الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M')}"

            # إنشاء أزرار الموافقة والرفض
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("✅ موافقة", callback_data=f"approve_payment_{payment_id}"),
                types.InlineKeyboardButton("❌ رفض", callback_data=f"reject_payment_{payment_id}")
            )
            markup.add(
                types.InlineKeyboardButton("👤 معلومات المستخدم", callback_data=f"user_info_{user_id}"),
                types.InlineKeyboardButton("📊 لوحة الإدارة", callback_data="admin_menu")
            )

            # إرسال الرسالة للإدارة
            self.bot.send_message(self.config.ADMIN_ID, admin_msg, reply_markup=markup)

            # إعادة إرسال إثبات الدفع للإدارة مع تفاصيل
            try:
                # إرسال تفاصيل الدفعة أولاً
                proof_details = f"📋 تفاصيل إثبات الدفع:\n\n" \
                              f"🆔 رقم الدفعة: #{payment_id}\n" \
                              f"👤 المستخدم: {first_name} (@{username or 'غير محدد'})\n" \
                              f"🆔 معرف المستخدم: {user_id}\n" \
                              f"💰 المبلغ: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
                              f"💳 طريقة الدفع: {payment_method or 'غير محدد'}\n" \
                              f"⏰ وقت الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n" \
                              f"📎 إثبات الدفع المرفق أدناه:"

                self.bot.send_message(self.config.ADMIN_ID, proof_details)

                # ثم إرسال إثبات الدفع
                if message.content_type == 'photo':
                    # إرسال الصورة مع تفاصيل
                    self.bot.send_photo(self.config.ADMIN_ID, message.photo[-1].file_id,
                                      caption=f"📸 صورة إثبات الدفع للدفعة #{payment_id}")
                elif message.content_type == 'text':
                    self.bot.send_message(self.config.ADMIN_ID,
                                        f"📝 نص إثبات الدفع:\n\n{message.text}")
                elif message.content_type == 'document':
                    # إرسال المستند مع تفاصيل
                    self.bot.send_document(self.config.ADMIN_ID, message.document.file_id,
                                         caption=f"📄 مستند إثبات الدفع للدفعة #{payment_id}")
                else:
                    # أي نوع آخر - استخدام forward
                    self.bot.forward_message(self.config.ADMIN_ID, user_id, message.message_id)

            except Exception as e:
                logger.error(f"Failed to forward payment proof to admin: {e}")
                # في حالة الفشل، إرسال رسالة تنبيه
                self.bot.send_message(self.config.ADMIN_ID,
                                    f"⚠️ فشل في إرسال إثبات الدفع للدفعة #{payment_id}\n"
                                    f"👤 من المستخدم: {first_name} ({user_id})")

            # تسجيل النشاط
            self.db.log_activity(user_id, "payment_proof_submitted",
                               f"Payment proof submitted for payment #{payment_id}")

            logger.info(f"Payment proof received from user {user_id}, payment ID: {payment_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to handle payment proof from user {user_id}: {e}")
            self.bot.send_message(user_id,
                                "❌ حدث خطأ في معالجة إثبات الدفع. يرجى المحاولة مرة أخرى.")
            return False

    def validate_transaction_id(self, transaction_id, payment_method):
        """التحقق من صحة رقم العملية"""
        if not transaction_id:
            return False

        transaction_id = transaction_id.strip().upper()

        if payment_method in ['vodafone', 'instapay']:
            return self.transaction_pattern.match(transaction_id) is not None

        return len(transaction_id) >= 6

    def get_payment_status(self, user_id):
        """الحصول على حالة المدفوعات للمستخدم"""
        try:
            with self.db.get_db_cursor() as cursor:
                cursor.execute(f"""
                    SELECT * FROM {self.db.payments_table}
                    WHERE user_id = %s
                    ORDER BY created_at DESC
                    LIMIT 5
                """, (user_id,))

                payments = cursor.fetchall()

                if not payments:
                    return "📄 لا توجد مدفوعات مسجلة"

                message = "📄 حالة مدفوعاتك:\n\n"

                for payment in payments:
                    status_emoji = {
                        'pending': '⏳',
                        'approved': '✅',
                        'rejected': '❌'
                    }.get(payment['status'], '❓')

                    status_text = {
                        'pending': 'قيد المراجعة',
                        'approved': 'مقبولة',
                        'rejected': 'مرفوضة'
                    }.get(payment['status'], 'غير معروف')

                    created_at = payment['created_at'].strftime('%Y-%m-%d %H:%M')

                    message += f"{status_emoji} دفعة #{payment['id']}\n" \
                              f"💰 المبلغ: {payment['amount']} جنيه\n" \
                              f"📅 التاريخ: {created_at}\n" \
                              f"📊 الحالة: {status_text}\n"

                    if payment['notes']:
                        message += f"📝 ملاحظات: {payment['notes']}\n"

                    message += "\n"

                return message

        except Exception as e:
            logger.error(f"Failed to get payment status for user {user_id}: {e}")
            return "❌ حدث خطأ في جلب حالة المدفوعات"

    def send_payment_reminder(self, user_id):
        """إرسال تذكير بالدفع"""
        try:
            subscriber = self.db.get_subscriber(user_id)
            if not subscriber:
                return False

            days_left = max(0, int((subscriber['expire_time'] - datetime.now().timestamp()) / 86400))

            if days_left <= 0:
                message = "🚨 انتهى اشتراكك!\n\n" \
                         "📛 تم إنهاء اشتراكك وإزالتك من الجروب\n" \
                         "🔄 يمكنك تجديد الاشتراك الآن للعودة"
            else:
                message = f"⚠️ تذكير: سينتهي اشتراكك خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}\n\n" \
                         f"💡 جدد اشتراكك الآن لتجنب انقطاع الخدمة"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("💳 تجديد الاشتراك", callback_data="payment_methods"))

            self.bot.send_message(user_id, message, reply_markup=markup)
            return True

        except Exception as e:
            logger.error(f"Failed to send payment reminder to user {user_id}: {e}")
            return False
