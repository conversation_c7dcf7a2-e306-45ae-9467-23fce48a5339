"""
نظام التنبيهات المتقدم
Advanced Notification System
"""
import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler  # type: ignore
from apscheduler.triggers.interval import IntervalTrigger  # type: ignore
from apscheduler.triggers.cron import CronTrigger  # type: ignore
from telebot import types  # type: ignore
import telebot  # type: ignore
from database import DatabaseManager
from config import Config

logger = logging.getLogger(__name__)

class NotificationManager:
    def __init__(self, bot: telebot.TeleBot):
        self.bot = bot
        self.db = DatabaseManager()
        self.config = Config()
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()

        # جدولة المهام
        self.schedule_tasks()

    def schedule_tasks(self):
        """جدولة المهام التلقائية"""
        try:
            # فحص التنبيهات كل ساعة
            self.scheduler.add_job(
                func=self.check_expiring_subscriptions,
                trigger=IntervalTrigger(hours=1),
                id='check_expiring',
                name='Check Expiring Subscriptions',
                replace_existing=True
            )

            # فحص الاشتراكات المنتهية كل 30 دقيقة
            self.scheduler.add_job(
                func=self.check_expired_subscriptions,
                trigger=IntervalTrigger(minutes=30),
                id='check_expired',
                name='Check Expired Subscriptions',
                replace_existing=True
            )

            # تقرير يومي للإدارة في الساعة 9 صباحاً
            self.scheduler.add_job(
                func=self.send_daily_report,
                trigger=CronTrigger(hour=9, minute=0),
                id='daily_report',
                name='Daily Admin Report',
                replace_existing=True
            )

            # تنظيف قاعدة البيانات أسبوعياً
            self.scheduler.add_job(
                func=self.cleanup_database,
                trigger=CronTrigger(day_of_week=0, hour=2, minute=0),
                id='weekly_cleanup',
                name='Weekly Database Cleanup',
                replace_existing=True
            )

            logger.info("Notification tasks scheduled successfully")

        except Exception as e:
            logger.error(f"Failed to schedule notification tasks: {e}")

    def start(self):
        """بدء تشغيل نظام التنبيهات"""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("Notification system started successfully")
            else:
                logger.info("Notification system is already running")
        except Exception as e:
            logger.error(f"Failed to start notification system: {e}")
            raise

    def check_expiring_subscriptions(self):
        """فحص الاشتراكات المنتهية قريباً وإرسال تنبيهات"""
        try:
            expiring_subscribers = self.db.get_expiring_subscribers(self.config.WARNING_DAYS_BEFORE_EXPIRY)

            for subscriber in expiring_subscribers:
                user_id = subscriber['user_id']
                expire_time = subscriber['expire_time']

                # حساب الأيام المتبقية
                days_left = max(0, int((expire_time - datetime.now().timestamp()) / 86400))

                # إنشاء رسالة التنبيه
                if days_left == 3:
                    message = self.create_expiry_warning_message(days_left, "🔔")
                elif days_left == 2:
                    message = self.create_expiry_warning_message(days_left, "⚠️")
                elif days_left == 1:
                    message = self.create_expiry_warning_message(days_left, "🚨")
                else:
                    continue

                # إرسال التنبيه
                self.send_notification(user_id, message, "expiry_warning")

                # تسجيل التنبيه في قاعدة البيانات
                self.db.add_notification(user_id, "expiry_warning", message)

            logger.info(f"Checked {len(expiring_subscribers)} expiring subscriptions")

        except Exception as e:
            logger.error(f"Failed to check expiring subscriptions: {e}")

    def check_expired_subscriptions(self):
        """فحص الاشتراكات المنتهية وإزالة المشتركين"""
        try:
            expired_subscribers = self.db.get_expired_subscribers()

            for subscriber in expired_subscribers:
                user_id = subscriber['user_id']
                username = subscriber.get('username', 'غير محدد')

                # إرسال رسالة انتهاء الاشتراك
                expiry_message = self.create_subscription_expired_message()
                self.send_notification(user_id, expiry_message, "subscription_expired")

                # إزالة المشترك من الجروب مع إلغاء روابط الدعوة
                try:
                    # استخدام مدير روابط الدعوة لإزالة المستخدم بأمان
                    from invite_manager import InviteManager
                    invite_manager = InviteManager(self.bot, self.db, self.config)

                    removal_result = invite_manager.handle_user_removal(user_id, "subscription_expired")

                    logger.info(f"Removed expired user {user_id} from group, revoked {removal_result['links_revoked']} invite links")

                    # إرسال تنبيه للإدارة مع تفاصيل الأمان
                    admin_message = f"🚫 تم إزالة المشترك المنتهي الصلاحية:\n" \
                                  f"👤 المعرف: {user_id}\n" \
                                  f"📝 اسم المستخدم: @{username}\n" \
                                  f"⏰ وقت الإزالة: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n" \
                                  f"🔒 تم إلغاء {removal_result['links_revoked']} رابط دعوة\n" \
                                  f"🏠 إزالة من الجروب: {'نجحت' if removal_result['group_removed'] else 'فشلت'}"

                    self.send_notification(self.config.ADMIN_ID, admin_message, "admin_notification")

                except Exception as e:
                    logger.error(f"Failed to handle expired subscription for user {user_id}: {e}")

                    # في حالة الفشل، محاولة الإزالة التقليدية
                    try:
                        self.bot.ban_chat_member(self.config.GROUP_ID, user_id)
                        logger.info(f"Fallback: Removed expired user {user_id} from group")
                    except Exception as fallback_error:
                        logger.error(f"Fallback removal also failed for user {user_id}: {fallback_error}")

                # إلغاء تفعيل المشترك في قاعدة البيانات
                self.db.deactivate_subscriber(user_id)

                # تسجيل النشاط
                self.db.log_activity(user_id, "subscription_expired", f"Removed from group due to expired subscription")

            logger.info(f"Processed {len(expired_subscribers)} expired subscriptions")

        except Exception as e:
            logger.error(f"Failed to check expired subscriptions: {e}")

    def send_daily_report(self):
        """إرسال تقرير يومي للإدارة"""
        try:
            stats = self.db.get_statistics()
            if not stats:
                return

            report = f"📊 التقرير اليومي - {datetime.now().strftime('%Y-%m-%d')}\n\n" \
                    f"👥 إجمالي المشتركين: {stats['total_subscribers']}\n" \
                    f"✅ المشتركين النشطين: {stats['active_subscribers']}\n" \
                    f"🆕 مشتركين جدد اليوم: {stats['today_new_subscribers']}\n" \
                    f"💰 مدفوعات اليوم: {stats['today_payments']}\n" \
                    f"💵 إجمالي الإيرادات: {stats['total_revenue']:.2f} جنيه\n\n" \
                    f"📈 معدل النمو: {((stats['today_new_subscribers'] / max(stats['total_subscribers'], 1)) * 100):.1f}%"

            self.send_notification(self.config.ADMIN_ID, report, "daily_report")
            logger.info("Daily report sent to admin")

        except Exception as e:
            logger.error(f"Failed to send daily report: {e}")

    def cleanup_database(self):
        """تنظيف قاعدة البيانات من البيانات القديمة"""
        try:
            with self.db.get_db_cursor() as cursor:
                # حذف التنبيهات القديمة (أكثر من 30 يوم)
                cursor.execute(f"""
                    DELETE FROM {self.db.notifications_table}
                    WHERE sent_at < NOW() - INTERVAL '30 days'
                """)

                # حذف سجلات النشاط القديمة (أكثر من 90 يوم)
                cursor.execute(f"""
                    DELETE FROM {self.db.activity_logs_table}
                    WHERE created_at < NOW() - INTERVAL '90 days'
                """)

                # حذف المدفوعات المرفوضة القديمة (أكثر من 60 يوم)
                cursor.execute(f"""
                    DELETE FROM {self.db.payments_table}
                    WHERE status = 'rejected' AND created_at < NOW() - INTERVAL '60 days'
                """)

                logger.info("Database cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup database: {e}")

    def send_notification(self, user_id, message, notification_type="general"):
        """إرسال تنبيه للمستخدم - التنبيهات مُفعلة تلقائ<|im_start|> للجميع"""
        try:
            # التنبيهات مُفعلة تلقائ<|im_start|> لجميع المستخدمين - لا يمكن إيقافها

            # إضافة أزرار تفاعلية للتنبيهات
            markup = None
            if notification_type == 'expiry_warning':
                # للمشتركين النشطين الذين قارب اشتراكهم على الانتهاء - يحق لهم التواصل
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("💳 تجديد الاشتراك", callback_data="payment_methods"),
                    types.InlineKeyboardButton("📞 تواصل مع الدعم", callback_data="contact_support")
                )
                markup.add(types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status"))
            elif notification_type == 'subscription_expired':
                # للمشتركين المنتهية صلاحيتهم - لا يحق لهم التواصل
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("💳 تجديد الاشتراك", callback_data="payment_methods"),
                    types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status")
                )
            elif notification_type == "welcome":
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("🔗 دخول الجروب", callback_data="group_link"),
                    types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status")
                )
            elif notification_type == "payment_confirmed":
                markup = types.InlineKeyboardMarkup()
                markup.add(
                    types.InlineKeyboardButton("🔗 دخول الجروب", callback_data="group_link"),
                    types.InlineKeyboardButton("📄 حالة اشتراكي", callback_data="my_status")
                )

            # إرسال الرسالة مع إضافة تنويه أن التنبيهات مُفعلة تلقائ<|im_start|>
            if notification_type in ['expiry_warning', 'subscription_expired']:
                message += f"\n\n🔔 **ملاحظة:** التنبيهات مُفعلة تلقائ<|im_start|> لضمان عدم فوات أي معلومة مهمة"

            self.bot.send_message(user_id, message, reply_markup=markup)

            # تسجيل النشاط
            self.db.log_activity(user_id, f"notification_sent_{notification_type}", message[:100])

            return True

        except Exception as e:
            logger.error(f"Failed to send notification to {user_id}: {e}")
            return False

    def create_expiry_warning_message(self, days_left, emoji):
        """إنشاء رسالة تنبيه انتهاء الاشتراك"""
        return f"{emoji} تنبيه تلقائي - انتهاء الاشتراك\n\n" \
               f"⏰ سينتهي اشتراكك خلال {days_left} {'يوم' if days_left == 1 else 'أيام'}\n\n" \
               f"💳 لتجديد اشتراكك:\n" \
               f"1️⃣ اضغط على 'تجديد الاشتراك' أدناه\n" \
               f"2️⃣ اتبع تعليمات الدفع\n" \
               f"3️⃣ أرسل إثبات الدفع\n\n" \
               f"💰 سعر التجديد: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
               f"🔔 هذا تنبيه تلقائي لضمان عدم انقطاع خدمتك\n" \
               f"📞 للمساعدة: راسل الإدارة"

    def create_subscription_expired_message(self):
        """إنشاء رسالة انتهاء الاشتراك"""
        return f"🚫 تنبيه تلقائي - انتهى اشتراكك\n\n" \
               f"📛 تم إنهاء اشتراكك وإزالتك من الجروب\n" \
               f"💡 يمكنك تجديد الاشتراك في أي وقت\n\n" \
               f"🔄 لتجديد الاشتراك:\n" \
               f"1️⃣ اضغط على 'تجديد الاشتراك' أدناه\n" \
               f"2️⃣ أكمل عملية الدفع\n" \
               f"3️⃣ ستتم إعادة إضافتك فور الموافقة\n\n" \
               f"💰 سعر الاشتراك: {self.config.SUBSCRIPTION_PRICE} جنيه\n" \
               f"🔔 هذا تنبيه تلقائي لمساعدتك في التجديد\n" \
               f"📞 للاستفسار: راسل الإدارة"

    def send_welcome_message(self, user_id, username=None):
        """إرسال رسالة ترحيب للمشترك الجديد"""
        # تجاهل المتغير غير المستخدم
        _ = username
        welcome_msg = f"🎉 مرحباً بك في العضوية المميزة!\n\n" \
                     f"✅ تم تفعيل اشتراكك بنجاح\n" \
                     f"⏰ مدة الاشتراك: {self.config.SUBSCRIPTION_DURATION_DAYS} يوم\n" \
                     f"🔔 التنبيهات مُفعلة تلقائ<|im_start|> - ستصلك تنبيهات قبل انتهاء الاشتراك بـ {self.config.WARNING_DAYS_BEFORE_EXPIRY} أيام\n" \
                     f"📱 لن تفوتك أي معلومة مهمة!\n\n" \
                     f"🎯 استمتع بالمحتوى الحصري!\n" \
                     f"📞 للدعم: راسل الإدارة في أي وقت\n\n" \
                     f"💡 **ملاحظة:** التنبيهات مُفعلة دائماً لضمان عدم فوات موعد التجديد"

        self.send_notification(user_id, welcome_msg, "welcome")

    def shutdown(self):
        """إيقاف نظام التنبيهات"""
        try:
            self.scheduler.shutdown()
            logger.info("Notification system shutdown completed")
        except Exception as e:
            logger.error(f"Failed to shutdown notification system: {e}")
